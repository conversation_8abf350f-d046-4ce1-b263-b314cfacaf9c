<template>
  <div class="line2-tour-app">
    <!-- 顶部标题栏 -->
    <div class="header">
      <div class="header-content">
        <h1>轨道游重庆-2号线</h1>
        <div class="line-badge">
          <span class="line-number">2</span>
          <span class="line-name">号线</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 线路概览卡片 -->
      <div class="overview-card">
        <div class="overview-header">
          <div class="line-info">
            <div class="line-color-bar"></div>
            <div class="line-details">
              <h2>重庆轨道交通<span style="color:#00a651;">2</span>号线</h2>
              <p>穿越山城的绿色长龙</p>
            </div>
          </div>
          <div class="line-stats">
            <div class="stat-item">
              <span class="stat-number">{{ featuredStations.length }}</span>
              <span class="stat-label">精选站点</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">25</span>
              <span class="stat-label">总站数</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 地铁线路图 -->
      <div class="metro-map-section">
        <div class="map-header">
          <h3>线路图</h3>
          <button class="map-toggle"
                  @click="toggleMapView">
            {{ isMapExpanded ? '收起' : '展开' }}
          </button>
        </div>

        <div class="metro-map"
             :class="{ 'expanded': isMapExpanded }">
          <svg class="line-svg"
               viewBox="0 0 500 600"
               xmlns="http://www.w3.org/2000/svg">
            <!-- 2号线路径 - 根据真实线路图绘制 -->

            <!-- 鱼洞-大江-白居寺连线，大江处有弯曲 -->
            <path d="M 115 555 Q 82 565 50 550 Q 25 520 25 450"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 白居寺到大渡口直线连接 -->
            <path d="M 25 450 L 25 330"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 1. 大渡口连接平安再连接马王场，都有弧度 -->
            <path d="M 25 330 Q 30 320 35 310 Q 45 300 55 290"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 2. 马王场连接大堰村再连接动物园，均是直线 -->
            <path d="M 55 290 L 85 290 L 115 290"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 3. 动物园弧线连接杨家坪 -->
            <path d="M 115 290 Q 130 280 145 265"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 4. 杨家坪直线连接上方三个城市到大坪 -->
            <path d="M 145 265 L 145 165"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 5. 大坪直线连接到牛角沱 -->
            <path d="M 145 165 L 235 75"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 6. 牛角沱连接到曾家岩（开头弧度，然后同y轴高度直线） -->
            <path d="M 235 75 Q 260 50 275 50 L 335 50"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 7. 曾家岩直线连接到黄花园 -->
            <path d="M 335 50 L 395 50"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 8. 黄花园弧度连接到临江口 -->
            <path d="M 395 50 Q 430 60 425 80"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 9. 临江口直线连接到较场口 -->
            <path d="M 425 80 L 425 110"
                  stroke="#00A651"
                  stroke-width="6"
                  fill="none"
                  class="metro-line" />

            <!-- 站点定位 -->
            <!-- 较场口 - 精选站点五角星 -->
            <path d="M 425 102 L 428 108 L 435 108 L 430 113 L 432 120 L 425 116 L 418 120 L 420 113 L 415 108 L 422 108 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('较场口')" />
            <text x="435"
                  y="115"
                  class="station-name"
                  @click="selectStationByName('较场口')">较场口</text>
            <!-- 临江门 - 精选站点五角星 -->
            <path d="M 425 72 L 428 78 L 435 78 L 430 83 L 432 90 L 425 86 L 418 90 L 420 83 L 415 78 L 422 78 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('临江门')" />
            <text x="435"
                  y="85"
                  class="station-name"
                  @click="selectStationByName('临江门')">临江门</text>
            <!-- 黄花园 - 精选站点五角星 -->
            <path d="M 395 42 L 398 48 L 405 48 L 400 53 L 402 60 L 395 56 L 388 60 L 390 53 L 385 48 L 392 48 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('黄花园')" />
            <text x="375"
                  y="75"
                  class="station-name"
                  @click="selectStationByName('黄花园')">黄花园</text>
            <!-- 大溪沟 - 精选站点五角星 -->
            <path d="M 365 42 L 368 48 L 375 48 L 370 53 L 372 60 L 365 56 L 358 60 L 360 53 L 355 48 L 362 48 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('大溪沟')" />
            <text x="345"
                  y="35"
                  class="station-name"
                  @click="selectStationByName('大溪沟')">大溪沟</text>
            <!-- 曾家岩 - 精选站点五角星 -->
            <path d="M 335 42 L 338 48 L 345 48 L 340 53 L 342 60 L 335 56 L 328 60 L 330 53 L 325 48 L 332 48 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('曾家岩')" />
            <text x="315"
                  y="75"
                  class="station-name"
                  @click="selectStationByName('曾家岩')">曾家岩</text>
            <!-- 牛角沱 - 精选站点五角星 -->
            <path d="M 235 67 L 238 73 L 245 73 L 240 78 L 242 85 L 235 81 L 228 85 L 230 78 L 225 73 L 232 73 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('牛角沱')" />
            <text x="195"
                  y="65"
                  class="station-name"
                  @click="selectStationByName('牛角沱')">牛角沱</text>
            <!-- 李子坝 - 精选站点五角星 -->
            <path d="M 205 97 L 208 103 L 215 103 L 210 108 L 212 115 L 205 111 L 198 115 L 200 108 L 195 103 L 202 103 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('李子坝')" />
            <text x="165"
                  y="95"
                  class="station-name">李子坝</text>
            <!-- 佛图关 - 精选站点五角星 -->
            <path d="M 175 127 L 178 133 L 185 133 L 180 138 L 182 145 L 175 141 L 168 145 L 170 138 L 165 133 L 172 133 Z"
                  fill="#FFD700"
                  stroke="white"
                  stroke-width="1"
                  class="station-star featured"
                  @click="selectStationByName('佛图关')" />
            <text x="135"
                  y="125"
                  class="station-name">佛图关</text>
            <circle cx="145"
                    cy="165"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('大坪')" />
            <text x="155"
                  y="170"
                  class="station-name"
                  @click="selectStationByName('大坪')">大坪</text>
            <circle cx="145"
                    cy="200"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('袁家岗')" />
            <text x="155"
                  y="205"
                  class="station-name"
                  @click="selectStationByName('袁家岗')">袁家岗</text>
            <circle cx="145"
                    cy="240"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('谢家湾')" />
            <text x="155"
                  y="245"
                  class="station-name"
                  @click="selectStationByName('谢家湾')">谢家湾</text>
            <circle cx="145"
                    cy="265"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('杨家坪')" />
            <text x="155"
                  y="270"
                  class="station-name"
                  @click="selectStationByName('杨家坪')">杨家坪</text>
            <circle cx="115"
                    cy="290"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('动物园')" />
            <text x="95"
                  y="310"
                  class="station-name"
                  @click="selectStationByName('动物园')">动物园</text>
            <circle cx="85"
                    cy="290"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('大堰村')" />
            <text x="65"
                  y="275"
                  class="station-name"
                  @click="selectStationByName('大堰村')">大堰村</text>
            <circle cx="55"
                    cy="290"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('马王场')" />
            <text x="50"
                  y="310"
                  class="station-name"
                  @click="selectStationByName('马王场')">马王场</text>
            <circle cx="35"
                    cy="310"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('平安')" />
            <text x="0"
                  y="315"
                  class="station-name"
                  @click="selectStationByName('平安')">平安</text>
            <circle cx="25"
                    cy="330"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('大渡口')" />
            <text x="35"
                  y="335"
                  class="station-name"
                  @click="selectStationByName('大渡口')">大渡口</text>
            <circle cx="25"
                    cy="350"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('新山村')" />
            <text x="35"
                  y="355"
                  class="station-name"
                  @click="selectStationByName('新山村')">新山村</text>
            <circle cx="25"
                    cy="370"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('天堂堡')" />
            <text x="35"
                  y="375"
                  class="station-name"
                  @click="selectStationByName('天堂堡')">天堂堡</text>
            <circle cx="25"
                    cy="390"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('建桥')" />
            <text x="35"
                  y="395"
                  class="station-name"
                  @click="selectStationByName('建桥')">建桥</text>

            <circle cx="25"
                    cy="410"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('金家湾')" />
            <text x="35"
                  y="415"
                  class="station-name"
                  @click="selectStationByName('金家湾')">金家湾</text>

            <circle cx="25"
                    cy="430"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('刘家坝')" />
            <text x="35"
                  y="435"
                  class="station-name"
                  @click="selectStationByName('刘家坝')">刘家坝</text>

            <circle cx="25"
                    cy="450"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('白居寺')" />
            <text x="35"
                  y="455"
                  class="station-name"
                  @click="selectStationByName('白居寺')">白居寺</text>

            <circle cx="50"
                    cy="550"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('大江')" />
            <text x="35"
                  y="575"
                  class="station-name"
                  @click="selectStationByName('大江')">大江</text>

            <circle cx="115"
                    cy="555"
                    r="6"
                    fill="#00A651"
                    stroke="white"
                    stroke-width="2"
                    class="station-circle"
                    @click="selectStationByName('鱼洞')" />
            <text x="100"
                  y="575"
                  class="station-name"
                  @click="selectStationByName('鱼洞')">鱼洞</text>
          </svg>
        </div>
      </div>

      <!-- 线路信息 -->
      <div class="line-info-section">
        <div class="info-card">
          <h3>线路信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">运营时间</span>
              <span class="info-value">6:00 - 23:00</span>
            </div>
            <div class="info-item">
              <span class="info-label">全程时间</span>
              <span class="info-value">约60分钟</span>
            </div>
            <div class="info-item">
              <span class="info-label">票价</span>
              <span class="info-value">2-7元</span>
            </div>
            <div class="info-item">
              <span class="info-label">车辆类型</span>
              <span class="info-value">跨座式单轨</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 站点详情弹窗 -->
    <div v-if="selectedStation"
         class="station-modal"
         @click="closeStationDetail">
      <div class="modal-content"
           @click.stop>
        <div class="modal-header">
          <h3>{{ selectedStation.name }}</h3>
          <button class="close-btn"
                  @click="closeStationDetail">×</button>
        </div>

        <div class="modal-body">
          <div class="station-image"
               v-if="selectedStation.image">
            <img :src="selectedStation.image"
                 :alt="selectedStation.name" />
          </div>

          <div class="station-details">
            <!-- 站点基本信息 -->
            <div class="station-basic-info">
              <div class="station-type-badge"
                   :class="{ 'transfer': selectedStation.isTransfer, 'featured': selectedStation.isFeatured }">
                <span v-if="selectedStation.isTransfer">换乘站</span>
                <span v-else-if="selectedStation.isFeatured">精选站点</span>
                <span v-else>普通站</span>
              </div>
              <div class="station-highlight"
                   v-if="selectedStation.highlight">
                {{ selectedStation.highlight }}
              </div>
            </div>

            <p class="station-intro">{{ selectedStation.detailedDescription }}</p>

            <!-- 特色标签 -->
            <div class="modal-features"
                 v-if="selectedStation.features?.length">
              <h4>站点特色</h4>
              <div class="feature-tags">
                <span v-for="feature in selectedStation.features"
                      :key="feature"
                      class="feature-tag">
                  {{ feature }}
                </span>
              </div>
            </div>

            <!-- 周边景点 -->
            <div class="modal-attractions"
                 v-if="selectedStation.attractions?.length">
              <h4>周边景点/设施</h4>
              <div class="attraction-list">
                <div v-for="attraction in selectedStation.attractions"
                     :key="attraction.name"
                     class="attraction-item"
                     @click="openAttractionModal(attraction)">
                  <div class="attraction-image">
                    <!-- 微信环境使用Canvas渲染 -->
                    <canvas v-if="attraction.image && isWechat()"
                            :ref="el => setCanvasRef(`thumbnail-${attraction.name}`, el)"
                            class="attraction-thumbnail protected-image canvas-image"
                            @contextmenu.prevent
                            @dragstart.prevent
                            @selectstart.prevent></canvas>
                    <!-- 非微信环境使用普通img -->
                    <img v-else-if="attraction.image"
                         :src="attraction.image"
                         :alt="attraction.name"
                         class="attraction-thumbnail protected-image"
                         @contextmenu.prevent
                         @dragstart.prevent
                         @selectstart.prevent />
                    <!-- 缩略图版权标识 (非微信环境) -->
                    <div class="thumbnail-copyright"
                         v-if="attraction.image && !isWechat()">©</div>
                    <!-- 微信防护遮罩 -->
                    <div class="wechat-protection-overlay"
                         v-if="attraction.image && isWechat()"></div>
                    <!-- 没有图片时显示占位符 -->
                    <div v-if="!attraction.image"
                         class="no-image-placeholder">
                      <div class="no-image-icon">🖼️</div>
                      <div class="no-image-text">暂无图片</div>
                    </div>
                  </div>
                  <div class="attraction-info">
                    <div class="attraction-header">
                      <span class="attraction-name">{{ attraction.name }}</span>
                      <span class="attraction-distance">{{ attraction.distance }}</span>
                    </div>
                    <p class="attraction-description"
                       v-if="attraction.description">{{ attraction.description }}</p>
                  </div>
                  <div class="attraction-arrow">
                    <svg width="16"
                         height="16"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2">
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <div class="detail-sections">
              <div class="detail-section"
                   v-if="selectedStation.history">
                <h4>历史背景</h4>
                <p>{{ selectedStation.history }}</p>
              </div>

              <div class="detail-section"
                   v-if="selectedStation.culture">
                <h4>文化特色</h4>
                <p>{{ selectedStation.culture }}</p>
              </div>

              <div class="detail-section"
                   v-if="selectedStation.tips">
                <h4>游览贴士</h4>
                <p>{{ selectedStation.tips }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 景点详细弹窗 -->
    <div v-if="isAttractionModalOpen"
         class="modal-overlay attraction-modal-overlay"
         @click="closeAttractionModal">
      <div class="modal-content attraction-modal-content"
           @click.stop>
        <div class="modal-header">
          <h3>{{ selectedAttraction?.name }}</h3>
          <button class="modal-close"
                  @click="closeAttractionModal">
            <svg width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2">
              <path d="m18 6-12 12" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>

        <div class="modal-body attraction-modal-body">
          <!-- 景点图片轮播 -->
          <div class="attraction-gallery"
               v-if="selectedAttraction?.image">
            <div class="gallery-main">
              <!-- 微信环境使用Canvas渲染 -->
              <div v-if="isWechat()"
                   class="canvas-container"
                   @click="!isMobile() ? handleCanvasContainerClick : undefined"
                   @touchend="!isMobile() ? handleCanvasContainerTouchEnd : undefined">
                <canvas :ref="el => setCanvasRef('detail-image', el)"
                        class="gallery-main-image protected-image canvas-image"
                        :style="imageTransformStyle"
                        @contextmenu.prevent
                        @dragstart.prevent
                        @selectstart.prevent
                        @wheel="handleImageWheel"
                        @mousedown="startImageDrag"
                        @mousemove="handleImageDrag"
                        @mouseup="endImageDrag"
                        @mouseleave="endImageDrag"
                        @touchstart="startImageTouch"
                        @touchmove="handleImageTouch"></canvas>
                <!-- Canvas点击覆盖层 (仅桌面端) -->
                <!-- 全屏查看覆盖层 - 所有设备都可用 -->
                <div class="canvas-click-overlay"
                     @click="openFullscreenView"
                     @touchend="openFullscreenView">
                  <!-- 全屏查看提示 -->

                </div>
              </div>
              <!-- 非微信环境使用普通img -->
              <img v-else
                   :src="currentImage"
                   :alt="selectedAttraction.name"
                   class="gallery-main-image protected-image"
                   :style="imageTransformStyle"
                   @contextmenu.prevent
                   @dragstart.prevent
                   @selectstart.prevent
                   @click="openFullscreenView"
                   @wheel="handleImageWheel"
                   @mousedown="startImageDrag"
                   @mousemove="handleImageDrag"
                   @mouseup="endImageDrag"
                   @mouseleave="endImageDrag"
                   @touchstart="startImageTouch"
                   @touchmove="handleImageTouch"
                   @touchend="endImageTouch" />
              <!-- 景点详情图片水印 (非微信环境) -->
              <div class="detail-image-watermark"
                   v-if="!isWechat()">
                © 版权保护
              </div>
              <!-- 微信防护遮罩 -->
              <div class="wechat-protection-overlay"
                   v-if="isWechat()"></div>
              <!-- 放大提示 (所有设备) -->
              <div class="zoom-hint"
                   v-if="!isImageZoomed">
                <svg width="20"
                     height="20"
                     viewBox="0 0 24 24"
                     fill="none"
                     stroke="currentColor"
                     stroke-width="2">
                  <circle cx="11"
                          cy="11"
                          r="8" />
                  <path d="m21 21-4.35-4.35" />
                  <line x1="11"
                        y1="8"
                        x2="11"
                        y2="14" />
                  <line x1="8"
                        y1="11"
                        x2="14"
                        y2="11" />
                </svg>
                点击全屏查看
              </div>
              <!-- 重置缩放按钮 -->
              <div class="zoom-reset"
                   v-if="isImageZoomed"
                   @click.stop="resetImageZoom">
                <svg width="20"
                     height="20"
                     viewBox="0 0 24 24"
                     fill="none"
                     stroke="currentColor"
                     stroke-width="2">
                  <circle cx="11"
                          cy="11"
                          r="8" />
                  <path d="m21 21-4.35-4.35" />
                  <line x1="8"
                        y1="11"
                        x2="14"
                        y2="11" />
                </svg>
                重置
              </div>
              <!-- 图片导航 -->
              <div class="gallery-nav"
                   v-if="selectedAttraction?.images && selectedAttraction.images.length > 1 && !isImageZoomed">
                <button class="nav-btn prev-btn"
                        @click.stop.prevent="prevImage"
                        @touchend.stop.prevent="prevImage">‹</button>
                <button class="nav-btn next-btn"
                        @click.stop.prevent="nextImage"
                        @touchend.stop.prevent="nextImage">›</button>
              </div>
            </div>
            <!-- 图片指示器 -->
            <div class="gallery-indicators"
                 v-if="selectedAttraction?.images && selectedAttraction.images.length > 1">
              <span v-for="(img, index) in selectedAttraction.images"
                    :key="index"
                    :class="['indicator', { active: index === currentImageIndex }]"
                    @click="setCurrentImage(index)"></span>
            </div>
          </div>

          <!-- 景点信息 -->
          <div class="attraction-details">
            <div class="attraction-basic-info">
              <div class="attraction-distance-badge">
                距离站点 {{ selectedAttraction?.distance }}
              </div>
            </div>

            <p class="attraction-intro"
               v-if="selectedAttraction?.description">
              {{ selectedAttraction.description }}
            </p>

            <div class="attraction-detail-content"
                 v-if="selectedAttraction?.details">
              <h4>详细介绍</h4>
              <p>{{ selectedAttraction.details }}</p>
            </div>

            <!-- 实用信息 -->
            <div class="attraction-practical-info">
              <h4>实用信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">步行时间</span>
                  <span class="info-value">约{{ Math.ceil(parseInt(selectedAttraction?.distance || '0') / 80) }}分钟</span>
                </div>
                <div class="info-item">
                  <span class="info-label">建议游览</span>
                  <span class="info-value">30-60分钟</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏图片查看器 -->
    <div v-if="isFullscreenView"
         class="fullscreen-viewer"
         @click="closeFullscreenView">
      <div class="fullscreen-content">
        <!-- 关闭按钮 -->
        <button class="fullscreen-close"
                @click="closeFullscreenView">
          <svg width="24"
               height="24"
               viewBox="0 0 24 24"
               fill="none"
               stroke="currentColor"
               stroke-width="2">
            <path d="m18 6-12 12" />
            <path d="m6 6 12 12" />
          </svg>
        </button>

        <!-- 图片容器 -->
        <div class="fullscreen-image-container"
             @click.stop>
          <!-- 微信环境使用Canvas渲染 -->
          <canvas v-if="isWechat()"
                  :ref="el => setCanvasRef('fullscreen-image', el)"
                  class="fullscreen-image protected-image canvas-image"
                  :style="fullscreenImageStyle"
                  @contextmenu.prevent
                  @dragstart.prevent
                  @selectstart.prevent
                  @wheel="isMobile() ? null : handleFullscreenWheel"
                  @mousedown="isMobile() ? null : startFullscreenDrag"
                  @mousemove="isMobile() ? null : handleFullscreenDrag"
                  @mouseup="isMobile() ? null : endFullscreenDrag"
                  @mouseleave="isMobile() ? null : endFullscreenDrag"
                  @touchstart="isMobile() ? null : startImageTouch"
                  @touchmove="isMobile() ? null : handleImageTouch"
                  @touchend="isMobile() ? null : endImageTouch"></canvas>
          <!-- 非微信环境使用普通img -->
          <img v-else
               :src="currentImage"
               :alt="selectedAttraction?.name"
               class="fullscreen-image protected-image"
               :style="fullscreenImageStyle"
               @contextmenu.prevent
               @dragstart.prevent
               @selectstart.prevent
               @wheel="isMobile() ? null : handleFullscreenWheel"
               @mousedown="isMobile() ? null : startFullscreenDrag"
               @mousemove="isMobile() ? null : handleFullscreenDrag"
               @mouseup="isMobile() ? null : endFullscreenDrag"
               @mouseleave="isMobile() ? null : endFullscreenDrag"
               @touchstart="isMobile() ? null : startImageTouch"
               @touchmove="isMobile() ? null : handleImageTouch"
               @touchend="isMobile() ? null : endImageTouch" />

          <!-- 版权保护水印 (非微信环境) -->
          <div class="fullscreen-watermark"
               v-if="!isWechat()"
               :style="watermarkStyle">
            🚫 版权保护 - 禁止转载
          </div>

          <!-- 微信防护遮罩 -->
          <div class="wechat-fullscreen-protection"
               v-if="isWechat()"></div>

          <!-- 缩放控制 (仅桌面端) -->
          <div class="fullscreen-controls"
               v-if="!isMobile()">
            <button class="control-btn"
                    @click="zoomOut"
                    :disabled="imageScale <= 0.5">
              <svg width="20"
                   height="20"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2">
                <circle cx="11"
                        cy="11"
                        r="8" />
                <path d="m21 21-4.35-4.35" />
                <line x1="8"
                      y1="11"
                      x2="14"
                      y2="11" />
              </svg>
            </button>
            <span class="zoom-level">{{ Math.round(imageScale * 100) }}%</span>
            <button class="control-btn"
                    @click="zoomIn"
                    :disabled="imageScale >= 4">
              <svg width="20"
                   height="20"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2">
                <circle cx="11"
                        cy="11"
                        r="8" />
                <path d="m21 21-4.35-4.35" />
                <line x1="11"
                      y1="8"
                      x2="11"
                      y2="14" />
                <line x1="8"
                      y1="11"
                      x2="14"
                      y2="11" />
              </svg>
            </button>
            <button class="control-btn"
                    @click="resetFullscreenZoom">
              <svg width="20"
                   height="20"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                <path d="M21 3v5h-5" />
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                <path d="M3 21v-5h5" />
              </svg>
            </button>
          </div>

          <!-- 图片导航 -->
          <div class="fullscreen-nav"
               v-if="selectedAttraction?.images && selectedAttraction.images.length > 1">
            <button class="fullscreen-nav-btn prev"
                    @click="prevImage">
              <svg width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2">
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>
            <button class="fullscreen-nav-btn next"
                    @click="nextImage">
              <svg width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </button>
          </div>

          <!-- 图片指示器 -->
          <div class="fullscreen-indicators"
               v-if="selectedAttraction?.images && selectedAttraction.images.length > 1">
            <span v-for="(img, index) in selectedAttraction.images"
                  :key="index"
                  :class="['fullscreen-indicator', { active: index === currentImageIndex }]"
                  @click="setCurrentImage(index)"></span>
          </div>
        </div>

        <!-- 版权保护提示 -->
        <div class="protection-notice"
             v-if="showProtectionNotice">
          <div class="notice-content">
            <div class="notice-icon">🛡️</div>
            <div class="notice-text">
              <h3>版权保护已启用</h3>
              <p>此图片受版权保护，已禁用截屏和保存功能</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { temp } from 'three/examples/jsm/nodes/Nodes.js'
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

// 站点接口定义
interface Station {
  id: string
  name: string
  order: number
  isTransfer: boolean
  isFeatured: boolean
  description: string
  detailedDescription: string
  highlight?: string
  features?: string[]
  attractions?: Array<{
    name: string
    distance: string
    image?: string
    description?: string
    details?: string
    images?: string[]
  }>
  history?: string
  culture?: string
  tips?: string
  image?: string
}

// 响应式数据
const isMapExpanded = ref(false)
const selectedStationId = ref<string | null>(null)
const selectedStation = ref<Station | null>(null)
const selectedAttraction = ref<any | null>(null)
const isAttractionModalOpen = ref(false)
const currentImageIndex = ref(0)
const currentImage = ref('')
const canvasRefs = ref<{ [key: string]: HTMLCanvasElement }>({})
const isImageZoomed = ref(false)
const isFullscreenView = ref(false)
const showProtectionNotice = ref(false)
const imageScale = ref(1)
const imageTranslateX = ref(0)
const imageTranslateY = ref(0)

// 页面水印管理
let pageWatermark: HTMLElement | null = null

// 重庆轨道交通2号线精选站点数据
const featuredStations = ref<Station[]>([
  {
    id: 'jiaochangkou',
    name: '较场口',
    order: 1,
    isTransfer: true,
    isFeatured: true,
    description: '重庆母城的历史起点，古城门遗址所在地',
    detailedDescription:
      '较场口是重庆最具历史底蕴的地区之一，这里曾是古重庆城的南门，承载着山城千年的历史记忆。',
    highlight: '历史文化',
    features: ['历史古迹', '文化遗址', '换乘枢纽'],
    attractions: [
      {
        name: '凯旋路电梯',
        distance: '352m',
        image: '/static/images/cq_2/较场口-凯旋路电梯.jpg',
        description: '重庆最长的户外电梯，连接上下半城',
        details:
          '凯旋路电梯全长112米，是重庆最长的户外电梯之一，连接了较场口的上下半城，是体验重庆立体交通的绝佳地点。',
      },
      {
        name: '湖广会馆',
        distance: '2000m',
        image: '/static/images/cq_2/湖广会馆.png',
        description: '明清古建筑群，重庆历史文化名片',
        details:
          '湖广会馆是重庆现存最大的古建筑群，始建于清朝，展现了重庆移民文化的历史变迁。',
      },
      {
        name: '东水门大桥',
        distance: '2000m',
        image: '/static/images/cq_2/东水门长江大桥.png',
        description: '横跨长江的现代化大桥',
        details:
          '东水门大桥是重庆的标志性建筑之一，夜景尤为壮观，是拍摄重庆夜景的最佳地点之一。',
      },
      {
        name: '较场口夜市',
        distance: '300m',
        image: '',
        description: '重庆传统夜市，品尝地道小吃',
        details:
          '较场口夜市汇聚了重庆各种传统小吃，是体验重庆夜生活和美食文化的绝佳场所。',
      },
    ],
    history: '较场口得名于明清时期的军事操练场，是重庆古城的重要组成部分。',
    culture: '这里保存着重庆传统的巴渝文化，是了解山城历史的重要窗口。',
    tips: '建议傍晚时分前往，可以欣赏到美丽的江景和夜景。',
  },
  {
    id: 'linjiangmen',
    name: '临江门',
    order: 2,
    isTransfer: false,
    isFeatured: true,
    description: '解放碑商圈核心，重庆最繁华的商业中心',
    detailedDescription:
      '临江门站位于解放碑商圈核心区域，是重庆最繁华的商业和金融中心，汇聚了众多购物中心和美食。',
    highlight: '商业中心',
    features: ['购物天堂', '美食聚集', '金融中心'],
    attractions: [
      {
        name: '解放碑',
        distance: '800m',
        image: '/static/images/cq_2/临江门-解放碑.jpg',
        description: '重庆地标建筑，抗战胜利纪功碑',
        details:
          '解放碑全称"重庆人民解放纪念碑"，原名"抗战胜利纪功碑"，是全中国唯一的一座纪念中华民族抗日战争胜利的国家纪念碑。碑高27.5米，有旋梯可达顶端。解放碑位于重庆主城渝中区商业区中心部位，是重庆的标志建筑物。以解放碑为中心的十字路口，包括民权路、民族路和邹容路，是重庆最繁华的商业圈。周边汇聚了重庆百货、新世纪百货、太平洋百货等大型购物中心，是购物、美食、娱乐的天堂。',
        images: ['/static/images/cq_2/临江门-解放碑.jpg'],
      },
      {
        name: '洪崖洞',
        distance: '1.2km',
        image: '/static/images/cq_2/临江门-洪崖洞.jpg',
        description: '巴渝传统建筑特色的吊脚楼群',
        details:
          '洪崖洞是重庆历史文化的见证和重庆城市精神的象征，以巴渝传统建筑特色的"吊脚楼"风貌为主体，依山就势，沿崖而建。洪崖洞由纸盐河酒吧街、天成巷巴渝风情街、盛宴美食街及异域风情城市阳台四部分组成。建筑面积4.6万平方米，主要景点由吊脚楼、仿古商业街等景观组成。夜晚灯火辉煌，金碧辉煌，被誉为"现实版千与千寻"，是重庆夜景的代表之一。这里汇聚了重庆各种特色小吃和手工艺品，是体验巴渝文化的绝佳场所。',
        images: [
          '/static/images/cq_2/临江门-洪崖洞.jpg',
          '/static/images/cq_2/洪崖洞2.jpg',
        ],
      },
      {
        name: '朝天门广场',
        distance: '2km',
        image: '/static/images/cq_2/朝天门1.jpg',
        description: '两江汇流的壮观景象',
        details:
          '朝天门位于重庆渝中区渝中半岛的嘉陵江与长江交汇处，是重庆以前的十七门之一。南宋时期宋朝定都临安，即今天的杭州，那时有圣旨传来是经长江到达朝天门，所以才有了朝天门这个名字。朝天门是两江枢纽，也是重庆最大的水路客运码头。站在朝天门广场上，可以看到两江汇流的壮观景象，嘉陵江水呈绿色，长江水呈黄色，两江汇合后的长江水呈混黄色。朝天门广场是观看重庆夜景的最佳地点之一，也是重庆标志性的旅游景点。',
        images: [
          '/static/images/cq_2/朝天门1.jpg',
          '/static/images/cq_2/朝天门2.jpg',
        ],
      },
      {
        name: '云端之眼',
        distance: '2km',
        image: '/static/images/cq_2/临江门-云端之眼.jpg',
        description: '重庆最高观景台，360度俯瞰山城全貌',
        details:
          '云端之眼位于重庆来福士广场顶层，是重庆最高的观景台之一，海拔约300米。这里提供360度无死角的重庆全景视野，可以俯瞰两江四岸的壮丽景色。观景台采用全透明玻璃设计，给游客带来悬空的刺激体验。白天可以清晰看到重庆的山水城市格局，夜晚则能欣赏到璀璨的重庆夜景。这里是拍摄重庆城市大片的绝佳位置，也是情侣约会和游客打卡的热门地点。',
        images: ['/static/images/cq_2/临江门-云端之眼.jpg'],
      },
    ],
    history: '临江门是重庆开埠后最早的商业区，见证了山城的现代化发展历程。',
    culture: '这里融合了传统巴渝文化和现代都市文明，是重庆文化的缩影。',
    tips: '周末人流较多，建议工作日前往购物和用餐。',
  },
  {
    id: 'huanghuayuan',
    name: '黄花园',
    order: 3,
    isTransfer: false,
    isFeatured: true,
    description: '嘉陵江畔的宁静站点，连接两江四岸',
    detailedDescription:
      '黄花园站紧邻嘉陵江，是观赏重庆山水城市风貌的绝佳位置，这里有着独特的江景和桥梁景观。',
    highlight: '江景观光',
    features: ['江景观光', '桥梁景观', '休闲漫步'],
    attractions: [
      {
        name: '水上列车',
        distance: '500m',
        image: '/static/images/cq_2/黄花园-水上列车.jpg',
        description: '重庆轻轨2号线跨江段，体验水上行驶的奇妙感觉',
        details:
          '重庆轻轨2号线在黄花园至大溪沟段跨越嘉陵江，被称为"水上列车"。这段轨道建在嘉陵江上，全长约1.2公里，是重庆独特的城市景观之一。乘坐轻轨经过这段时，可以近距离欣赏嘉陵江的美丽风光，感受在江面上行驶的奇妙体验。两岸的山城景色尽收眼底，是体验重庆"山水之城"魅力的绝佳方式。这里也是摄影爱好者的天堂，可以拍摄到轻轨与江景完美融合的画面。',
        images: ['/static/images/cq_2/黄花园-水上列车.jpg'],
      },
      {
        name: '黄花园大桥',
        distance: '300m',
        image: '',
        description: '横跨嘉陵江的现代化大桥',
        details:
          '黄花园大桥是连接重庆江北区和渝中区的重要桥梁，全长1022米，主跨长度为250米。大桥采用双塔双索面斜拉桥结构，造型优美，是重庆的标志性建筑之一。桥上可以欣赏到嘉陵江两岸的美丽风光，是拍摄重庆城市景观的绝佳位置。夜晚时分，大桥灯光璀璨，与江面倒影相映成趣，构成了重庆夜景的重要组成部分。',
      },
      {
        name: '嘉陵江滨江路',
        distance: '500m',
        image: '',
        description: '江边休闲步道，亲水观景胜地',
        details:
          '嘉陵江滨江路是重庆重要的滨江景观带，沿江而建，全长数公里。这里绿树成荫，环境优美，是市民休闲健身的好去处。滨江路上设有多个观景平台，可以近距离欣赏嘉陵江的美丽风光。路边还有咖啡厅、茶楼等休闲场所，是约会聊天的理想地点。清晨和傍晚时分，这里聚集了众多晨练和散步的市民，充满了生活气息。',
      },
      {
        name: '重庆科技馆',
        distance: '2.5km',
        image: '',
        description: '现代化科普教育基地',
        details:
          '重庆科技馆是重庆市重要的科普教育基地，建筑面积4.5万平方米，展示面积3万平方米。馆内设有生活科技、防灾科技、交通科技、国防科技等多个主题展厅，通过互动体验的方式普及科学知识。科技馆采用现代化的展示手段，集科学性、知识性、趣味性于一体，是青少年科普教育的重要场所。馆内还定期举办各种科普活动和临时展览，是家庭亲子游的热门选择。',
      },
    ],
    history: '黄花园因历史上此地遍植黄花而得名，是重庆江北区的重要节点。',
    culture: '这里体现了重庆"山水之城"的独特魅力，是摄影爱好者的天堂。',
    tips: '最佳观景时间是日落时分，可以拍摄到美丽的江景和桥梁剪影。',
  },
  {
    id: 'daxigou',
    name: '大溪沟',
    order: 4,
    isTransfer: false,
    isFeatured: true,
    description: '文化艺术聚集地，重庆的文艺心脏',
    detailedDescription:
      '大溪沟是重庆重要的文化艺术区域，这里聚集了众多文化场馆和艺术机构，是重庆文化生活的重要组成部分。',
    highlight: '文化艺术',
    features: ['文化场馆', '艺术展览', '创意产业'],
    attractions: [
      {
        name: 'S湾',
        distance: '500m',
        image: '/static/images/cq_2/大溪沟.jpg',
        description: '嘉陵江S型弯道，重庆最美江湾景观',
        details:
          'S湾是嘉陵江在大溪沟段形成的天然S型弯道，是重庆最具特色的江湾景观之一。从高处俯瞰，嘉陵江在这里画出了一个完美的S型曲线，两岸绿树成荫，江水清澈。这里是重庆"山水之城"的完美体现，也是摄影师们钟爱的拍摄地点。沿江步道设施完善，是市民晨练和休闲的好去处。夜晚时分，两岸灯火倒映在江面上，形成美丽的光影效果。',
        images: ['/static/images/cq_2/大溪沟.jpg'],
      },
      {
        name: '重庆美术馆',
        distance: '600m',
        image: '',
        description: '重庆市重要的艺术展览场所',
        details:
          '重庆美术馆是重庆市重要的艺术展览和文化交流场所，建筑面积约1.5万平方米。馆内设有多个展厅，常年举办各类艺术展览，包括绘画、雕塑、摄影、书法等多种艺术形式。美术馆不仅展示本土艺术家的作品，也引进国内外优秀的艺术展览。这里是重庆文化艺术的重要窗口，也是市民接受艺术熏陶的重要场所。建筑设计现代简约，内部空间宽敞明亮，为艺术品展示提供了良好的环境。',
      },
      {
        name: '重庆图书馆',
        distance: '800m',
        image: '',
        description: '重庆市最大的综合性图书馆',
        details:
          '重庆图书馆是重庆市最大的综合性图书馆，建筑面积8.3万平方米，藏书量超过400万册。图书馆设有阅览室、电子阅览室、少儿阅览室、古籍阅览室等多个功能区域。这里不仅是市民阅读学习的重要场所，也是重庆重要的文献信息中心。图书馆建筑设计现代化，内部环境优雅安静，配备了先进的数字化设备。定期举办各种文化讲座和读书活动，是重庆文化生活的重要组成部分。',
      },
      {
        name: '文化创意园',
        distance: '700m',
        image: '',
        description: '重庆文化创意产业聚集地',
        details:
          '大溪沟文化创意园是重庆重要的文化创意产业聚集地，汇聚了众多设计公司、艺术工作室、文化机构等。园区内有画廊、咖啡厅、书店、手工艺品店等，营造了浓厚的文化艺术氛围。这里经常举办艺术展览、文化沙龙、创意市集等活动，是年轻人和文艺爱好者的聚集地。园区建筑多为改造的老厂房，保留了工业遗产的特色，与现代文化创意完美融合。',
      },
    ],
    history: '大溪沟历史上是重庆的文教区，培养了众多文化名人。',
    culture: '这里是重庆现代文化的发源地，承载着城市的文化记忆。',
    tips: '周末经常有文化活动和艺术展览，建议提前查看活动安排。',
  },
  {
    id: 'zengjiayan',
    name: '曾家岩',
    order: 5,
    isTransfer: false,
    isFeatured: true,
    description: '红色文化地标，抗战历史见证地',
    detailedDescription:
      '曾家岩是重庆重要的红色文化地标，这里保存着丰富的抗战历史遗迹，是了解重庆抗战文化的重要场所。',
    highlight: '红色文化',
    features: ['红色旅游', '历史教育', '爱国主义'],
    attractions: [
      {
        name: '重庆人民大礼堂',
        distance: '500m',
        image: '/static/images/cq_2/曾家岩-大礼堂jpg.jpg',
        description: '重庆标志性建筑，仿古宫殿式建筑群',
        details:
          '重庆人民大礼堂是重庆市的标志性建筑之一，建于1951-1954年，是一座仿古民族建筑群。大礼堂采用明清宫殿建筑风格，主体建筑为四层，顶部为重檐攒尖式屋顶，气势恢宏。大礼堂可容纳4200人，是重庆重要的政治文化活动场所。建筑群包括大礼堂主体、南楼、北楼，整体布局对称，体现了中国传统建筑的美学特色。这里经常举办重要会议、文艺演出等活动，是重庆政治文化生活的重要场所。',
        images: ['/static/images/cq_2/曾家岩-大礼堂jpg.jpg'],
      },
      {
        name: '中山四路',
        distance: '500m',
        image: '/static/images/cq_2/曾家岩-中山四路.jpg',
        description: '重庆最美街道，民国建筑风情街',
        details:
          '中山四路被誉为重庆最美的街道之一，全长约800米，两旁梧桐成荫，保存着大量民国时期的建筑。这条路曾是国民政府时期的政治中心，沿街有桂园、周公馆、戴公馆等历史建筑。街道两旁的法国梧桐形成绿色隧道，四季景色各异，春夏绿意盎然，秋季金黄满树。这里是重庆历史文化的重要载体，也是市民休闲散步的好去处。许多影视作品都在这里取景，是重庆文艺青年的打卡圣地。',
        images: ['/static/images/cq_2/曾家岩-中山四路.jpg'],
      },
      {
        name: '重庆中国三峡博物馆',
        distance: '1.5km',
        image: '/static/images/cq_2/曾家岩-三峡博物馆pg.jpg',
        description: '展示三峡文化和重庆历史的国家级博物馆',
        details:
          '重庆中国三峡博物馆是国家一级博物馆，建筑面积4.25万平方米，是展示和研究三峡文化的重要场所。博物馆设有《壮丽三峡》、《远古巴渝》、《重庆·城市之路》等基本陈列，全面展示了三峡地区的自然风光、历史文化和重庆的城市发展历程。馆藏文物17万余件套，其中珍贵文物4万余件套。博物馆建筑设计现代化，与重庆人民大礼堂形成"古今对话"的建筑景观。这里是了解重庆历史文化和三峡文明的重要窗口。',
        images: ['/static/images/cq_2/曾家岩-三峡博物馆pg.jpg'],
      },
    ],
    history: '曾家岩在抗战时期是重要的政治活动中心，见证了重庆的抗战历史。',
    culture: '这里承载着深厚的红色文化底蕴，是进行爱国主义教育的重要基地。',
    tips: '建议安排半天时间深度游览，可以更好地了解抗战历史。',
  },
  {
    id: 'niujiaotuo',
    name: '牛角沱',
    order: 6,
    isTransfer: true,
    isFeatured: true,
    description: '重要交通枢纽，连接江北与渝中',
    detailedDescription:
      '牛角沱是重庆重要的交通枢纽站，连接着江北区和渝中区，是重庆轨道交通网络的重要节点。',
    highlight: '交通枢纽',
    features: ['换乘枢纽', '交通便利', '商业配套'],
    attractions: [
      {
        name: '纱帽石',
        distance: '1.5km',
        image: '/static/images/cq_2/纱帽石.png',
        description: '嘉陵江中的天然石岛，重庆独特地质景观',
        details:
          '纱帽石是位于嘉陵江中的一座天然石岛，因形似古代官员的纱帽而得名。这座石岛面积约2000平方米，高出江面约10米，是重庆独特的地质景观。石岛上绿树成荫，建有亭台楼阁，是观赏嘉陵江风光的绝佳位置。从岛上可以360度欣赏两岸的山城景色，特别是夜晚时分，两岸灯火辉煌，倒映在江面上，景色格外迷人。纱帽石也是重庆历史文化的见证，历代文人墨客都曾在此留下诗词佳作。',
        images: ['/static/images/cq_2/纱帽石.png'],
      },
      {
        name: '怡园',
        distance: '1.5km',
        image: '/static/images/cq_2/牛角沱-纱帽石，怡园.jpg',
        description: '江边休闲公园，市民健身娱乐好去处',
        details:
          '怡园是位于嘉陵江边的一座综合性公园，占地面积约15公顷，是市民休闲健身的重要场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区等多个功能区域。园内还有观江亭台，可以欣赏嘉陵江的美丽风光。公园的设计充分利用了江边的自然地形，形成了高低错落的景观层次。这里是附近居民晨练、散步、休闲的好去处，也是家庭亲子活动的理想场所。',
        images: ['/static/images/cq_2/牛角沱-纱帽石，怡园.jpg'],
      },
    ],
    history: '牛角沱因地形似牛角而得名，是重庆传统的渡口和交通要道。',
    culture: '这里体现了重庆作为交通枢纽城市的重要地位。',
    tips: '换乘时注意指示标识，高峰期人流较大。',
  },
  {
    id: 'liziba',
    name: '李子坝',
    order: 7,
    isTransfer: false,
    isFeatured: true,
    description: '网红打卡地，轻轨穿楼奇观',
    detailedDescription:
      '李子坝站因轻轨穿越居民楼而闻名全国，成为重庆最具特色的网红打卡地，展现了山城独特的立体交通魅力。',
    highlight: '网红景点',
    features: ['网红打卡', '建筑奇观', '摄影圣地'],
    attractions: [
      {
        name: '轻轨穿楼',
        distance: '50m',
        image: '/static/images/cq_2/李子坝-轻轨穿楼.jpg',
        description: '世界唯一的轻轨穿楼奇观',
        details:
          '李子坝轻轨穿楼是重庆独有的城市景观，重庆轨道交通2号线从李子坝站穿过一栋19层的居民楼，这在全世界都是独一无二的。这栋楼建于2004年，轻轨在6-8层穿过，由于采用了减震降噪技术，对居民生活影响很小。这里已成为重庆最著名的网红打卡地，每天都有大量游客前来拍照留念。最佳拍摄时间是上午10点到下午4点，可以拍到轻轨穿楼的完整画面。',
        images: ['/static/images/cq_2/李子坝-轻轨穿楼.jpg'],
      },
      {
        name: '李子坝抗战遗址公园',
        distance: '800m',
        image: '/static/images/cq_2/李子坝抗战遗址.jpg',
        description: '抗战时期重要历史遗址',
        details:
          '李子坝抗战遗址公园是重庆抗战文化的重要载体，园内保存了抗战时期的重要历史建筑和文物。公园内有抗战时期的防空洞、指挥所等历史遗迹，还有详细的抗战历史展览。这里曾是国民政府军事委员会政治部第三厅所在地，郭沫若、老舍、冰心等文化名人都曾在此工作过。公园环境优美，既是历史教育基地，也是市民休闲的好去处。',
        images: [
          '/static/images/cq_2/李子坝抗战遗址.jpg',
          '/static/images/cq_2/李子坝抗战遗址展览.jpg',
        ],
      },
      {
        name: '鹅岭公园',
        distance: '1.8km',
        image: '/static/images/cq_2/鹅岭公园.jpg',
        description: '重庆主城最高点，俯瞰全城',
        details:
          '鹅岭公园位于重庆市渝中区，海拔345米，是重庆主城区的最高点，也是观赏重庆夜景的最佳地点之一。公园内有瞰胜楼，登楼可360度俯瞰重庆全景，长江、嘉陵江尽收眼底。公园始建于1909年，原名鹅项岭，因其形似鹅颈项而得名。园内古树参天，环境清幽，是重庆市民休闲健身的好去处。夜晚时分，这里是观赏重庆璀璨夜景的绝佳位置。',
        images: [
          '/static/images/cq_2/鹅岭公园.jpg',
          '/static/images/cq_2/鹅岭公园夜景.jpg',
        ],
      },
    ],
    history:
      '李子坝站的设计体现了重庆"立体城市"的建设理念，是城市规划的创新典范。',
    culture: '这里代表了重庆人敢于创新、因地制宜的城市精神。',
    tips: '最佳拍摄时间是上午10点到下午4点，光线较好。观景台位于站台对面。',
  },
  {
    id: 'fotuguan',
    name: '佛图关',
    order: 8,
    isTransfer: false,
    isFeatured: true,
    description: '古关隘遗址，山城制高点',
    detailedDescription:
      '佛图关是重庆古代的重要关隘，位于山城制高点，这里有着深厚的历史文化底蕴和绝佳的城市景观。',
    highlight: '历史古迹',
    features: ['古关隘', '城市观景', '历史文化'],
    attractions: [
      {
        name: '开往春天的地铁',
        distance: '200m',
        image: '/static/images/cq_2/佛图关-开往春天的地铁.jpg',
        description: '重庆网红打卡地，轻轨穿越花海的浪漫景象',
        details:
          '"开往春天的地铁"是重庆轻轨2号线佛图关段的网红景观，每年春季，轻轨沿线开满了粉色的樱花和黄色的油菜花，形成了"轻轨穿花海"的浪漫景象。这里成为了重庆最受欢迎的拍照打卡地之一，吸引了无数游客和摄影爱好者前来。最佳观赏时间是每年3-4月，此时花开正盛，轻轨从花海中穿过，如同开往春天的列车。这里不仅是拍照圣地，也体现了重庆"山水之城、美丽之地"的城市魅力。',
        images: ['/static/images/cq_2/佛图关-开往春天的地铁.jpg'],
      },
      {
        name: '佛图关公园',
        distance: '200m',
        image: '',
        description: '重庆主城制高点之一，俯瞰城市全景',
        details:
          '佛图关公园位于重庆主城的制高点之一，海拔约400米，是观赏重庆全景的绝佳位置。公园内绿树成荫，环境清幽，设有多个观景台，可以360度俯瞰重庆的山水城市风貌。从这里可以看到长江、嘉陵江的交汇，以及重庆主城的高楼大厦。公园内还保留着古代关隘的遗迹，体现了重庆深厚的历史文化底蕴。这里是市民登高望远、休闲健身的好去处，也是摄影爱好者拍摄重庆全景的理想地点。',
      },
      {
        name: '佛图关遗址',
        distance: '300m',
        image: '',
        description: '古代军事关隘遗址，重庆历史文化见证',
        details:
          '佛图关遗址是重庆古代重要的军事关隘遗址，始建于宋代，历经元、明、清各朝代的修建和使用。关隘地处重庆主城制高点，地势险要，是古代重庆城防体系的重要组成部分。遗址现存城墙、城门、炮台等建筑遗迹，虽经历史沧桑，但仍能看出当年的雄伟气势。这里是了解重庆古代军事文化和城市发展历史的重要场所，也是重庆市文物保护单位。遗址周围环境优美，是历史文化爱好者探访的好去处。',
      },
    ],
    history: '佛图关始建于宋代，是重庆古代军事防御体系的重要组成部分。',
    culture: '这里承载着重庆深厚的历史文化，是了解山城古代文明的重要窗口。',
    tips: '公园内有观景台，可以俯瞰重庆全景，建议选择晴朗天气前往。',
  },
])

// 所有站点数据（包括精选站点和其他站点）
const allStations = ref([
  ...featuredStations.value,
  // 其他主要站点
  {
    id: 'daping',
    name: '大坪',
    order: 9,
    isTransfer: true,
    isFeatured: false,
    description: '重要的交通枢纽和商业区',
    detailedDescription:
      '大坪站是2号线的重要分叉点，连接主线和支线，周边商业发达，是九龙坡区的重要交通枢纽。',
    highlight: '交通枢纽',
    features: ['交通枢纽', '商业区', '分叉点'],
    attractions: [
      {
        name: '大坪时代天街',
        distance: '400m',
        image: '/static/images/cq_2/大坪时代天街.jpg',
        description: '重庆知名购物中心，时尚潮流聚集地',
        details:
          '大坪时代天街是重庆知名的大型购物中心，汇聚了众多国际国内知名品牌。商场内设有电影院、餐饮、娱乐等多种业态，是年轻人购物休闲的热门去处。建筑设计现代时尚，内部装修精美，购物环境舒适。这里经常举办各种时尚活动和品牌发布会，是重庆时尚潮流的风向标。',
        images: [
          '/static/images/cq_2/大坪时代天街.jpg',
          '/static/images/cq_2/大坪时代天街内景.jpg',
        ],
      },
      {
        name: '重庆天地',
        distance: '1.2km',
        image: '/static/images/cq_2/重庆天地.jpg',
        description: '高端商业综合体，国际化生活方式',
        details:
          '重庆天地是重庆高端商业综合体的代表，由国际知名开发商打造。项目融合了购物、餐饮、娱乐、办公、居住等多种功能，体现了国际化的生活方式。这里汇聚了众多国际奢侈品牌和高端餐饮，是重庆上流社会的聚集地。建筑风格现代简约，环境优雅，服务品质一流。',
        images: [
          '/static/images/cq_2/重庆天地.jpg',
          '/static/images/cq_2/重庆天地夜景.jpg',
        ],
      },
      {
        name: '大坪医院',
        distance: '600m',
        image: '/static/images/cq_2/大坪医院.jpg',
        description: '重庆知名三甲医院，医疗服务中心',
        details:
          '重庆大坪医院是中国人民解放军陆军军医大学第三附属医院，是一所集医疗、教学、科研为一体的大型综合性三级甲等医院。医院技术力量雄厚，医疗设备先进，在心血管、神经外科、烧伤科等专业领域享有盛誉。医院环境优美，服务优质，是重庆市民信赖的医疗机构。',
        images: [
          '/static/images/cq_2/大坪医院.jpg',
          '/static/images/cq_2/大坪医院内景.jpg',
        ],
      },
    ],
    history: '大坪地区历史悠久，是重庆传统的商贸集散地。',
    culture: '现代商业与传统文化的完美融合。',
    tips: '这里是换乘的重要节点，建议预留充足的换乘时间。',
  },
  {
    id: 'yuanjigang',
    name: '袁家岗',
    order: 10,
    isTransfer: false,
    isFeatured: false,
    description: '医疗卫生中心区域',
    detailedDescription:
      '袁家岗站周边聚集了多家大型医院，是重庆重要的医疗卫生服务中心。',
    features: ['医疗中心', '居住区'],
    attractions: [
      {
        name: '重医附一院',
        distance: '400m',
        image: '',
        description: '重庆医科大学附属第一医院，西南地区知名三甲医院',
        details:
          '重庆医科大学附属第一医院是西南地区知名的三级甲等综合医院，始建于1957年。医院技术力量雄厚，医疗设备先进，在心血管、神经外科、肿瘤、器官移植等专业领域享有盛誉。医院占地面积约200亩，建筑面积30余万平方米，开放床位3200余张。这里不仅是重要的医疗服务中心，也是医学教育和科研基地，为重庆及西南地区的医疗卫生事业做出了重要贡献。',
      },
      {
        name: '重庆医科大学',
        distance: '600m',
        image: '',
        description: '重庆市重点医科大学，培养医学人才的摇篮',
        details:
          '重庆医科大学是重庆市重点建设的医科大学，创建于1956年，是国家"中西部高校基础能力建设工程"重点建设高校。学校设有临床医学、基础医学、药学、护理学等多个学院，拥有完善的医学教育体系。校园环境优美，教学设施先进，图书馆藏书丰富。学校培养了大批优秀的医学人才，为重庆乃至全国的医疗卫生事业发展做出了重要贡献。',
      },
      {
        name: '袁家岗公园',
        distance: '300m',
        image: '',
        description: '社区休闲公园，市民健身娱乐场所',
        details:
          '袁家岗公园是一座综合性社区公园，占地面积约8公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区等多个功能区域。园内还有小型人工湖，湖水清澈，环境优美。公园的设计充分考虑了不同年龄段市民的需求，是附近居民晨练、散步、休闲的重要场所，也体现了重庆宜居城市的建设成果。',
      },
    ],
    history: '袁家岗因清代袁姓大户聚居而得名。',
    culture: '医学教育和医疗服务的重要基地。',
    tips: '周边医院较多，交通相对繁忙，建议避开就诊高峰期。',
  },
  {
    id: 'xiejiawan',
    name: '谢家湾',
    order: 11,
    isTransfer: false,
    isFeatured: false,
    description: '居住区和教育区',
    detailedDescription:
      '谢家湾站位于成熟的居住区，周边有多所学校，是重要的教育和居住区域。',
    features: ['教育区', '居住区'],
    attractions: [
      {
        name: '谢家湾小学',
        distance: '200m',
        image: '',
        description: '重庆知名小学，基础教育典范',
        details:
          '谢家湾小学是重庆市知名的小学，创建于1957年，是重庆市基础教育的典范学校。学校占地面积约3万平方米，建筑面积2万余平方米，环境优美，设施先进。学校以"红梅花儿开，朵朵放光彩"为办学理念，注重学生的全面发展。多年来，学校在教育教学、素质教育等方面取得了显著成绩，培养了大批优秀学生，在重庆教育界享有很高声誉。',
      },
      {
        name: '华润万象城',
        distance: '800m',
        image: '',
        description: '大型购物中心，时尚消费新地标',
        details:
          '华润万象城是重庆九龙坡区的大型购物中心，总建筑面积约15万平方米，是集购物、餐饮、娱乐、休闲于一体的现代化商业综合体。商场内汇聚了众多国际国内知名品牌，设有电影院、儿童乐园、美食广场等多种业态。建筑设计现代时尚，内部装修精美，购物环境舒适。这里是周边居民购物消费的重要场所，也是年轻人休闲娱乐的热门去处。',
      },
      {
        name: '彩云湖公园',
        distance: '1km',
        image: '',
        description: '重庆最大的人工湖公园，生态休闲胜地',
        details:
          '彩云湖公园是重庆最大的人工湖公园，占地面积约1100亩，其中湖面面积约600亩。公园以生态环保为主题，湖水清澈，环境优美，是重庆重要的生态休闲场所。园内设有环湖步道、观景台、儿童游乐区等多个功能区域，还有丰富的水生植物和鸟类。公园的建设体现了重庆"山水之城"的生态理念，是市民休闲健身、亲近自然的好去处，也是重庆生态文明建设的重要成果。',
      },
    ],
    history: '谢家湾因谢姓人家聚居而得名，历史悠久。',
    culture: '重庆基础教育的重要区域。',
    tips: '周边学校较多，上下学时间人流量大。',
  },
  {
    id: 'yangjiaping',
    name: '杨家坪',
    order: 12,
    isTransfer: false,
    isFeatured: false,
    description: '九龙坡区政治经济中心',
    detailedDescription:
      '杨家坪是九龙坡区的政治、经济、文化中心，商业繁荣，交通便利。',
    features: ['区政府', '商业中心', '文化中心'],
    attractions: [
      {
        name: '杨家坪步行街',
        distance: '600m',
        image: '',
        description: '九龙坡区商业中心，购物休闲一条街',
        details:
          '杨家坪步行街是九龙坡区的商业中心，全长约800米，是重庆西部重要的商业步行街。街道两旁商铺林立，汇聚了服装、餐饮、娱乐等多种业态，是当地居民购物消费的主要场所。步行街环境整洁，设施完善，经常举办各种商业活动和文化表演。这里不仅是购物的好去处，也是体验重庆本土商业文化的重要场所，展现了重庆西部地区的商业活力。',
      },
      {
        name: '九龙坡区政府',
        distance: '800m',
        image: '',
        description: '九龙坡区行政中心，政务服务中心',
        details:
          '九龙坡区政府是九龙坡区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。周边配套设施齐全，交通便利。作为九龙坡区的政治中心，这里承担着重要的行政管理职能，是推动九龙坡区经济社会发展的重要机构。',
      },
      {
        name: '重庆动物园',
        distance: '1.2km',
        image: '',
        description: '西南地区重要动物园，家庭亲子游胜地',
        details:
          '重庆动物园建于1954年，是西南地区重要的动物园之一，占地面积约45公顷。园内饲养着大熊猫、金丝猴、华南虎等珍稀动物200多种、3000余只。动物园环境优美，设施完善，设有熊猫馆、猛兽区、鸟语林等多个展区。这里不仅是重要的动物保护和科研基地，也是市民休闲娱乐和青少年科普教育的重要场所，是重庆家庭亲子游的热门选择。',
      },
    ],
    history: '杨家坪因杨姓人家聚居的平坝而得名。',
    culture: '九龙坡区的政治文化中心。',
    tips: '商业区人流量大，建议选择合适时间出行。',
  },
  {
    id: 'dongwuyuan',
    name: '动物园',
    order: 13,
    isTransfer: false,
    isFeatured: false,
    description: '重庆动物园所在地',
    detailedDescription:
      '动物园站因重庆动物园而得名，是市民休闲娱乐的重要场所。',
    features: ['动物园', '休闲娱乐'],
    attractions: [
      {
        name: '重庆动物园',
        distance: '300m',
        image: '',
        description: '西南地区最大动物园，珍稀动物的家园',
        details:
          '重庆动物园是西南地区最大的动物园，建于1954年，占地面积约45公顷，是国家AAAA级旅游景区。园内饲养着来自世界各地的珍稀动物200多种、3000余只，其中包括大熊猫、金丝猴、华南虎、亚洲象等国家一级保护动物。动物园分为猛兽区、灵长类区、鸟语林、水禽湖等多个展区，每个区域都有其独特的特色。园内环境优美，绿树成荫，是集动物保护、科学研究、科普教育、休闲娱乐于一体的综合性动物园。',
      },
      {
        name: '杨家坪商圈',
        distance: '1.2km',
        image: '',
        description: '九龙坡区核心商业区，购物娱乐中心',
        details:
          '杨家坪商圈是九龙坡区的核心商业区，以杨家坪步行街为中心，辐射周边多个商业综合体。商圈内汇聚了百货商场、专卖店、餐饮店、娱乐场所等各类商业设施，是重庆西部重要的购物娱乐中心。这里交通便利，人流量大，商业氛围浓厚，不仅满足了周边居民的日常消费需求，也吸引了众多外地游客前来购物。商圈的发展体现了重庆西部地区的经济活力和商业繁荣。',
      },
    ],
    history: '重庆动物园建于1954年，是西南地区重要的动物园。',
    culture: '重庆市民休闲娱乐的重要场所。',
    tips: '适合家庭出游，建议预留半天时间游览动物园。',
  },
  {
    id: 'dayancun',
    name: '大堰村',
    order: 14,
    isTransfer: false,
    isFeatured: false,
    description: '传统居住区',
    detailedDescription: '大堰村站位于传统的居住区域，保持着重庆老城区的特色。',
    features: ['居住区', '传统社区'],
    attractions: [
      {
        name: '大堰村社区',
        distance: '100m',
        image: '',
        description: '重庆传统社区，体验本土生活文化',
        details:
          '大堰村社区是重庆典型的传统社区，保持着浓厚的重庆本土生活气息。社区内有传统的重庆民居建筑，街道狭窄但充满生活气息。这里有传统的小商铺、茶馆、面馆等，是体验重庆市井文化的好地方。社区居民热情好客，保持着重庆人特有的豪爽性格。虽然是老社区，但基础设施不断完善，体现了重庆城市更新和社区改造的成果。',
      },
      {
        name: '九龙坡公园',
        distance: '600m',
        image: '',
        description: '区域性综合公园，市民休闲健身场所',
        details:
          '九龙坡公园是九龙坡区重要的综合性公园，占地面积约20公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了重庆宜居城市的建设理念。',
      },
    ],
    history: '大堰村因古代的大堰而得名，历史悠久。',
    culture: '保持着重庆传统社区的文化特色。',
    tips: '体验重庆传统社区生活的好地方。',
  },
  // 继续添加其他站点
  {
    id: 'mawangchang',
    name: '马王场',
    order: 15,
    isTransfer: false,
    isFeatured: false,
    description: '传统居住区和商业区',
    detailedDescription:
      '马王场站位于传统的居住和商业区域，是当地居民生活的重要交通节点。',
    features: ['居住区', '商业区'],
    attractions: [
      {
        name: '马王场市场',
        distance: '200m',
        image: '',
        description: '传统农贸市场，体验重庆市井生活',
        details:
          '马王场市场是重庆传统的农贸市场，历史悠久，是当地居民购买生鲜食品的主要场所。市场内商品丰富，有新鲜蔬菜、水果、肉类、水产等各类农副产品。这里保持着重庆传统市场的特色，商贩热情，价格实惠，是体验重庆市井生活的好地方。市场虽然规模不大，但商品齐全，服务周边多个社区的居民，体现了重庆传统商贸文化的延续。',
      },
      {
        name: '九龙坡体育馆',
        distance: '800m',
        image: '',
        description: '区级体育场馆，全民健身中心',
        details:
          '九龙坡体育馆是九龙坡区重要的体育场馆，建筑面积约8000平方米，可容纳3000余人。体育馆设施完善，可举办篮球、排球、羽毛球等多种体育比赛和活动。这里不仅是重要的体育比赛场所，也是市民健身锻炼的重要场所，定期开放供市民使用。体育馆经常举办各种体育赛事和全民健身活动，是推动九龙坡区体育事业发展的重要载体。',
      },
    ],
    history: '马王场因古代马市而得名，历史悠久。',
    culture: '重庆传统商贸文化的体现。',
    tips: '周边有传统市场，可以体验重庆本地生活。',
  },
  {
    id: 'pingan',
    name: '平安',
    order: 16,
    isTransfer: false,
    isFeatured: false,
    description: '居住区和工业区',
    detailedDescription:
      '平安站周边以居住区和轻工业为主，是重庆西部重要的居住区域。',
    features: ['居住区', '工业区'],
    attractions: [
      {
        name: '平安公园',
        distance: '300m',
        image: '',
        description: '社区公园，居民休闲健身场所',
        details:
          '平安公园是一座社区性公园，占地面积约5公顷，为周边居民提供了良好的休闲健身场所。公园内绿化良好，设有健身步道、儿童游乐设施、老年活动区等多个功能区域。园内还有小型广场，是居民进行广场舞、太极拳等健身活动的场所。公园虽然规模不大，但设施齐全，环境优美，是附近居民日常休闲的重要场所，体现了重庆社区公园建设的成果。',
      },
      {
        name: '九龙工业园',
        distance: '1km',
        image: '',
        description: '现代化工业园区，重庆西部工业基地',
        details:
          '九龙工业园是重庆西部重要的工业园区，占地面积约10平方公里，是九龙坡区重要的经济增长点。园区内汇聚了机械制造、电子信息、新材料等多个产业，形成了完整的产业链条。园区基础设施完善，交通便利，为入驻企业提供了良好的发展环境。这里是重庆制造业发展的重要载体，也是推动九龙坡区经济转型升级的重要平台。',
      },
    ],
    history: '平安地区寓意平安吉祥，是重庆西部发展的重要区域。',
    culture: '现代工业与居住的和谐发展。',
    tips: '适合了解重庆现代工业发展。',
  },
  {
    id: 'dadukou',
    name: '大渡口',
    order: 17,
    isTransfer: false,
    isFeatured: false,
    description: '大渡口区政府所在地',
    detailedDescription:
      '大渡口站是大渡口区的政治经济中心，历史上是重要的工业基地。',
    features: ['区政府', '工业基地', '历史文化'],
    attractions: [
      {
        name: '大渡口区政府',
        distance: '200m',
        image: '',
        description: '大渡口区行政中心，政务服务中心',
        details:
          '大渡口区政府是大渡口区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。作为大渡口区的政治中心，这里承担着重要的行政管理职能，是推动大渡口区经济社会发展的重要机构。周边配套设施齐全，交通便利，体现了现代化政务服务的理念。',
      },
      {
        name: '重钢博物馆',
        distance: '500m',
        image: '',
        description: '重庆钢铁工业历史博物馆，工业文化遗产',
        details:
          '重钢博物馆是展示重庆钢铁工业发展历史的专题博物馆，建在原重庆钢铁厂旧址上。博物馆保留了大量的工业设备和历史文物，全面展示了重庆钢铁工业从无到有、从小到大的发展历程。这里不仅是重庆工业文化的重要载体，也是进行爱国主义教育和工业文明教育的重要基地。博物馆的建设体现了对工业遗产的保护和利用，是重庆城市转型发展的重要见证。',
      },
      {
        name: '义渡公园',
        distance: '600m',
        image: '',
        description: '长江边综合性公园，历史文化主题公园',
        details:
          '义渡公园是位于长江边的综合性公园，占地面积约20公顷，以"义渡"历史文化为主题。公园内设有义渡文化广场、历史文化展示区、滨江步道等多个功能区域。园内绿化良好，环境优美，可以欣赏长江美景。公园不仅是市民休闲健身的好去处，也是了解大渡口历史文化的重要场所。这里承载着大渡口"义渡"文化的历史记忆，体现了重庆深厚的历史文化底蕴。',
      },
    ],
    history: '大渡口因长江古渡口而得名，是重庆重要的工业基地。',
    culture: '重庆近现代工业文化的重要载体。',
    tips: '可以参观重钢博物馆，了解重庆工业发展历史。',
  },
  {
    id: 'baijusi',
    name: '白居寺',
    order: 18,
    isTransfer: false,
    isFeatured: false,
    description: '历史文化区域',
    detailedDescription:
      '白居寺站因古代白居寺而得名，承载着深厚的历史文化底蕴。',
    features: ['历史文化', '宗教文化'],
    attractions: [
      {
        name: '白居寺遗址',
        distance: '300m',
        image: '',
        description: '唐代古寺遗址，重庆佛教文化遗产',
        details:
          '白居寺遗址是重庆重要的佛教文化遗产，始建于唐代，历史悠久。虽然古寺建筑已不复存在，但遗址仍保留着重要的历史价值。这里曾是重庆地区重要的佛教活动场所，承载着深厚的宗教文化底蕴。遗址周围环境清幽，是了解重庆古代佛教文化的重要场所。现在这里建有纪念性建筑，供人们缅怀历史，感受佛教文化的博大精深。',
      },
      {
        name: '大渡口公园',
        distance: '400m',
        image: '',
        description: '区域性综合公园，市民休闲娱乐场所',
        details:
          '大渡口公园是大渡口区重要的综合性公园，占地面积约15公顷，为市民提供了良好的休闲娱乐场所。公园内绿化良好，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了大渡口区宜居城市的建设成果。',
      },
    ],
    history: '白居寺始建于唐代，是重庆重要的历史文化遗址。',
    culture: '佛教文化与巴渝文化的融合。',
    tips: '适合了解重庆古代宗教文化。',
  },
  {
    id: 'dajiang',
    name: '大江',
    order: 19,
    isTransfer: false,
    isFeatured: false,
    description: '长江沿岸居住区',
    detailedDescription:
      '大江站位于长江沿岸，是重要的居住区域，享有优美的江景。',
    features: ['江景', '居住区'],
    attractions: [
      {
        name: '长江滨江路',
        distance: '200m',
        image: '',
        description: '长江沿岸景观大道，亲水休闲步道',
        details:
          '长江滨江路是沿长江而建的景观大道，全长数公里，是重庆重要的滨江景观带。这里绿树成荫，环境优美，设有多个观景平台和休息区，是市民休闲健身的好去处。滨江路上可以近距离欣赏长江的壮丽风光，感受"一江春水向东流"的磅礴气势。路边还有咖啡厅、茶楼等休闲场所，是约会聊天的理想地点。清晨和傍晚时分，这里聚集了众多晨练和散步的市民，充满了生活气息。',
      },
      {
        name: '大江公园',
        distance: '300m',
        image: '',
        description: '江边主题公园，长江文化展示地',
        details:
          '大江公园是以长江文化为主题的综合性公园，占地面积约10公顷，紧邻长江，地理位置优越。公园内设有长江文化展示区、滨江步道、观景台等多个功能区域。园内绿化良好，花草繁茂，可以欣赏到长江的美丽风光。公园不仅是市民休闲娱乐的场所，也是了解长江文化的重要窗口。这里经常举办各种文化活动，是传承和弘扬长江文化的重要载体。',
      },
    ],
    history: '大江地区因临近长江而得名，历史悠久。',
    culture: '长江文化的重要体现。',
    tips: '可以沿江散步，欣赏长江美景。',
  },
  {
    id: 'yudong',
    name: '鱼洞',
    order: 20,
    isTransfer: false,
    isFeatured: false,
    description: '2号线南端终点站',
    detailedDescription:
      '鱼洞站是2号线的南端终点站，是巴南区的重要交通枢纽和商业中心。',
    features: ['终点站', '商业中心', '交通枢纽'],
    attractions: [
      {
        name: '鱼洞老街',
        distance: '800m',
        image: '',
        description: '巴南历史文化街区，传统巴渝建筑群',
        details:
          '鱼洞老街是巴南区重要的历史文化街区，保存着大量传统的巴渝建筑和历史遗迹。老街全长约500米，街道两旁是典型的川东民居建筑，青砖黛瓦，古朴典雅。这里曾是重要的商贸集散地，承载着丰富的历史文化内涵。老街内有传统的茶馆、小吃店、手工艺品店等，是体验巴南传统文化的重要场所。近年来经过保护性开发，既保持了历史风貌，又融入了现代元素。',
      },
      {
        name: '巴南区政府',
        distance: '1.5km',
        image: '',
        description: '巴南区行政中心，政务服务中心',
        details:
          '巴南区政府是巴南区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。作为巴南区的政治中心，这里承担着重要的行政管理职能，是推动巴南区经济社会发展的重要机构。周边配套设施齐全，交通便利，体现了现代化政务服务的理念。',
      },
      {
        name: '鱼洞长江大桥',
        distance: '2km',
        image: '',
        description: '跨越长江的现代化大桥，连接两岸交通',
        details:
          '鱼洞长江大桥是连接巴南区与主城区的重要桥梁，全长约1500米，是重庆南部地区重要的交通枢纽。大桥采用现代化的桥梁设计，造型优美，是重庆桥梁建设的重要成果。从桥上可以欣赏到长江两岸的美丽风光，是观赏长江景色的好地方。大桥的建成极大地改善了巴南区的交通条件，促进了区域经济的发展，是重庆城市建设的重要标志。',
      },
    ],
    history: '鱼洞因古代此地多鱼洞而得名，是巴南区的政治经济中心。',
    culture: '巴南区政治经济文化中心。',
    tips: '作为终点站，这里是探索巴南区的起点。',
  },
  // 支线站点
  {
    id: 'xinshancun',
    name: '新山村',
    order: 21,
    isTransfer: false,
    isFeatured: false,
    description: '2号线支线终点站',
    detailedDescription: '新山村站是2号线支线的终点站，周边以居住区为主。',
    features: ['支线终点', '居住区'],
    attractions: [
      {
        name: '新山村社区',
        distance: '100m',
        image: '',
        description: '现代化居住社区，宜居生活典范',
        details:
          '新山村社区是重庆现代化居住社区的典型代表，规划合理，环境优美，配套设施完善。社区内绿化率高，有完善的健身设施、儿童游乐区、老年活动中心等公共设施。社区管理规范，服务贴心，为居民提供了良好的居住环境。这里体现了重庆现代社区建设的成果，是宜居重庆建设的重要体现。社区文化活动丰富，邻里关系和谐，展现了现代城市社区的文明风貌。',
      },
      {
        name: '九龙坡公园',
        distance: '600m',
        image: '',
        description: '区域性综合公园，市民休闲健身场所',
        details:
          '九龙坡公园是九龙坡区重要的综合性公园，占地面积约20公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了重庆宜居城市的建设理念。',
      },
    ],
    history: '新山村是重庆城市发展中的新兴居住区。',
    culture: '现代居住社区的典型代表。',
    tips: '体验重庆现代社区生活。',
  },
  // 新增站点：天堂堡到刘家坝段
  {
    id: 'tiantangbao',
    name: '天堂堡',
    order: 22,
    isTransfer: false,
    isFeatured: false,
    description: '大渡口区居住区，传统社区与现代发展的结合',
    detailedDescription:
      '天堂堡站位于大渡口区，是连接新山村和建桥的重要站点，周边以居住区为主，体现了重庆传统社区向现代化发展的转变。',
    highlight: '居住社区',
    features: ['居住区', '社区服务', '传统文化'],
    attractions: [
      {
        name: '天堂堡社区',
        distance: '200m',
        image: '',
        description: '传统居住社区，保持重庆本土生活特色',
        details:
          '天堂堡社区是大渡口区的传统居住社区，保持着浓厚的重庆本土生活气息。社区内有传统的重庆民居建筑，街道虽然不宽但充满生活气息。这里有传统的小商铺、茶馆、面馆等，是体验重庆市井文化的好地方。社区居民热情好客，保持着重庆人特有的豪爽性格。近年来社区基础设施不断完善，在保持传统特色的同时，也融入了现代化的便民设施。',
      },
      {
        name: '大渡口滨江公园',
        distance: '800m',
        image: '',
        description: '长江边休闲公园，观江景的好去处',
        details:
          '大渡口滨江公园沿长江而建，是当地居民休闲娱乐的重要场所。公园内设有观江平台、健身步道、儿童游乐区等设施。从这里可以欣赏到长江的壮丽景色，感受"一江春水向东流"的磅礴气势。公园绿化良好，环境优美，是市民晨练、散步的好去处。夜晚时分，江面波光粼粼，两岸灯火辉煌，景色格外迷人。',
      },
      {
        name: '天堂堡市场',
        distance: '300m',
        image: '',
        description: '传统农贸市场，体验本地生活',
        details:
          '天堂堡市场是当地重要的农贸市场，为周边居民提供新鲜的蔬菜、水果、肉类等生活必需品。市场虽然规模不大，但商品齐全，价格实惠，保持着重庆传统市场的特色。这里是了解当地生活文化、体验重庆市井风情的好地方。市场内商贩热情，充满了浓厚的生活气息。',
      },
    ],
    history: '天堂堡因地势较高，寓意美好而得名，是大渡口区的传统居住区。',
    culture: '保持着重庆传统社区文化，体现了巴渝人民的生活智慧。',
    tips: '适合体验重庆传统社区生活，感受本土文化魅力。',
  },
  {
    id: 'jianqiao',
    name: '建桥',
    order: 23,
    isTransfer: false,
    isFeatured: false,
    description: '工业与居住并重的区域，重庆西南部重要节点',
    detailedDescription:
      '建桥站位于大渡口区南部，是连接天堂堡和金家湾的重要站点，周边既有工业区也有居住区，体现了重庆工业城市的特色。',
    highlight: '工业居住',
    features: ['工业区', '居住区', '交通节点'],
    attractions: [
      {
        name: '建桥工业园',
        distance: '500m',
        image: '',
        description: '现代化工业园区，重庆制造业基地',
        details:
          '建桥工业园是大渡口区重要的工业园区，汇聚了机械制造、建材、化工等多个产业。园区基础设施完善，交通便利，为入驻企业提供了良好的发展环境。这里是重庆制造业发展的重要载体，也是推动大渡口区经济发展的重要引擎。园区注重环保和可持续发展，体现了现代工业园区的发展理念。',
      },
      {
        name: '建桥社区文化中心',
        distance: '300m',
        image: '',
        description: '社区文化活动中心，居民精神文化生活场所',
        details:
          '建桥社区文化中心是当地居民进行文化活动的重要场所，设有图书阅览室、多功能活动厅、健身房等设施。中心定期举办各种文化活动、技能培训、健康讲座等，丰富了居民的精神文化生活。这里也是社区居民交流互动的重要平台，增进了邻里关系，体现了和谐社区的建设成果。',
      },
      {
        name: '建桥长江大桥观景点',
        distance: '1km',
        image: '',
        description: '观赏长江大桥的最佳位置',
        details:
          '建桥长江大桥观景点是观赏附近长江大桥的最佳位置，可以近距离欣赏大桥的雄伟壮观。这里视野开阔，可以看到长江两岸的美丽风光。观景点设有休息设施，是市民休闲观光的好去处。特别是在夕阳西下时，大桥与江水相映成趣，构成了一幅美丽的画卷。',
      },
    ],
    history: '建桥因附近有重要桥梁建设而得名，见证了重庆交通建设的发展。',
    culture: '工业文化与居住文化的融合，体现了重庆城市发展的特色。',
    tips: '可以了解重庆工业发展历程，感受工业与生活的和谐共存。',
  },
  {
    id: 'jinjiawan',
    name: '金家湾',
    order: 24,
    isTransfer: false,
    isFeatured: false,
    description: '巴南区北部重要站点，连接大渡口与巴南的桥梁',
    detailedDescription:
      '金家湾站位于巴南区北部，是连接大渡口区和巴南区的重要交通节点，周边以居住区和商业区为主，是巴南区重要的发展区域。',
    highlight: '交通节点',
    features: ['交通枢纽', '商业区', '居住区'],
    attractions: [
      {
        name: '金家湾商业街',
        distance: '400m',
        image: '',
        description: '巴南区重要商业街，购物休闲好去处',
        details:
          '金家湾商业街是巴南区北部重要的商业街区，汇聚了服装、餐饮、娱乐等多种业态。街道两旁商铺林立，商品丰富，价格实惠，是当地居民购物消费的主要场所。商业街环境整洁，设施完善，经常举办各种促销活动和文化表演。这里不仅是购物的好去处，也是体验巴南区商业文化的重要场所。',
      },
      {
        name: '金家湾公园',
        distance: '600m',
        image: '',
        description: '社区公园，居民休闲健身场所',
        details:
          '金家湾公园是一座综合性社区公园，占地面积约8公顷，为周边居民提供了良好的休闲健身场所。公园内绿化良好，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型广场，是居民进行广场舞、太极拳等健身活动的场所。公园环境优美，是附近居民日常休闲的重要场所。',
      },
      {
        name: '巴南区文化馆金家湾分馆',
        distance: '500m',
        image: '',
        description: '文化活动场所，丰富居民精神生活',
        details:
          '巴南区文化馆金家湾分馆是当地重要的文化活动场所，设有展览厅、多功能厅、培训室等设施。分馆定期举办各种文化展览、艺术培训、文艺演出等活动，为居民提供了丰富的精神文化生活。这里也是传承和弘扬巴南区地方文化的重要平台，增进了社区文化建设。',
      },
    ],
    history: '金家湾因历史上金姓人家聚居而得名，是巴南区的重要发展区域。',
    culture: '巴南区文化的重要组成部分，体现了地方文化特色。',
    tips: '适合购物休闲，体验巴南区的商业文化和社区生活。',
  },
  {
    id: 'liujiaba',
    name: '刘家坝',
    order: 25,
    isTransfer: false,
    isFeatured: false,
    description: '巴南区传统居住区，历史文化与现代发展并存',
    detailedDescription:
      '刘家坝站位于巴南区，是连接金家湾和白居寺的重要站点，这里保持着传统的居住区特色，同时也在现代化发展中不断进步。',
    highlight: '传统居住',
    features: ['传统社区', '历史文化', '居住区'],
    attractions: [
      {
        name: '刘家坝老街',
        distance: '300m',
        image: '',
        description: '巴南传统街区，保存完好的历史建筑',
        details:
          '刘家坝老街是巴南区保存较为完好的传统街区之一，街道两旁是典型的川东民居建筑，青砖黛瓦，古朴典雅。老街全长约400米，承载着丰富的历史文化内涵。这里曾是重要的商贸通道，见证了巴南区的历史变迁。老街内有传统的茶馆、小吃店、手工艺品店等，是体验巴南传统文化的重要场所。',
      },
      {
        name: '刘家坝社区广场',
        distance: '200m',
        image: '',
        description: '社区活动中心，居民文化生活场所',
        details:
          '刘家坝社区广场是当地居民进行文化活动的重要场所，广场面积约2000平方米，设有舞台、健身器材、休息座椅等设施。这里是居民进行广场舞、太极拳、社区活动的主要场所。广场周围绿化良好，环境优美，是社区居民交流互动的重要平台，体现了和谐社区的建设成果。',
      },
      {
        name: '巴南区历史文化展示馆',
        distance: '800m',
        image: '',
        description: '展示巴南历史文化的重要场所',
        details:
          '巴南区历史文化展示馆是展示巴南区历史文化的重要场所，馆内通过文物、图片、模型等形式，全面展示了巴南区从古至今的发展历程。展示馆设有古代文明、近现代发展、民俗文化等多个展区，是了解巴南区历史文化的重要窗口。这里也是进行爱国主义教育和历史文化教育的重要基地。',
      },
    ],
    history: '刘家坝因刘姓人家在此建坝而得名，是巴南区的传统居住区。',
    culture: '保持着深厚的巴南地方文化传统，是历史文化的重要载体。',
    tips: '适合了解巴南历史文化，体验传统社区生活。',
  },
])

// 计算属性和方法
const toggleMapView = () => {
  isMapExpanded.value = !isMapExpanded.value
}

const selectStation = (station: Station) => {
  selectedStationId.value = station.id
  selectedStation.value = station

  // 延迟渲染缩略图Canvas，确保DOM已更新
  nextTick(() => {
    setTimeout(() => {
      renderAllCanvasImages()
    }, 200) // 增加延迟确保DOM完全渲染
  })
}

const closeStationDetail = () => {
  selectedStation.value = null
  selectedStationId.value = null
}

const selectStationByName = (stationName: string) => {
  const station = allStations.value.find((s) => s.name === stationName)
  if (station) {
    selectStation(station)
    // 触发对应圆圈的视觉效果
    highlightStationCircle(stationName)
  }
}

// 高亮站点圆圈或五角星的函数
const highlightStationCircle = (stationName: string) => {
  // 找到对应的圆圈元素
  const circles = document.querySelectorAll(
    '.station-circle'
  ) as NodeListOf<SVGCircleElement>
  circles.forEach((circle) => {
    const nextElement = circle.nextElementSibling
    if (nextElement && nextElement.textContent?.trim() === stationName) {
      // 添加一个临时的CSS类来触发hover效果
      circle.classList.add('station-circle-clicked')

      // 500ms后移除效果
      setTimeout(() => {
        circle.classList.remove('station-circle-clicked')
      }, 500)
    }
  })

  // 找到对应的五角星元素
  const stars = document.querySelectorAll(
    '.station-star'
  ) as NodeListOf<SVGPathElement>
  stars.forEach((star) => {
    const nextElement = star.nextElementSibling
    if (nextElement && nextElement.textContent?.trim() === stationName) {
      // 添加一个临时的CSS类来触发hover效果
      star.classList.add('station-star-clicked')

      // 500ms后移除效果
      setTimeout(() => {
        star.classList.remove('station-star-clicked')
      }, 500)
    }
  })
}

// 景点相关函数
const openAttractionModal = (attraction: any) => {
  selectedAttraction.value = attraction
  isAttractionModalOpen.value = true
  currentImageIndex.value = 0
  updateCurrentImage()

  // 延迟渲染Canvas，确保DOM已更新
  nextTick(() => {
    renderAllCanvasImages()
  })
}

const closeAttractionModal = () => {
  selectedAttraction.value = null
  isAttractionModalOpen.value = false
  currentImageIndex.value = 0
  currentImage.value = ''
  resetImageZoom()
  if (isFullscreenView.value) {
    closeFullscreenView()
  }
}

// 图片轮播相关函数
const updateCurrentImage = () => {
  if (selectedAttraction.value) {
    if (
      selectedAttraction.value.images &&
      selectedAttraction.value.images.length > 0
    ) {
      currentImage.value =
        selectedAttraction.value.images[currentImageIndex.value]
    } else {
      currentImage.value = selectedAttraction.value.image || ''
    }
  }
}

const nextImage = () => {
  if (
    selectedAttraction.value?.images &&
    selectedAttraction.value.images.length > 1
  ) {
    currentImageIndex.value =
      (currentImageIndex.value + 1) % selectedAttraction.value.images.length
    updateCurrentImage()

    // 微信环境下强制重新渲染Canvas图片
    if (isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 50) // 增加延迟确保DOM更新完成
      })
    }
  }
}

const prevImage = () => {
  if (
    selectedAttraction.value?.images &&
    selectedAttraction.value.images.length > 1
  ) {
    currentImageIndex.value =
      currentImageIndex.value === 0
        ? selectedAttraction.value.images.length - 1
        : currentImageIndex.value - 1
    updateCurrentImage()

    // 微信环境下强制重新渲染Canvas图片
    if (isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 50) // 增加延迟确保DOM更新完成
      })
    }
  }
}

const setCurrentImage = (index: number) => {
  currentImageIndex.value = index
  updateCurrentImage()
}

// 图片缩放和拖拽相关
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const dragStartTranslateX = ref(0)
const dragStartTranslateY = ref(0)

const imageTransformStyle = computed(() => {
  return {
    transform: `scale(${imageScale.value}) translate(${imageTranslateX.value}px, ${imageTranslateY.value}px)`,
    cursor: isImageZoomed.value
      ? isDragging.value
        ? 'grabbing'
        : 'grab'
      : 'zoom-in',
    transition: isDragging.value ? 'none' : 'transform 0.3s ease',
  }
})

// 全屏查看相关函数
const openFullscreenView = () => {
  isFullscreenView.value = true
  // 重置拖拽位置，不使用缩放
  imageTranslateX.value = 0
  imageTranslateY.value = 0
  // 禁用页面滚动
  document.body.style.overflow = 'hidden'

  // 显示页面水印
  showPageWatermark()

  // 显示保护提示（3秒后自动隐藏）
  showProtectionNotice.value = true
  setTimeout(() => {
    showProtectionNotice.value = false
  }, 3000)

  // 延迟渲染全屏Canvas
  nextTick(() => {
    renderAllCanvasImages()
  })
}

const closeFullscreenView = () => {
  isFullscreenView.value = false
  resetImageZoom()
  // 恢复页面滚动
  document.body.style.overflow = ''
  // 隐藏页面水印
  hidePageWatermark()
}

const fullscreenImageStyle = computed(() => {
  // 移动端使用简化样式，直接显示原尺寸
  if (isMobile()) {
    return {
      transform: 'none',
      cursor: 'default',
      transition: 'none',
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      objectFit: 'contain',
    }
  }

  // 桌面端使用完整的缩放和拖拽功能
  return {
    transform: `scale(${imageScale.value}) translate(${imageTranslateX.value}px, ${imageTranslateY.value}px)`,
    cursor:
      imageScale.value > 1
        ? isDragging.value
          ? 'grabbing'
          : 'grab'
        : 'default',
    transition: isDragging.value ? 'none' : 'transform 0.3s ease',
  }
})

// 水印样式计算属性
const watermarkStyle = computed(() => {
  return {
    transform: `scale(${imageScale.value}) translate(${imageTranslateX.value}px, ${imageTranslateY.value}px)`,
    transition: isDragging.value ? 'none' : 'transform 0.3s ease',
  }
})

const handleImageWheel = (event: WheelEvent) => {
  if (!isImageZoomed.value) return

  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(1, Math.min(4, imageScale.value + delta))

  if (newScale === 1) {
    imageScale.value = 1
    imageTranslateX.value = 0
    imageTranslateY.value = 0
    isImageZoomed.value = false
  } else {
    imageScale.value = newScale
  }
}

const startImageDrag = (event: MouseEvent) => {
  if (!isImageZoomed.value || imageScale.value <= 1) return

  isDragging.value = true
  dragStartX.value = event.clientX
  dragStartY.value = event.clientY
  dragStartTranslateX.value = imageTranslateX.value
  dragStartTranslateY.value = imageTranslateY.value

  event.preventDefault()
}

const handleImageDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value
  const deltaY = event.clientY - dragStartY.value

  imageTranslateX.value = dragStartTranslateX.value + deltaX / imageScale.value
  imageTranslateY.value = dragStartTranslateY.value + deltaY / imageScale.value

  event.preventDefault()
}

const endImageDrag = () => {
  isDragging.value = false
}

const resetImageZoom = () => {
  imageScale.value = 1
  imageTranslateX.value = 0
  imageTranslateY.value = 0
  isImageZoomed.value = false
  isDragging.value = false
}

// 触摸事件处理
const startImageTouch = (event: TouchEvent) => {
  if (!isImageZoomed.value || imageScale.value <= 1) return

  if (event.touches.length === 1) {
    isDragging.value = true
    const touch = event.touches[0]
    dragStartX.value = touch.clientX
    dragStartY.value = touch.clientY
    dragStartTranslateX.value = imageTranslateX.value
    dragStartTranslateY.value = imageTranslateY.value
  }

  event.preventDefault()
}

const handleImageTouch = (event: TouchEvent) => {
  if (!isDragging.value || event.touches.length !== 1) return

  const touch = event.touches[0]
  const deltaX = touch.clientX - dragStartX.value
  const deltaY = touch.clientY - dragStartY.value

  imageTranslateX.value = dragStartTranslateX.value + deltaX / imageScale.value
  imageTranslateY.value = dragStartTranslateY.value + deltaY / imageScale.value

  event.preventDefault()
}

const endImageTouch = () => {
  isDragging.value = false
}

// 全屏模式缩放控制
const zoomIn = () => {
  imageScale.value = Math.min(4, imageScale.value + 0.25)
}

const zoomOut = () => {
  imageScale.value = Math.max(0.5, imageScale.value - 0.25)
}

const resetFullscreenZoom = () => {
  imageScale.value = 1
  imageTranslateX.value = 0
  imageTranslateY.value = 0
}

// 全屏模式的滚轮缩放
const handleFullscreenWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  imageScale.value = Math.max(0.5, Math.min(4, imageScale.value + delta))
}

// 全屏模式的拖拽
const startFullscreenDrag = (event: MouseEvent) => {
  if (imageScale.value <= 1) return

  isDragging.value = true
  dragStartX.value = event.clientX
  dragStartY.value = event.clientY
  dragStartTranslateX.value = imageTranslateX.value
  dragStartTranslateY.value = imageTranslateY.value

  event.preventDefault()
}

const handleFullscreenDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value
  const deltaY = event.clientY - dragStartY.value

  imageTranslateX.value = dragStartTranslateX.value + deltaX / imageScale.value
  imageTranslateY.value = dragStartTranslateY.value + deltaY / imageScale.value

  event.preventDefault()
}

const endFullscreenDrag = () => {
  isDragging.value = false
}

// 页面水印管理函数
const addPageWatermark = () => {
  if (pageWatermark) return // 避免重复添加

  pageWatermark = document.createElement('div')
  pageWatermark.innerHTML = '© 版权保护 - 禁止转载'
  pageWatermark.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 48px;
    color: rgba(0, 0, 0, 1);
    pointer-events: none;
    user-select: none;
    z-index: 10002;
    font-weight: bold;
    white-space: nowrap;
    display: none;
  `
  pageWatermark.id = 'page-watermark'
  document.body.appendChild(pageWatermark)
}

const showPageWatermark = () => {
  if (pageWatermark) {
    pageWatermark.style.display = 'block'
  }
}

const hidePageWatermark = () => {
  if (pageWatermark) {
    pageWatermark.style.display = 'none'
  }
}

// Canvas图片渲染函数
const renderImageToCanvas = (
  canvas: HTMLCanvasElement,
  imageSrc: string,
  watermarkText?: string,
  retryCount = 0
) => {
  return new Promise((resolve, reject) => {
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      reject(new Error('无法获取Canvas上下文'))
      return
    }

    const img = new Image()
    img.crossOrigin = 'anonymous' // 处理跨域问题

    img.onload = () => {
      // 获取Canvas容器的显示尺寸
      const isFullscreenCanvas = canvas.classList.contains('fullscreen-image')

      let containerWidth: number, containerHeight: number

      if (isFullscreenCanvas && isMobile()) {
        // 全屏模式下的移动端，使用视窗尺寸
        containerWidth = window.innerWidth
        containerHeight = window.innerHeight
      } else {
        // 其他情况使用原有逻辑
        containerWidth =
          canvas.clientWidth ||
          canvas.offsetWidth ||
          canvas.parentElement?.clientWidth ||
          (isFullscreenCanvas ? window.innerWidth * 0.9 : 300)
        containerHeight =
          canvas.clientHeight ||
          canvas.offsetHeight ||
          canvas.parentElement?.clientHeight ||
          (isFullscreenCanvas ? window.innerHeight * 0.9 : 200)
      }

      // 设置Canvas的实际像素尺寸（高分辨率）
      const pixelRatio = window.devicePixelRatio || 1
      canvas.width = containerWidth * pixelRatio
      canvas.height = containerHeight * pixelRatio

      // 设置Canvas的显示尺寸
      canvas.style.width = containerWidth + 'px'
      canvas.style.height = containerHeight + 'px'

      // 清除Canvas内容
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 缩放上下文以匹配高分辨率
      ctx.scale(pixelRatio, pixelRatio)

      // 计算图片在Canvas中的绘制尺寸
      const imgAspect = img.width / img.height
      const canvasAspect = containerWidth / containerHeight

      let drawWidth: number, drawHeight: number, drawX: number, drawY: number

      // 使用之前已声明的isFullscreenCanvas变量
      if (isFullscreenCanvas) {
        if (imgAspect < canvasAspect) {
          // 图片更宽，以宽度为准
          drawWidth = containerWidth
          drawHeight = containerWidth / imgAspect
          drawX = 0
          drawY = (containerHeight - drawHeight) / 2
        } else {
          // 图片更高，以高度为准
          drawHeight = containerHeight
          drawWidth = containerHeight * imgAspect
          drawX = (containerWidth - drawWidth) / 2
          drawY = 0
        }
      } else {
        drawWidth = containerWidth
        drawHeight = containerWidth / imgAspect
        drawX = 0
        drawY = (containerHeight - drawHeight) / 2
      }

      // 绘制图片
      ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight)

      // 添加水印
      if (watermarkText) {
        ctx.save()

        // 设置水印样式
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
        ctx.font = 'bold 12px Arial'
        ctx.textAlign = 'right'
        ctx.textBaseline = 'bottom'

        // 计算水印位置
        const padding = 8
        const textWidth = ctx.measureText(watermarkText).width
        const textHeight = 16

        // 绘制水印背景
        ctx.fillRect(
          containerWidth - textWidth - padding * 2,
          containerHeight - textHeight - padding,
          textWidth + padding * 2,
          textHeight + padding
        )

        // 绘制水印文字
        ctx.fillStyle = 'white'
        ctx.fillText(
          watermarkText,
          containerWidth - padding,
          containerHeight - padding
        )

        ctx.restore()
      }

      resolve(canvas)
    }

    img.onerror = () => {
      if (retryCount < 3) {
        setTimeout(() => {
          renderImageToCanvas(canvas, imageSrc, watermarkText, retryCount + 1)
            .then(resolve)
            .catch(reject)
        }, 1000 * (retryCount + 1))
      } else {
        reject(new Error('图片加载失败'))
      }
    }

    img.src = imageSrc
  })
}

// 创建Canvas图片元素
const createCanvasImage = (
  imageSrc: string,
  className: string,
  watermarkText?: string
) => {
  const canvas = document.createElement('canvas')
  canvas.className = className
  canvas.style.cssText = `
    width: 100%;
    height: 100%;
    object-fit: cover;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    pointer-events: auto;
  `

  renderImageToCanvas(canvas, imageSrc, watermarkText)
    .then(() => {
      console.log('Canvas图片渲染完成')
    })
    .catch((error) => {
      console.error('Canvas图片渲染失败:', error)
      // 如果Canvas渲染失败，回退到普通img
      const img = document.createElement('img')
      img.src = imageSrc
      img.className = className
      img.style.cssText = canvas.style.cssText
      canvas.parentNode?.replaceChild(img, canvas)
    })

  return canvas
}

// Canvas引用管理
const setCanvasRef = (key: string, el: HTMLCanvasElement | null) => {
  if (el) {
    canvasRefs.value[key] = el
  }
}

// Canvas容器点击处理
const handleCanvasContainerClick = (event: Event) => {
  // 移动端不触发全屏查看
  if (isMobile()) {
    return
  }

  // 检查点击的目标是否是导航按钮
  const target = event.target as HTMLElement
  if (target.closest('.nav-btn') || target.closest('.gallery-nav')) {
    return // 如果点击的是导航按钮，不触发全屏查看
  }

  event.preventDefault()
  event.stopPropagation()
  openFullscreenView()
}

// Canvas容器触摸结束处理
const handleCanvasContainerTouchEnd = (event: TouchEvent) => {
  // 移动端不触发全屏查看
  if (isMobile()) {
    return
  }

  // 检查触摸的目标是否是导航按钮
  const target = event.target as HTMLElement
  if (target.closest('.nav-btn') || target.closest('.gallery-nav')) {
    return // 如果触摸的是导航按钮，不触发全屏查看
  }

  event.preventDefault()
  event.stopPropagation()
  openFullscreenView()
}

// 渲染所有Canvas图片
const renderAllCanvasImages = () => {
  if (!isWechat()) return

  // 渲染缩略图
  if (selectedStation.value?.attractions) {
    selectedStation.value.attractions.forEach((attraction) => {
      if (attraction.image) {
        const canvasKey = `thumbnail-${attraction.name}`
        const canvas = canvasRefs.value[canvasKey]
        if (canvas) {
          // 确保Canvas有尺寸
          if (canvas.clientWidth === 0 || canvas.clientHeight === 0) {
            setTimeout(() => {
              renderImageToCanvas(canvas, attraction.image, '©').catch(
                (error) => console.error('缩略图Canvas渲染失败:', error)
              )
            }, 300)
          } else {
            renderImageToCanvas(canvas, attraction.image, '©').catch((error) =>
              console.error('缩略图Canvas渲染失败:', error)
            )
          }
        }
      }
    })
  }

  // 渲染详情图片 - 使用当前显示的图片
  if (currentImage.value) {
    const canvas = canvasRefs.value['detail-image']
    if (canvas) {
      // 强制清除Canvas
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
      }

      renderImageToCanvas(canvas, currentImage.value, '© 版权保护').catch(
        (error) => console.error('详情图片Canvas渲染失败:', error)
      )
    }
  }

  // 渲染全屏图片
  if (isFullscreenView.value && currentImage.value) {
    const canvas = canvasRefs.value['fullscreen-image']
    if (canvas) {
      // 强制清除Canvas
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
      }

      // 全屏Canvas需要特殊处理
      setTimeout(() => {
        // 移动端全屏时强制设置Canvas尺寸
        if (isMobile()) {
          canvas.style.width = '100vw'
          canvas.style.height = '100vh'
        }

        const watermarkText = isMobile()
          ? '🚫 版权保护'
          : '🚫 版权保护 - 禁止截屏'
        renderImageToCanvas(canvas, currentImage.value, watermarkText).catch(
          (error) => console.error('全屏图片Canvas渲染失败:', error)
        )
      }, 100)
    }
  }
}

// 检测微信浏览器
const isWechat = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

// 检测是否为移动端
const isMobile = () => {
  return (
    window.innerWidth <= 768 ||
    /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  )
}

// 版权保护相关函数
const initCopyrightProtection = () => {
  // 禁用右键菜单
  document.addEventListener('contextmenu', (e) => {
    const target = e.target as HTMLElement
    if (
      target &&
      target.classList &&
      target.classList.contains('protected-image')
    ) {
      e.preventDefault()
      return false
    }
  })

  // 禁用拖拽
  document.addEventListener('dragstart', (e) => {
    const target = e.target as HTMLElement
    if (
      target &&
      target.classList &&
      target.classList.contains('protected-image')
    ) {
      e.preventDefault()
      return false
    }
  })

  // 禁用选择
  document.addEventListener('selectstart', (e) => {
    const target = e.target as HTMLElement
    if (
      target &&
      target.classList &&
      target.classList.contains('protected-image')
    ) {
      e.preventDefault()
      return false
    }
  })

  // 禁用常见的截屏快捷键
  document.addEventListener('keydown', (e) => {
    // 禁用 PrintScreen
    if (e.key === 'PrintScreen') {
      e.preventDefault()
      alert('为保护版权，禁止截屏操作')
      return false
    }

    // 禁用 Ctrl+S (保存)
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault()
      alert('为保护版权，禁止保存操作')
      return false
    }

    // 禁用 F12 (开发者工具)
    if (e.key === 'F12') {
      e.preventDefault()
      alert('为保护版权，禁止使用开发者工具')
      return false
    }

    // 禁用 Ctrl+Shift+I (开发者工具)
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault()
      alert('为保护版权，禁止使用开发者工具')
      return false
    }

    // 禁用 Ctrl+U (查看源代码)
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault()
      alert('为保护版权，禁止查看源代码')
      return false
    }
  })

  // 检测开发者工具
  let devtools = {
    open: false,
    orientation: null,
  }

  const threshold = 160

  setInterval(() => {
    if (
      window.outerHeight - window.innerHeight > threshold ||
      window.outerWidth - window.innerWidth > threshold
    ) {
      if (!devtools.open) {
        devtools.open = false
        // alert('检测到开发者工具，为保护版权请关闭')
      }
    } else {
      devtools.open = false
    }
  }, 500)

  // 禁用控制台
  const originalConsole = console.log
  console.log = function () {
    if (arguments[0] && arguments[0].includes('protected')) {
      return
    }
    originalConsole.apply(console, arguments)
  }

  // 页面失焦检测（可能在截屏）
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      // 页面被隐藏，可能在截屏
      console.warn('页面失焦，可能存在截屏行为')
    }
  })

  // 禁用复制粘贴
  document.addEventListener('copy', (e) => {
    e.preventDefault()
    alert('为保护版权，禁止复制操作')
    return false
  })

  // 禁用剪切
  document.addEventListener('cut', (e) => {
    e.preventDefault()
    alert('为保护版权，禁止剪切操作')
    return false
  })

  // 监听窗口大小变化（可能在使用截屏工具）
  let lastWindowSize = { width: window.innerWidth, height: window.innerHeight }
  window.addEventListener('resize', () => {
    const currentSize = { width: window.innerWidth, height: window.innerHeight }
    const widthDiff = Math.abs(currentSize.width - lastWindowSize.width)
    const heightDiff = Math.abs(currentSize.height - lastWindowSize.height)

    // 如果窗口大小变化过大，可能在使用截屏工具
    if (widthDiff > 100 || heightDiff > 100) {
      console.warn('检测到窗口大小异常变化')
    }

    lastWindowSize = currentSize
  })

  // 微信浏览器特殊保护
  if (isWechat()) {
    initWechatProtection()
  }
}

// 微信浏览器专用保护
const initWechatProtection = () => {
  // 禁用长按事件，但保留点击事件
  let touchStartTime = 0
  let touchStartX = 0
  let touchStartY = 0

  document.addEventListener(
    'touchstart',
    (e) => {
      const target = e.target as HTMLElement
      if (
        target &&
        target.classList &&
        target.classList.contains('protected-image')
      ) {
        touchStartTime = Date.now()
        touchStartX = e.touches[0].clientX
        touchStartY = e.touches[0].clientY
        // 不阻止touchstart，让点击事件能正常触发
      }
    },
    { passive: true }
  )

  // 禁用长按上下文菜单
  document.addEventListener(
    'contextmenu',
    (e) => {
      const target = e.target as HTMLElement
      if (
        target &&
        target.classList &&
        target.classList.contains('protected-image')
      ) {
        e.preventDefault()
        e.stopPropagation()
        return false
      }
    },
    true
  )

  // 禁用触摸保持事件
  document.addEventListener(
    'touchend',
    (e) => {
      const target = e.target as HTMLElement
      if (
        target &&
        target.classList &&
        target.classList.contains('protected-image')
      ) {
        e.preventDefault()
      }
    },
    { passive: false }
  )

  // 禁用选择开始事件
  document.addEventListener(
    'selectstart',
    (e) => {
      const target = e.target as HTMLElement
      if (
        target &&
        target.classList &&
        target.classList.contains('protected-image')
      ) {
        e.preventDefault()
        return false
      }
    },
    true
  )

  // 监听长按手势
  let touchTimer: any = null

  document.addEventListener(
    'touchstart',
    (e) => {
      const target = e.target as HTMLElement
      if (
        target &&
        target.classList &&
        target.classList.contains('protected-image')
      ) {
        touchTimer = setTimeout(() => {
          // 长按超过400ms，显示警告并阻止
          if (isWechat()) {
            alert(
              '🚫 微信用户提醒\n\n为保护图片版权，已禁用长按保存功能。\n\n如需了解更多信息，请联系我们。'
            )
          } else {
            alert('为保护版权，禁止长按保存图片')
          }
          // 阻止长按后的默认行为
          document.addEventListener('contextmenu', preventContextMenu, {
            once: true,
          })
        }, 400)
      }
    },
    { passive: true }
  )

  // 阻止上下文菜单的函数
  const preventContextMenu = (e: Event) => {
    e.preventDefault()
    e.stopPropagation()
    return false
  }

  document.addEventListener('touchend', (e) => {
    if (touchTimer) {
      clearTimeout(touchTimer)
      touchTimer = null
    }

    // 检查是否是快速点击（小于400ms且没有移动太多）
    const touchEndTime = Date.now()
    const touchDuration = touchEndTime - touchStartTime
    const touchEndX = e.changedTouches[0].clientX
    const touchEndY = e.changedTouches[0].clientY
    const moveDistance = Math.sqrt(
      Math.pow(touchEndX - touchStartX, 2) +
        Math.pow(touchEndY - touchStartY, 2)
    )

    // 如果是快速点击且移动距离小于10px，允许点击事件
    if (touchDuration < 400 && moveDistance < 10) {
      // 这是一个有效的点击，不做任何阻止
      return
    }
  })

  document.addEventListener('touchmove', (e) => {
    if (touchTimer) {
      clearTimeout(touchTimer)
      touchTimer = null
    }
  })
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreenView.value) {
    closeFullscreenView()
  }
}

// 组件挂载时初始化版权保护和键盘事件

// 监听selectedStation变化，自动渲染Canvas
watch(
  selectedStation,
  (newStation) => {
    if (newStation && isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 300)
      })
    }
  },
  { immediate: true }
)

// 监听selectedAttraction变化，自动渲染Canvas
watch(
  selectedAttraction,
  (newAttraction) => {
    if (newAttraction && isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 100)
      })
    }
  },
  { immediate: true }
)

// 监听全屏状态变化，自动渲染Canvas
watch(
  isFullscreenView,
  (isFullscreen) => {
    if (isFullscreen && isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 200)
      })
    }
  },
  { immediate: true }
)

// 监听当前图片变化，自动重新渲染Canvas
watch(
  currentImage,
  (newImage) => {
    if (newImage && isWechat()) {
      nextTick(() => {
        setTimeout(() => {
          renderAllCanvasImages()
        }, 50)
      })
    }
  },
  { immediate: true }
)

onMounted(() => {
  initCopyrightProtection()
  document.addEventListener('keydown', handleKeydown)
  // 初始化页面水印（但不显示）
  addPageWatermark()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 确保页面滚动恢复
  document.body.style.overflow = ''
  // 清理页面水印
  if (pageWatermark) {
    document.body.removeChild(pageWatermark)
    pageWatermark = null
  }
})
</script>

<!-- 全局样式重置，确保页面可以滚动 -->
<style>
html,
body {
  overflow: auto !important;
  height: auto !important;
  max-height: none !important;
}

#app {
  overflow: auto !important;
  height: auto !important;
  max-height: none !important;
}
</style>

<style scoped>
/* 移动端重庆轨道交通2号线专题页面样式 */
.line2-tour-app {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #00a651 0%, #008b3d 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}

/* 顶部标题栏 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.header h1 {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.line-badge {
  display: flex;
  align-items: center;
  background: #00a651;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: bold;
}

.line-number {
  font-size: 18px;
  margin-right: 2px;
}

.line-name {
  font-size: 14px;
}

/* 主要内容区域 */
.main-content {
  padding: 20px 20px 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  min-height: calc(100vh - 80px);
}

/* 概览卡片 */
.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.line-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.line-color-bar {
  width: 6px;
  height: 60px;
  background: #00a651;
  border-radius: 3px;
}

.line-details h2 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.line-details p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.line-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 28px;
  font-weight: bold;
  color: #00a651;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 地铁线路图区域 */
.metro-map-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.map-header h3 {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.map-toggle {
  background: #00a651;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  transform-origin: center;
}

.map-toggle:hover {
  background: #008b3d;
  transform: translateY(-1px);
}

.metro-map {
  height: 300px;
  overflow: hidden;
  transition: height 0.3s ease;
  border-radius: 12px;
  background: #f8f9fa;
}

.metro-map.expanded {
  height: 600px;
}

.line-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.metro-line {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.station-circle {
  cursor: pointer;
  transition: all 0.3s ease;
  transform-origin: center center;
}

.station-circle:hover,
.station-circle-clicked {
  r: 8;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
}

/* 五角星样式 */
.station-star {
  cursor: pointer;
  transition: all 0.3s ease;
  transform-origin: center;
}

.station-star:hover,
.station-star-clicked {
  r: 8;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
}

.station-circle.transfer {
  stroke-width: 4;
}

.station-name {
  font-size: 14px;
  font-weight: 500;
  fill: #333;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.station-name:hover {
  font-weight: bold;
  fill: #00a651;
}

.station-name.transfer {
  font-weight: bold;
  font-size: 15px;
}

.station-highlight {
  font-size: 12px;
  fill: #00a651;
  font-weight: 500;
}

/* 线路信息区域 */
.line-info-section {
  margin-bottom: 24px;
}

.info-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.info-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #00a651;
}

/* 站点详情弹窗 */
.station-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #00a651 0%, #008b3d 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 22px;
  font-weight: bold;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 28px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  line-height: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

/* 弹窗滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #00a651;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #008b3d;
}

.station-image {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.station-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.station-intro {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #00a651;
}

.detail-section p {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 弹窗中的站点基本信息 */
.station-basic-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.station-type-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background: #f0f0f0;
  color: #666;
}

.station-type-badge.transfer {
  background: #e3f2fd;
  color: #1976d2;
}

.station-type-badge.featured {
  background: #fff3e0;
  color: #f57c00;
}

.station-highlight {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background: #00a651;
  color: white;
}

/* 弹窗中的特色标签 */
.modal-features {
  margin-bottom: 20px;
}

.modal-features h4 {
  color: #00a651;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.modal-features .feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.modal-features .feature-tag {
  background: #f0f8f0;
  color: #00a651;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

/* 弹窗中的周边景点 */
.modal-attractions {
  margin-bottom: 20px;
}

.modal-attractions h4 {
  color: #00a651;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.modal-attractions .attraction-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modal-attractions .attraction-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 12px;
}

.modal-attractions .attraction-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-attractions .attraction-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

/* 缩略图水印 */
.thumbnail-watermark {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  z-index: 10;
}

/* 微信防护遮罩 */
.wechat-protection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999;
  pointer-events: none; /* 改为none，不阻止点击事件 */
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

.wechat-fullscreen-protection {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999;
  pointer-events: none; /* 改为none，不阻止点击事件 */
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 微信浏览器特殊样式 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .protected-image {
    /* 微信浏览器中禁用所有触摸交互 */
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
    -webkit-appearance: none !important;
    outline: none !important;

    /* 创建一个不可见的伪元素来阻止长按 */
  }

  .protected-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background: transparent;
    pointer-events: auto;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    touch-action: none !important;
  }
}

.modal-attractions .attraction-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 无图片占位符样式 */
.no-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px;
  color: #999;
}

.no-image-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.no-image-text {
  font-size: 12px;
  color: #666;
}

.modal-attractions .attraction-info {
  flex: 1;
  min-width: 0;
}

.modal-attractions .attraction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.modal-attractions .attraction-name {
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.modal-attractions .attraction-distance {
  color: #00a651;
  font-size: 12px;
  font-weight: 500;
  background: rgba(0, 166, 81, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.modal-attractions .attraction-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.modal-attractions .attraction-arrow {
  flex-shrink: 0;
  color: #999;
  transition: transform 0.3s ease;
}

.modal-attractions .attraction-item:hover .attraction-arrow {
  transform: translateX(2px);
  color: #00a651;
}

/* 景点详细弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  padding: 20px;
}

.attraction-modal-overlay {
  z-index: 2000;
}

.attraction-modal-content {
  max-width: 600px;
  max-height: 80vh;
}

.attraction-modal-body {
  padding: 0;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

/* 景点弹窗滚动条样式 */
.attraction-modal-body::-webkit-scrollbar {
  width: 6px;
}

.attraction-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.attraction-modal-body::-webkit-scrollbar-thumb {
  background: #00a651;
  border-radius: 3px;
}

.attraction-modal-body::-webkit-scrollbar-thumb:hover {
  background: #008b3d;
}

.attraction-gallery {
  margin-bottom: 24px;
}

.gallery-main {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.gallery-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  transform-origin: center center;
  user-select: none;
  background: #f5f5f5;
}

/* 景点详情图片水印 */
.detail-image-watermark {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  pointer-events: none;
  user-select: none;
  z-index: 10;
  opacity: 0.9;
}

/* 缩略图版权标识 */
.thumbnail-copyright {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Canvas容器样式 */
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  -webkit-tap-highlight-color: transparent;
}

/* 桌面端Canvas容器样式 */
@media (min-width: 769px) {
  .canvas-container {
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
  }

  .canvas-container:hover {
    border-color: rgba(255, 255, 255, 0.5);
  }

  .canvas-container:active {
    border-color: rgba(255, 255, 255, 0.8);
  }
}

/* 移动端Canvas容器样式 */
@media (max-width: 768px) {
  .canvas-container {
    cursor: default;
    border: none;
  }
}

/* Canvas图片样式 */
.canvas-image {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-appearance: none !important;
  user-select: none !important;
  pointer-events: none; /* 禁用Canvas直接事件，使用覆盖层 */
  background: #f5f5f5;
}

/* Canvas点击覆盖层 */
.canvas-click-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  cursor: pointer;
  z-index: 1;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 全屏查看提示 */
.fullscreen-hint {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.canvas-click-overlay:hover .fullscreen-hint,
.canvas-click-overlay:active .fullscreen-hint {
  opacity: 1;
}

/* 移动端始终显示提示 */
@media (max-width: 768px) {
  .fullscreen-hint {
    opacity: 0.8;
  }
}

.fullscreen-icon {
  font-size: 16px;
}

/* 全屏Canvas特殊样式 */
.fullscreen-image.canvas-image {
  pointer-events: auto; /* 全屏时恢复Canvas事件 */
  cursor: grab;
  touch-action: pan-x pan-y pinch-zoom;
}

.fullscreen-image.canvas-image:active {
  cursor: grabbing;
}

/* 放大提示 */
.zoom-hint {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-main:hover .zoom-hint {
  opacity: 1;
}

/* 重置缩放按钮 */
.zoom-reset {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.zoom-reset:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

/* 全屏查看器样式 */
.fullscreen-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.fullscreen-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10001;
}

.fullscreen-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.fullscreen-image-container {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  transform-origin: center center;
  user-select: none;
}

/* 移动端全屏图片样式 */
@media (max-width: 768px) {
  .fullscreen-image-container {
    max-width: 100%;
    max-height: 100%;
    width: 100vw;
    height: 100vh;
    padding: 0;
  }

  .fullscreen-image {
    max-width: 100vw;
    max-height: 100vh;
    width: 100vw;
    height: 100vh;
    object-fit: contain;
    transform: none !important;
    cursor: default !important;
    transition: none !important;
  }

  .fullscreen-image.canvas-image {
    pointer-events: none !important;
    cursor: default !important;
    touch-action: none !important;
    width: 100vw !important;
    height: 100vh !important;
  }
}

.fullscreen-watermark {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  pointer-events: none;
  user-select: none;
  z-index: 10;
  transform-origin: bottom right;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

/* 全屏控制按钮 */
.fullscreen-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.7);
  padding: 12px 20px;
  border-radius: 30px;
  backdrop-filter: blur(10px);
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  color: white;
  font-size: 14px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

/* 全屏导航按钮 */
.fullscreen-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.fullscreen-nav-btn {
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.fullscreen-nav-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* 全屏指示器 */
.fullscreen-indicators {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
}

.fullscreen-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
}

.fullscreen-indicator.active {
  background: white;
  transform: scale(1.2);
}

.fullscreen-indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}

/* 版权保护提示 */
.protection-notice {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  border-radius: 16px;
  padding: 0;
  z-index: 10001;
  animation: fadeInOut 3s ease-in-out;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px 32px;
}

.notice-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.notice-text h3 {
  color: white;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
}

.notice-text p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* 图片轮播导航 */
.gallery-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  pointer-events: none;
  z-index: 999;
}

.nav-btn {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  position: relative;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* 图片指示器 */
.gallery-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #00a651;
  transform: scale(1.2);
}

.indicator:hover {
  background: #00a651;
}

/* 版权保护样式 */
.protected-image {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: auto;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.protected-image::selection {
  background: transparent;
}

.protected-image::-moz-selection {
  background: transparent;
}

/* 版权水印 */
.gallery-main {
  position: relative;
}

.copyright-watermark {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  user-select: none;
  z-index: 10;
}

/* 防止通过CSS隐藏水印 */
.copyright-watermark {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* 图片容器保护 */
.attraction-gallery,
.attraction-image {
  position: relative;
  overflow: hidden;
}

.attraction-gallery::before,
.attraction-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 1;
  pointer-events: none;
}

/* 禁用图片的所有交互 */
.protected-image {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  -ms-interpolation-mode: nearest-neighbor;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: pixelated;

  /* 微信浏览器专用保护 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-appearance: none !important;
  pointer-events: auto !important;

  /* 禁用长按选择 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;

  /* 禁用拖拽 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;

  /* 禁用右键和长按菜单 */
  -webkit-context-menu: none !important;
  -moz-context-menu: none !important;
  context-menu: none !important;
}

.attraction-details {
  padding: 0 24px 24px;
}

.attraction-basic-info {
  margin-bottom: 16px;
}

.attraction-distance-badge {
  display: inline-block;
  background: #00a651;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.attraction-intro {
  font-size: 16px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20px;
}

.attraction-detail-content {
  margin-bottom: 20px;
}

.attraction-detail-content h4 {
  color: #00a651;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.attraction-detail-content p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.attraction-practical-info h4 {
  color: #00a651;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.info-label {
  display: block;
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.info-value {
  display: block;
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 10px;
  }

  .header h1 {
    font-size: 18px;
  }

  .main-content {
    padding: 15px;
  }

  .overview-header {
    flex-direction: column;
    gap: 16px;
  }

  .line-stats {
    justify-content: center;
    width: 100%;
  }

  .line-details h2 {
    font-size: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 12px 15px;
  }

  .header h1 {
    font-size: 16px;
  }

  .line-badge {
    padding: 6px 10px;
  }

  .line-number {
    font-size: 16px;
  }

  .line-name {
    font-size: 12px;
  }

  .main-content {
    padding: 12px;
  }

  .overview-card,
  .metro-map-section,
  .info-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .line-details h2 {
    font-size: 18px;
  }

  .stat-number {
    font-size: 24px;
  }

  .station-info h4 {
    font-size: 18px;
  }

  .modal-content {
    margin: 10px;
    width: calc(100% - 20px);
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-header h3 {
    font-size: 20px;
  }

  .modal-body {
    padding: 20px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移动端站点圆圈、五角星和文字点击优化 */
  .station-circle,
  .station-star,
  .station-name {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    cursor: pointer;
  }

  .station-circle:active,
  .station-name:active {
    r: 8;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
  }

  .station-star:active {
    r: 8;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
  }

  .map-toggle:hover {
    transform: none;
  }

  .map-toggle:active {
    transform: translateY(-1px);
  }

  /* 景点列表移动端优化 */
  .modal-attractions .attraction-item {
    padding: 16px 12px;
  }

  .modal-attractions .attraction-image {
    width: 50px;
    height: 50px;
  }

  .modal-attractions .attraction-name {
    font-size: 13px;
  }

  .modal-attractions .attraction-description {
    font-size: 11px;
    -webkit-line-clamp: 1;
  }

  /* 景点详细弹窗移动端优化 */
  .attraction-modal-content {
    margin: 20px;
    max-height: calc(100vh - 40px);
  }

  .gallery-main {
    height: 200px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* 移动端图片缩放优化 */
  .zoom-hint,
  .zoom-reset {
    font-size: 11px;
    padding: 4px 8px;
  }

  .nav-btn {
    width: 44px;
    height: 44px;
    font-size: 20px;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.7);
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
    touch-action: manipulation;
  }

  .nav-btn:active {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(0.95);
  }

  .gallery-indicators {
    margin-top: 8px;
  }

  .indicator {
    width: 10px;
    height: 10px;
  }

  /* 全屏查看器移动端优化 */
  .fullscreen-close {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
  }

  .fullscreen-controls {
    bottom: 10px;
    padding: 8px 16px;
    gap: 8px;
  }

  .control-btn {
    width: 35px;
    height: 35px;
  }

  .zoom-level {
    font-size: 12px;
    min-width: 40px;
  }

  .fullscreen-nav {
    padding: 0 10px;
  }

  .fullscreen-nav-btn {
    width: 50px;
    height: 50px;
  }

  .fullscreen-indicators {
    top: 10px;
    gap: 8px;
  }

  .fullscreen-indicator {
    width: 10px;
    height: 10px;
  }

  .fullscreen-watermark {
    bottom: 10px;
    right: 10px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
  }

  /* 移动端景点详情水印优化 */
  .detail-image-watermark {
    bottom: 6px;
    right: 6px;
    padding: 4px 8px;
    font-size: 10px;
  }

  .thumbnail-copyright {
    width: 16px;
    height: 16px;
    font-size: 9px;
    bottom: 3px;
    right: 3px;
  }

  /* 移动端保护提示优化 */
  .protection-notice {
    margin: 20px;
  }

  .notice-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 20px 24px;
  }

  .notice-icon {
    font-size: 36px;
  }

  .notice-text h3 {
    font-size: 18px;
  }

  .notice-text p {
    font-size: 13px;
  }
}
</style>