<template>
  <view class="line2-tour-app">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">重庆轨道2号线</text>
        <view class="line-badge" @click="showLineSelector">
          <text class="line-number">2</text>
          <text class="line-name">号线</text>
          <text class="line-arrow">▼</text>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 线路概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <view class="line-info">
            <view class="line-color-bar"></view>
            <view class="line-details">
              <text class="line-title">重庆轨道交通2号线</text>
              <text class="line-subtitle">穿越山城的绿色长龙</text>
            </view>
          </view>
          <view class="line-stats">
            <view class="stat-item">
              <text class="stat-number">{{ stationCount }}</text>
              <text class="stat-label">站点</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 地铁地图区域 -->
      <view class="metro-map-section">
        <view class="map-header">
          <text class="map-title">线路图</text>
          <button class="map-toggle" @click="toggleMap">
            {{ mapExpanded ? '收起' : '展开' }}
          </button>
        </view>
        <view class="metro-map" :class="{ expanded: mapExpanded }">
          <view class="canvas-container">
            <!-- 这里可以放置SVG地图或canvas -->
            <text>地铁线路图</text>
          </view>
        </view>
      </view>

      <!-- 特色站点 -->
      <view class="featured-stations">
        <view class="stations-header">
          <view class="header-left">
            <text class="stations-title">特色站点</text>
            <text class="stations-count">{{ featuredStations.length }}个</text>
          </view>
        </view>
        <view class="stations-scroll">
          <view 
            v-for="station in featuredStations" 
            :key="station.id"
            class="station-item"
            @click="showStationDetail(station)"
          >
            <view class="station-icon">
              <text class="station-star">⭐</text>
            </view>
            <view class="station-content">
              <view class="station-header">
                <text class="station-name">{{ station.name }}</text>
                <text class="station-highlight">{{ station.highlight }}</text>
              </view>
              <text class="station-desc">{{ station.description }}</text>
              <view class="station-features">
                <text 
                  v-for="feature in station.features" 
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </text>
              </view>
            </view>
            <text class="station-arrow">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 站点详情弹窗 -->
    <view v-if="showStationModal" class="station-modal" @click="closeStationModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="header-left">
            <view class="station-title-area">
              <text class="modal-title">{{ currentStation.name }}</text>
              <view class="station-badges">
                <text class="station-type-badge">{{ currentStation.highlight }}</text>
              </view>
            </view>
          </view>
          <button class="close-btn" @click="closeStationModal">
            <text class="modal-close-icon">×</text>
          </button>
        </view>
        <view class="modal-body">
          <view class="station-details">
            <text class="station-intro">{{ currentStation.detailedDescription }}</text>
            
            <!-- 周边景点 -->
            <view v-if="currentStation.attractions" class="modal-attractions">
              <text class="attractions-title">周边景点</text>
              <view class="attraction-list">
                <view 
                  v-for="attraction in currentStation.attractions" 
                  :key="attraction.name"
                  class="attraction-item"
                  @click="showAttractionDetail(attraction)"
                >
                  <view class="attraction-image">
                    <image 
                      v-if="attraction.image" 
                      :src="attraction.image" 
                      class="attraction-thumbnail"
                    />
                    <view v-else class="no-image-placeholder">
                      <text class="no-image-icon">🏞️</text>
                      <text class="no-image-text">暂无图片</text>
                    </view>
                  </view>
                  <view class="attraction-info">
                    <view class="attraction-header">
                      <text class="attraction-name">{{ attraction.name }}</text>
                      <text class="attraction-distance">{{ attraction.distance }}</text>
                    </view>
                    <text class="attraction-description">{{ attraction.description }}</text>
                  </view>
                  <text class="attraction-arrow">›</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 线路选择器弹窗 -->
    <view v-if="showLineSelectorModal" class="line-selector-modal" @click="closeLineSelector">
      <view class="line-selector-container" @click.stop>
        <view class="line-selector-header">
          <text class="selector-title">选择线路</text>
          <view class="selector-close" @click="closeLineSelector">
            <text class="selector-close-icon">×</text>
          </view>
        </view>
        <view class="line-list">
          <view class="line-item current">
            <view class="line-info">
              <view class="line-badge-small line-2">
                <text class="line-number-small">2</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">2号线</text>
                <text class="line-desc">穿越山城的绿色长龙</text>
              </view>
            </view>
            <view class="line-status current-status">
              <text class="status-text">当前</text>
            </view>
          </view>
          <view 
            v-for="line in otherLines" 
            :key="line.number"
            class="line-item"
            @click="selectLine(line)"
          >
            <view class="line-info">
              <view :class="['line-badge-small', `line-${line.number}`]">
                <text class="line-number-small">{{ line.number }}</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">{{ line.number }}号线</text>
                <text class="line-desc">{{ line.description }}</text>
              </view>
            </view>
            <view class="line-status">
              <text class="status-text">建设中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面数据
const mapExpanded = ref(false)
const showStationModal = ref(false)
const showLineSelectorModal = ref(false)
const currentStation = ref<any>({})

// 站点数据 (从反编译代码中提取)
const stations = ref([
  {
    id: "jiaochangkou",
    name: "较场口",
    order: 1,
    isTransfer: true,
    isFeatured: true,
    description: "重庆母城的历史起点，古城门遗址所在地",
    detailedDescription: "较场口是重庆最具历史底蕴的地区之一，这里曾是古重庆城的南门，承载着山城千年的历史记忆。",
    highlight: "历史文化",
    features: ["历史古迹", "文化遗址", "换乘枢纽"],
    attractions: [
      {
        name: "凯旋路电梯",
        distance: "352m",
        image: "/static/images/cq_2/kxldt.jpg",
        description: "重庆最长的户外电梯，连接上下半城"
      }
    ]
  },
  // 更多站点数据...
])

// 其他线路数据
const otherLines = ref([
  { number: 1, description: "红色主干线" },
  { number: 3, description: "金色环线" },
  { number: 4, description: "紫色快线" },
  { number: 5, description: "蓝色支线" },
  { number: 6, description: "粉色新线" },
  { number: 9, description: "绿色支线" },
  { number: 10, description: "紫色环线" },
  { number: 18, description: "橙色快线" }
])

// 计算属性
const stationCount = computed(() => stations.value.length)
const featuredStations = computed(() => stations.value.filter(s => s.isFeatured))

// 方法
const toggleMap = () => {
  mapExpanded.value = !mapExpanded.value
}

const showLineSelector = () => {
  showLineSelectorModal.value = true
}

const closeLineSelector = () => {
  showLineSelectorModal.value = false
}

const showStationDetail = (station: any) => {
  currentStation.value = station
  showStationModal.value = true
}

const closeStationModal = () => {
  showStationModal.value = false
}

const showAttractionDetail = (attraction: any) => {
  console.log('显示景点详情:', attraction)
}

const selectLine = (line: any) => {
  uni.showToast({
    title: `${line.number}号线建设中...`,
    icon: 'none',
    duration: 2000
  })
  closeLineSelector()
}

onMounted(() => {
  console.log('重庆2号线页面加载完成')
})
</script>

<style scoped>
/* 从反编译的CSS中提取的样式 */
@import './cq-line2.css';
</style>
