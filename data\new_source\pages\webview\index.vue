<template>
  <view class="index-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/webview/index</text>
      <text>分类: webview</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/webview/index')
})
</script>

<style scoped>
.container[data-v-7f77755d]{width:100%;height:100vh;display:flex;flex-direction:column}uni-web-view[data-v-7f77755d]{flex:1;width:100%}.debug-info[data-v-7f77755d]{pointer-events:none}

</style>
