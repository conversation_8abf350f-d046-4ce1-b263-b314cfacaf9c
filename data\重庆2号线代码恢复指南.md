# 重庆2号线代码恢复指南

## 概述
通过APK反编译，我们成功找到了重庆2号线的完整代码，包括页面样式、JavaScript逻辑、站点数据等。

## 发现的重要文件

### 1. CSS样式文件
**位置**: `data/assets/assets/apps/__UNI__5CBF086/www/pages/rail/cq-line2.css`
- 包含完整的重庆2号线页面样式
- 包含轻轨穿楼、站点信息、景点展示等所有样式
- 包含响应式设计和动画效果

### 2. JavaScript逻辑代码
**位置**: `data/assets/assets/apps/__UNI__5CBF086/www/app-service.js`
- 包含重庆2号线的完整业务逻辑
- 包含站点数据、景点信息、图片预览等功能
- 包含线路选择器、站点详情弹窗等交互逻辑

### 3. 重庆文化页面样式
**位置**: `data/assets/assets/apps/__UNI__5CBF086/www/pages/culture/chongqing.css`
- 重庆地铁页面的样式文件
- 包含地图容器、站点信息面板等样式

## 关键代码片段

### 站点数据结构
从JavaScript代码中提取的站点数据包含以下信息：
- 站点ID、名称、顺序
- 是否为换乘站、特色站点
- 站点描述、详细介绍
- 周边景点信息（名称、距离、图片、详细介绍）
- 历史文化信息

### 主要功能
1. **轻轨穿楼展示** - 李子坝站的网红景点
2. **站点详情弹窗** - 显示站点信息和周边景点
3. **图片预览功能** - 带水印的图片浏览
4. **线路选择器** - 显示其他线路"建设中"状态
5. **响应式设计** - 适配不同屏幕尺寸

## 恢复步骤

### 第1步：创建页面文件
在 `millennia-now-app/src/pages/rail/` 目录下创建：
- `cq-line2.vue` - 主页面文件
- `cq-line2.css` - 样式文件（从反编译结果复制）

### 第2步：提取JavaScript逻辑
从 `app-service.js` 中提取重庆2号线相关的：
- 站点数据数组
- 页面组件逻辑
- 事件处理函数
- 图片预览功能

### 第3步：配置路由
在 `pages.json` 中添加路由配置：
```json
{
  "path": "pages/rail/cq-line2",
  "style": {
    "navigationBarTitleText": "重庆轨道2号线"
  }
}
```

### 第4步：添加静态资源
将站点和景点图片放置到对应的静态资源目录中。

## 重要发现

### 站点列表（部分）
1. **较场口** - 历史文化起点，换乘枢纽
2. **临江门** - 解放碑商圈核心
3. **黄花园** - 嘉陵江畔观光点
4. **大溪沟** - 文化艺术聚集地
5. **曾家岩** - 红色文化地标
6. **牛角沱** - 重要交通枢纽
7. **李子坝** - 网红轻轨穿楼
8. **佛图关** - 开往春天的地铁

### 特色功能
- **线路选择器弹窗** - 显示重庆所有轨道线路
- **站点详情模态框** - 展示站点信息和周边景点
- **图片预览系统** - 带版权保护水印
- **响应式地图展示** - 可展开/收起的地铁线路图

## 下一步操作建议

1. **立即备份** - 将反编译结果完整备份
2. **代码提取** - 从反编译文件中提取关键代码
3. **重新集成** - 将代码整合到当前项目中
4. **功能测试** - 确保所有功能正常工作
5. **样式调整** - 根据需要调整样式和布局

## 文件位置总结

```
data/
├── assets/assets/apps/__UNI__5CBF086/www/
│   ├── pages/rail/cq-line2.css          # 2号线样式文件 ⭐
│   ├── pages/culture/chongqing.css      # 重庆文化页面样式
│   └── app-service.js                   # 主要业务逻辑 ⭐
├── source_code/                         # Smali源代码
├── resources/                           # 应用资源文件
└── decompile_summary.txt               # 反编译总结
```

**标记⭐的文件是恢复重庆2号线功能的关键文件**

## 注意事项

1. 反编译的代码可能需要适当调整以适配当前项目结构
2. 图片路径可能需要更新
3. API接口调用可能需要重新配置
4. 建议先在测试环境中验证功能完整性

恢复工作现在可以开始了！所有必要的代码和资源都已经成功提取。
