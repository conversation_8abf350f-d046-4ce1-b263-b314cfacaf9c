{"name": "millennia-now-view", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "gsap": "^3.12.3", "pinia": "^2.1.7", "three": "^0.159.0", "vue": "^3.3.10", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.4", "@types/three": "^0.159.0", "@vitejs/plugin-vue": "^4.5.1", "sass": "^1.69.5", "typescript": "^5.3.3", "vite": "^5.0.5", "vue-tsc": "^1.8.25"}}