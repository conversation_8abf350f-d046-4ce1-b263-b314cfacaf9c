<?xml version="1.0" encoding="utf-8"?>
<rotate android:fromDegrees="0.0" android:toDegrees="360.0" android:pivotX="50.0%" android:pivotY="50.0%"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <shape android:shape="ring" android:innerRadiusRatio="3.0" android:thicknessRatio="12.0" android:useLevel="false">
        <size android:height="120.0dip" android:width="120.0dip" />
        <gradient android:startColor="#ffededed" android:endColor="#00ffffff" android:useLevel="false" android:angle="0.0" android:type="sweep" />
    </shape>
</rotate>