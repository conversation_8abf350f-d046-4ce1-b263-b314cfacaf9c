import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,
    open: true, // 自动打开浏览器
    cors: true, // 启用 CORS
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  // 确保静态资源被正确处理
  assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg'],
   // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      '@dcloudio/uni-app',
      '@dcloudio/uni-components'
    ]
  },
  // 明确指定构建入口
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      }
    }
  }
});
