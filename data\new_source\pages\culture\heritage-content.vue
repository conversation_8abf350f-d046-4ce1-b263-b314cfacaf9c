<template>
  <view class="heritage-content-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/culture/heritage-content</text>
      <text>分类: culture</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/culture/heritage-content')
})
</script>

<style scoped>
.heritage-content-page{background-color:#f9f9f9;position:relative;min-height:100vh;overflow-x:hidden}.animated-bg{position:fixed;top:0;left:0;width:100%;height:100%;z-index:0;pointer-events:none}.ink-wash{position:absolute;width:300px;height:300px;background:radial-gradient(circle,rgba(0,0,0,.03),rgba(0,0,0,0) 70%);border-radius:50%;opacity:0;transform:scale(.5)}.ink-wash:nth-child(1){top:10%;left:20%;animation:inkWash 15s ease-in-out 0s infinite}.ink-wash:nth-child(2){top:50%;left:80%;animation:inkWash 18s ease-in-out 2s infinite}.ink-wash:nth-child(3){top:80%;left:15%;animation:inkWash 20s ease-in-out 5s infinite}.ink-wash:nth-child(4){top:30%;left:60%;animation:inkWash 17s ease-in-out 7s infinite}.ink-wash:nth-child(5){top:70%;left:40%;animation:inkWash 22s ease-in-out 10s infinite}@keyframes inkWash{0%{opacity:0;transform:scale(.5)}40%{opacity:.5}80%{opacity:0;transform:scale(1.5)}to{opacity:0;transform:scale(.5)}}.cover-section{position:relative;height:12.5rem;overflow:hidden;animation:fadeIn 1.5s ease-out}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.cover-image{width:100%;height:100%;transform:scale(1.1);animation:zoomOut 2s ease-out forwards}@keyframes zoomOut{0%{transform:scale(1.1);filter:brightness(.8) saturate(1.2)}to{transform:scale(1);filter:brightness(1) saturate(1)}}.cover-overlay{position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(to bottom,rgba(0,0,0,.1),rgba(0,0,0,.7))}.cover-content{position:absolute;bottom:1.25rem;left:1.25rem;z-index:2;opacity:0;transform:translateY(20px);animation:slideUp 1.5s ease-out forwards;animation-delay:.5s}@keyframes slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.place-name{font-size:1.875rem;color:#fff;font-weight:700;text-shadow:0 .0625rem .125rem rgba(0,0,0,.5);display:block;margin-bottom:.3125rem}.place-desc{font-size:1rem;color:#fff;text-shadow:0 .0625rem .125rem rgba(0,0,0,.5)}.intro-section{background-color:#fff;padding:1.25rem .9375rem;margin:.625rem;border-radius:.375rem;opacity:0;transform:translateY(20px);animation:fadeInUp 1.5s ease-out forwards;animation-delay:.8s;position:relative;overflow:hidden;box-shadow:0 .125rem .375rem rgba(0,0,0,.08)}.intro-section:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at top right,rgba(0,0,0,.03),rgba(255,255,255,0) 70%);opacity:0;animation:inkFade 2s ease-out forwards;animation-delay:1.2s}@keyframes inkFade{0%{opacity:0}50%{opacity:1}to{opacity:.5}}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.intro-text{font-size:.9375rem;color:#333;line-height:1.8;text-align:justify;position:relative;z-index:1}.modern-section{padding:.9375rem .625rem;opacity:0;animation:fadeIn 1.5s ease-out forwards;animation-delay:1s;position:relative}.section-title{font-size:1.125rem;font-weight:700;color:#333;text-align:center;margin-bottom:1.25rem;position:relative}.section-title:after{content:"";position:absolute;bottom:-.3125rem;left:50%;width:0;height:.1875rem;background-color:#c8161e;border-radius:.09375rem;transform:translate(-50%);animation:lineExpand 1.5s ease-out forwards;animation-delay:1.5s}@keyframes lineExpand{0%{width:0}to{width:2.5rem}}.section-title:before{content:"";position:absolute;bottom:-.625rem;left:50%;width:0;height:0;background:radial-gradient(circle,rgba(200,22,30,.7),rgba(200,22,30,0) 70%);border-radius:50%;transform:translate(-50%);opacity:0;z-index:0;animation:inkDrop 2s ease-out forwards;animation-delay:1.2s}@keyframes inkDrop{0%{width:0;height:0;opacity:0}50%{opacity:.7}to{width:3.125rem;height:3.125rem;opacity:0}}.heritage-list{padding:0 .625rem}.empty-tip{text-align:center;padding:1.875rem .625rem;color:#999}.empty-text{font-size:.875rem;line-height:1.6}.heritage-item{display:flex;background-color:#fff;border-radius:.375rem;padding:.9375rem;margin-bottom:.9375rem;box-shadow:0 .125rem .375rem rgba(0,0,0,.05);position:relative;overflow:hidden;transform:translateY(30px);opacity:0;animation:cardFadeIn .8s ease-out forwards;transition:transform .3s,box-shadow .3s}.heritage-item:active{transform:translateY(0) scale(.98);box-shadow:0 .0625rem .25rem rgba(0,0,0,.08)}@keyframes cardFadeIn{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.heritage-image{width:5.625rem;height:5.625rem;border-radius:.25rem;margin-right:.9375rem;position:relative;z-index:1;overflow:hidden}.heritage-info{flex:1;position:relative;z-index:1}.heritage-title{font-size:1rem;font-weight:700;color:#333;margin-bottom:.3125rem;display:block}.heritage-type{font-size:.75rem;color:#c8161e;background-color:#fff0f0;padding:.125rem .375rem;border-radius:.125rem;display:inline-block;margin-bottom:.5rem}.heritage-brief{font-size:.875rem;color:#666;line-height:1.6;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis}.ink-animation{position:absolute;width:100%;height:100%;top:0;left:0;pointer-events:none;overflow:hidden}.ink-drop{position:absolute;width:200%;height:200%;background:radial-gradient(ellipse at center,rgba(0,0,0,.05),rgba(255,255,255,0) 70%);border-radius:50%;transform:translate(-50%,-50%);top:50%;left:50%;opacity:0;animation:inkSpread 3s ease-out infinite}@keyframes inkSpread{0%{transform:translate(-50%,-50%) scale(0);opacity:0}30%{opacity:.3}to{transform:translate(-50%,-50%) scale(1);opacity:0}}.footer{text-align:center;padding:1.875rem 0;opacity:0;animation:fadeIn 1.5s ease-out forwards;animation-delay:2s}.footer-text{font-size:.875rem;color:#999}.loading-container{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.9);display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:999}.ink-loading{position:relative;width:3.75rem;height:3.75rem;margin-bottom:.9375rem}.ink-circle{position:absolute;top:0;left:0;width:100%;height:100%;border:.1875rem solid #f0f0f0;border-top:.1875rem solid #c8161e;border-radius:50%;animation:spin 1.2s linear infinite}.ink-splash{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:1.875rem;height:1.875rem;background:radial-gradient(circle,rgba(200,22,30,.7),rgba(200,22,30,0) 70%);border-radius:50%;animation:pulsate 1.5s ease-in-out infinite}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes pulsate{0%{transform:translate(-50%,-50%) scale(.6);opacity:.2}50%{transform:translate(-50%,-50%) scale(1);opacity:.6}to{transform:translate(-50%,-50%) scale(.6);opacity:.2}}.loading-text{font-size:.875rem;color:#666;font-family:KaiTi,STKaiti,serif;letter-spacing:.0625rem}

</style>
