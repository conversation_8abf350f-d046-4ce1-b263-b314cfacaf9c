#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从app-service.js中提取Vue组件和页面逻辑
"""

import os
import re
import json
from pathlib import Path

class VueComponentExtractor:
    def __init__(self, source_path):
        self.source_path = Path(source_path)
        self.app_service_file = self.source_path / "app-service.js"
        
    def read_app_service(self):
        """读取app-service.js文件"""
        if not self.app_service_file.exists():
            print("❌ app-service.js文件不存在")
            return None
            
        with open(self.app_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    
    def extract_page_definitions(self, content):
        """提取页面定义"""
        # 查找__definePage调用
        page_pattern = r'__definePage\("([^"]+)",\s*([^)]+)\)'
        pages = re.findall(page_pattern, content)
        
        page_info = {}
        for page_path, component_ref in pages:
            page_info[page_path] = {
                'path': page_path,
                'component': component_ref.strip()
            }
        
        print(f"✓ 找到 {len(page_info)} 个页面定义")
        return page_info
    
    def extract_component_code(self, content, component_name):
        """提取特定组件的代码"""
        # 查找组件定义模式
        patterns = [
            rf'const\s+{component_name}\s*=\s*([^;]+);',
            rf'let\s+{component_name}\s*=\s*([^;]+);',
            rf'var\s+{component_name}\s*=\s*([^;]+);',
            rf'{component_name}\s*=\s*([^;]+);'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                return match.group(1)
        
        return None
    
    def extract_station_data(self, content):
        """提取重庆2号线站点数据"""
        # 查找站点数据数组
        station_pattern = r'pe=\[({[^}]+}[^]]+)\]'
        match = re.search(station_pattern, content, re.DOTALL)
        
        if match:
            station_data = match.group(1)
            return station_data
        return None
    
    def create_enhanced_vue_pages(self):
        """创建增强的Vue页面"""
        content = self.read_app_service()
        if not content:
            return
        
        # 提取页面定义
        pages = self.extract_page_definitions(content)
        
        # 特殊处理重庆2号线页面
        self.create_cq_line2_page(content)
        
        # 创建其他页面
        for page_path, page_info in pages.items():
            if 'cq-line2' not in page_path:  # 跳过已处理的2号线页面
                self.create_generic_page(page_path, content)
    
    def create_cq_line2_page(self, content):
        """创建重庆2号线页面"""
        # 提取站点数据
        station_data = self.extract_station_data(content)
        
        vue_content = '''<template>
  <view class="line2-tour-app">
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">重庆轨道2号线</text>
        <view class="line-badge" @click="showLineSelector">
          <text class="line-number">2</text>
          <text class="line-name">号线</text>
          <text class="line-arrow">▼</text>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 线路概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <view class="line-info">
            <view class="line-color-bar"></view>
            <view class="line-details">
              <text class="line-title">重庆轨道交通2号线</text>
              <text class="line-subtitle">穿越山城的绿色长龙</text>
            </view>
          </view>
          <view class="line-stats">
            <view class="stat-item">
              <text class="stat-number">{{ stationCount }}</text>
              <text class="stat-label">站点</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 地铁地图区域 -->
      <view class="metro-map-section">
        <view class="map-header">
          <text class="map-title">线路图</text>
          <button class="map-toggle" @click="toggleMap">
            {{ mapExpanded ? '收起' : '展开' }}
          </button>
        </view>
        <view class="metro-map" :class="{ expanded: mapExpanded }">
          <view class="canvas-container">
            <!-- 这里可以放置SVG地图或canvas -->
            <text>地铁线路图</text>
          </view>
        </view>
      </view>

      <!-- 特色站点 -->
      <view class="featured-stations">
        <view class="stations-header">
          <view class="header-left">
            <text class="stations-title">特色站点</text>
            <text class="stations-count">{{ featuredStations.length }}个</text>
          </view>
        </view>
        <view class="stations-scroll">
          <view 
            v-for="station in featuredStations" 
            :key="station.id"
            class="station-item"
            @click="showStationDetail(station)"
          >
            <view class="station-icon">
              <text class="station-star">⭐</text>
            </view>
            <view class="station-content">
              <view class="station-header">
                <text class="station-name">{{ station.name }}</text>
                <text class="station-highlight">{{ station.highlight }}</text>
              </view>
              <text class="station-desc">{{ station.description }}</text>
              <view class="station-features">
                <text 
                  v-for="feature in station.features" 
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </text>
              </view>
            </view>
            <text class="station-arrow">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 站点详情弹窗 -->
    <view v-if="showStationModal" class="station-modal" @click="closeStationModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="header-left">
            <view class="station-title-area">
              <text class="modal-title">{{ currentStation.name }}</text>
              <view class="station-badges">
                <text class="station-type-badge">{{ currentStation.highlight }}</text>
              </view>
            </view>
          </view>
          <button class="close-btn" @click="closeStationModal">
            <text class="modal-close-icon">×</text>
          </button>
        </view>
        <view class="modal-body">
          <view class="station-details">
            <text class="station-intro">{{ currentStation.detailedDescription }}</text>
            
            <!-- 周边景点 -->
            <view v-if="currentStation.attractions" class="modal-attractions">
              <text class="attractions-title">周边景点</text>
              <view class="attraction-list">
                <view 
                  v-for="attraction in currentStation.attractions" 
                  :key="attraction.name"
                  class="attraction-item"
                  @click="showAttractionDetail(attraction)"
                >
                  <view class="attraction-image">
                    <image 
                      v-if="attraction.image" 
                      :src="attraction.image" 
                      class="attraction-thumbnail"
                    />
                    <view v-else class="no-image-placeholder">
                      <text class="no-image-icon">🏞️</text>
                      <text class="no-image-text">暂无图片</text>
                    </view>
                  </view>
                  <view class="attraction-info">
                    <view class="attraction-header">
                      <text class="attraction-name">{{ attraction.name }}</text>
                      <text class="attraction-distance">{{ attraction.distance }}</text>
                    </view>
                    <text class="attraction-description">{{ attraction.description }}</text>
                  </view>
                  <text class="attraction-arrow">›</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 线路选择器弹窗 -->
    <view v-if="showLineSelectorModal" class="line-selector-modal" @click="closeLineSelector">
      <view class="line-selector-container" @click.stop>
        <view class="line-selector-header">
          <text class="selector-title">选择线路</text>
          <view class="selector-close" @click="closeLineSelector">
            <text class="selector-close-icon">×</text>
          </view>
        </view>
        <view class="line-list">
          <view class="line-item current">
            <view class="line-info">
              <view class="line-badge-small line-2">
                <text class="line-number-small">2</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">2号线</text>
                <text class="line-desc">穿越山城的绿色长龙</text>
              </view>
            </view>
            <view class="line-status current-status">
              <text class="status-text">当前</text>
            </view>
          </view>
          <view 
            v-for="line in otherLines" 
            :key="line.number"
            class="line-item"
            @click="selectLine(line)"
          >
            <view class="line-info">
              <view :class="['line-badge-small', `line-${line.number}`]">
                <text class="line-number-small">{{ line.number }}</text>
              </view>
              <view class="line-details-small">
                <text class="line-name-small">{{ line.number }}号线</text>
                <text class="line-desc">{{ line.description }}</text>
              </view>
            </view>
            <view class="line-status">
              <text class="status-text">建设中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面数据
const mapExpanded = ref(false)
const showStationModal = ref(false)
const showLineSelectorModal = ref(false)
const currentStation = ref<any>({})

// 站点数据 (从反编译代码中提取)
const stations = ref([
  {
    id: "jiaochangkou",
    name: "较场口",
    order: 1,
    isTransfer: true,
    isFeatured: true,
    description: "重庆母城的历史起点，古城门遗址所在地",
    detailedDescription: "较场口是重庆最具历史底蕴的地区之一，这里曾是古重庆城的南门，承载着山城千年的历史记忆。",
    highlight: "历史文化",
    features: ["历史古迹", "文化遗址", "换乘枢纽"],
    attractions: [
      {
        name: "凯旋路电梯",
        distance: "352m",
        image: "/static/images/cq_2/kxldt.jpg",
        description: "重庆最长的户外电梯，连接上下半城"
      }
    ]
  },
  // 更多站点数据...
])

// 其他线路数据
const otherLines = ref([
  { number: 1, description: "红色主干线" },
  { number: 3, description: "金色环线" },
  { number: 4, description: "紫色快线" },
  { number: 5, description: "蓝色支线" },
  { number: 6, description: "粉色新线" },
  { number: 9, description: "绿色支线" },
  { number: 10, description: "紫色环线" },
  { number: 18, description: "橙色快线" }
])

// 计算属性
const stationCount = computed(() => stations.value.length)
const featuredStations = computed(() => stations.value.filter(s => s.isFeatured))

// 方法
const toggleMap = () => {
  mapExpanded.value = !mapExpanded.value
}

const showLineSelector = () => {
  showLineSelectorModal.value = true
}

const closeLineSelector = () => {
  showLineSelectorModal.value = false
}

const showStationDetail = (station: any) => {
  currentStation.value = station
  showStationModal.value = true
}

const closeStationModal = () => {
  showStationModal.value = false
}

const showAttractionDetail = (attraction: any) => {
  console.log('显示景点详情:', attraction)
}

const selectLine = (line: any) => {
  uni.showToast({
    title: `${line.number}号线建设中...`,
    icon: 'none',
    duration: 2000
  })
  closeLineSelector()
}

onMounted(() => {
  console.log('重庆2号线页面加载完成')
})
</script>

<style scoped>
/* 从反编译的CSS中提取的样式 */
@import './cq-line2.css';
</style>
'''
        
        # 保存到对应的页面文件
        page_file = self.source_path / "pages/rail/cq-line2.vue"
        with open(page_file, 'w', encoding='utf-8') as f:
            f.write(vue_content)
        
        print("✓ 创建重庆2号线页面完成")
    
    def create_generic_page(self, page_path, content):
        """创建通用页面"""
        # 从路径中提取页面信息
        path_parts = page_path.split('/')
        if len(path_parts) >= 3:
            category = path_parts[1]
            page_name = path_parts[2]
            
            # 读取对应的CSS文件
            css_file = self.source_path / f"pages/{category}/{page_name}.vue"
            if css_file.exists():
                print(f"✓ 页面已存在: {page_path}")
                return
    
    def run(self):
        """运行提取流程"""
        print("🔧 开始提取Vue组件...")
        self.create_enhanced_vue_pages()
        print("✅ Vue组件提取完成!")

if __name__ == "__main__":
    extractor = VueComponentExtractor("data/new_source")
    extractor.run()
