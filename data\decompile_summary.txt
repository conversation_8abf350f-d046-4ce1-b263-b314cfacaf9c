APK反编译结果摘要
==================================================

APK文件: millennia-now-app/new.apk
反编译时间: 2025-07-31
反编译工具: apktool 2.9.3

反编译状态: 部分成功
说明: 主要内容已成功反编译，但在处理 assets/39285EFA.dex 文件时出现错误。
      这不影响主要的应用代码和资源文件的反编译。

目录结构:
├── source_code/         # Smali源代码文件
│   ├── smali/          # 主要应用代码
│   ├── smali_classes2/ # 第二个dex文件的代码
│   └── smali_assets/   # Assets中的dex代码
├── resources/          # 应用资源文件
│   └── res/           # 布局、图片、字符串等资源
├── assets/             # Assets文件夹内容
│   ├── apps/          # 应用相关文件
│   ├── data/          # 数据文件
│   ├── fonts/         # 字体文件
│   └── res/           # 资源文件
├── manifest/           # 应用清单文件
│   └── AndroidManifest.xml
├── libraries/          # 原生库文件
│   ├── lib/
│   │   ├── arm64-v8a/  # 64位ARM库
│   │   └── armeabi-v7a/ # 32位ARM库
├── raw_files/          # APK原始文件（ZIP解压）
│   ├── classes.dex     # Dalvik字节码
│   ├── classes2.dex    # 第二个字节码文件
│   ├── resources.arsc  # 编译后的资源
│   └── META-INF/       # 签名信息
└── apktool_output/     # apktool完整输出（已整理到其他目录）

重要文件说明:
- AndroidManifest.xml: 应用配置文件，包含权限、组件声明等
- smali/: 反编译的Java代码（Smali汇编格式）
- res/: 应用资源文件（布局、图片、字符串等）
- assets/: 应用打包的静态资源文件
- lib/: 原生库文件（.so文件）

查找重庆2号线相关代码建议:
1. 在 source_code/smali/ 目录中搜索关键词：
   - "2号线" 或 "line2"
   - "重庆" 或 "chongqing"
   - "轨道" 或 "rail"
   - "cq-line"

2. 在 resources/res/values/ 目录中查看字符串资源文件

3. 在 assets/ 目录中查找相关的数据文件或配置文件

反编译完成！您现在可以在这些目录中查找重庆2号线的相关代码了。
