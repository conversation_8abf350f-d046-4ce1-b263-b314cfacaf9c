#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK反编译源代码提取工具
将反编译的uni-app代码重构为完整的源代码项目结构
"""

import os
import json
import re
import shutil
from pathlib import Path

class UniAppSourceExtractor:
    def __init__(self, apk_data_path, output_path):
        self.apk_data_path = Path(apk_data_path)
        self.output_path = Path(output_path)
        self.www_path = self.apk_data_path / "assets/assets/apps/__UNI__5CBF086/www"
        
        # 确保输出目录存在
        self.output_path.mkdir(parents=True, exist_ok=True)
        
    def extract_manifest(self):
        """提取manifest.json配置"""
        manifest_file = self.www_path / "manifest.json"
        if manifest_file.exists():
            with open(manifest_file, 'r', encoding='utf-8') as f:
                manifest_data = json.load(f)
            
            # 创建pages.json (uni-app页面配置)
            pages_config = {
                "pages": [],
                "tabBar": manifest_data.get("plus", {}).get("tabBar", {}),
                "globalStyle": {
                    "navigationBarTitleText": manifest_data.get("name", "千载·今知"),
                    "navigationBarBackgroundColor": "#000000",
                    "navigationBarTextStyle": "white"
                }
            }
            
            # 从tabBar提取页面路径
            tab_bar = manifest_data.get("plus", {}).get("tabBar", {})
            if "list" in tab_bar:
                for tab in tab_bar["list"]:
                    page_path = tab["pagePath"]
                    pages_config["pages"].append({
                        "path": page_path,
                        "style": {
                            "navigationBarTitleText": tab["text"]
                        }
                    })
            
            # 保存pages.json
            with open(self.output_path / "pages.json", 'w', encoding='utf-8') as f:
                json.dump(pages_config, f, ensure_ascii=False, indent=2)
            
            # 保存原始manifest.json
            with open(self.output_path / "manifest.json", 'w', encoding='utf-8') as f:
                json.dump(manifest_data, f, ensure_ascii=False, indent=2)
                
            print("✓ 提取manifest.json和pages.json完成")
            return pages_config
        return None
    
    def extract_pages(self):
        """提取所有页面文件"""
        pages_dir = self.www_path / "pages"
        if not pages_dir.exists():
            print("❌ 页面目录不存在")
            return
        
        output_pages_dir = self.output_path / "pages"
        output_pages_dir.mkdir(exist_ok=True)
        
        page_list = []
        
        # 遍历所有页面目录
        for page_category in pages_dir.iterdir():
            if page_category.is_dir():
                category_name = page_category.name
                output_category_dir = output_pages_dir / category_name
                output_category_dir.mkdir(exist_ok=True)
                
                # 处理每个页面
                for css_file in page_category.glob("*.css"):
                    page_name = css_file.stem
                    page_info = {
                        "path": f"pages/{category_name}/{page_name}",
                        "category": category_name,
                        "name": page_name
                    }
                    page_list.append(page_info)
                    
                    # 创建Vue页面文件
                    self.create_vue_page(css_file, output_category_dir / f"{page_name}.vue", page_info)
                    
                    print(f"✓ 创建页面: {page_info['path']}")
        
        return page_list
    
    def create_vue_page(self, css_file, output_file, page_info):
        """根据CSS文件创建Vue页面"""
        # 读取CSS内容
        with open(css_file, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 生成Vue页面模板
        vue_template = f'''<template>
  <view class="{page_info['name']}-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: {page_info['path']}</text>
      <text>分类: {page_info['category']}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import {{ ref, onMounted }} from 'vue'

// 页面数据
const pageData = ref({{}})

// 页面方法
onMounted(() => {{
  console.log('页面加载: {page_info['path']}')
}})
</script>

<style scoped>
{css_content}
</style>
'''
        
        # 保存Vue文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(vue_template)
    
    def extract_static_resources(self):
        """提取静态资源"""
        static_dir = self.www_path / "static"
        if static_dir.exists():
            output_static_dir = self.output_path / "static"
            if output_static_dir.exists():
                shutil.rmtree(output_static_dir)
            shutil.copytree(static_dir, output_static_dir)
            print("✓ 提取静态资源完成")
    
    def extract_app_files(self):
        """提取应用主文件"""
        # 复制主要的JS文件
        main_files = [
            "app-service.js",
            "app-config.js", 
            "app-config-service.js",
            "app.css"
        ]
        
        for file_name in main_files:
            src_file = self.www_path / file_name
            if src_file.exists():
                dst_file = self.output_path / file_name
                shutil.copy2(src_file, dst_file)
                print(f"✓ 复制文件: {file_name}")
    
    def create_project_structure(self):
        """创建完整的项目结构"""
        # 创建标准的uni-app目录结构
        directories = [
            "components",
            "utils", 
            "api",
            "store",
            "types"
        ]
        
        for dir_name in directories:
            (self.output_path / dir_name).mkdir(exist_ok=True)
        
        # 创建基础文件
        self.create_main_ts()
        self.create_app_vue()
        self.create_uni_scss()
        
        print("✓ 创建项目结构完成")
    
    def create_main_ts(self):
        """创建main.ts入口文件"""
        main_content = '''import { createSSRApp } from 'vue'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
'''
        with open(self.output_path / "main.ts", 'w', encoding='utf-8') as f:
            f.write(main_content)
    
    def create_app_vue(self):
        """创建App.vue主应用文件"""
        app_content = '''<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  console.log('App Launch')
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style>
/* 全局样式 */
@import './app.css';
</style>
'''
        with open(self.output_path / "App.vue", 'w', encoding='utf-8') as f:
            f.write(app_content)
    
    def create_uni_scss(self):
        """创建uni.scss全局样式文件"""
        scss_content = '''/* uni-app全局样式文件 */

/* 全局变量 */
$primary-color: #C8161E;
$background-color: #ffffff;
$text-color: #333333;

/* 全局样式 */
page {
  background-color: $background-color;
  color: $text-color;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 通用类 */
.container {
  padding: 20rpx;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
'''
        with open(self.output_path / "uni.scss", 'w', encoding='utf-8') as f:
            f.write(scss_content)
    
    def run(self):
        """执行完整的提取流程"""
        print("🚀 开始提取uni-app源代码...")
        print(f"输入路径: {self.apk_data_path}")
        print(f"输出路径: {self.output_path}")
        print("-" * 50)
        
        # 1. 提取配置文件
        manifest_data = self.extract_manifest()
        
        # 2. 创建项目结构
        self.create_project_structure()
        
        # 3. 提取页面
        pages = self.extract_pages()
        
        # 4. 提取静态资源
        self.extract_static_resources()
        
        # 5. 提取应用文件
        self.extract_app_files()
        
        print("-" * 50)
        print("✅ 源代码提取完成!")
        print(f"📁 输出目录: {self.output_path}")
        print(f"📄 提取页面数量: {len(pages) if pages else 0}")
        
        return True

if __name__ == "__main__":
    # 配置路径
    apk_data_path = "data"
    output_path = "data/new_source"
    
    # 创建提取器并运行
    extractor = UniAppSourceExtractor(apk_data_path, output_path)
    extractor.run()
