<template>
  <view class="chongqing-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/culture/chongqing</text>
      <text>分类: culture</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/culture/chongqing')
})
</script>

<style scoped>
.chongqing-metro-page[data-v-180b4ea4]{width:100%;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);position:relative}.header[data-v-180b4ea4]{position:fixed;top:0;left:0;right:0;height:2.75rem;background:rgba(255,255,255,.95);-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);display:flex;align-items:center;justify-content:center;padding:0 1.25rem;z-index:1000;box-shadow:0 .125rem .625rem rgba(0,0,0,.1)}.title[data-v-180b4ea4]{font-size:1.125rem;font-weight:700;color:#333}.back-button[data-v-180b4ea4]{position:absolute;left:1.25rem;width:1.875rem;height:1.875rem;display:flex;align-items:center;justify-content:center;border-radius:50%;background:rgba(0,0,0,.1)}.back-icon[data-v-180b4ea4]{font-size:1rem;color:#333}.map-container[data-v-180b4ea4]{position:fixed;top:2.75rem;left:0;right:0;bottom:6.875rem;padding:.625rem;display:flex;flex-direction:column}.metro-wrapper[data-v-180b4ea4]{position:relative;flex:1;background:rgba(255,255,255,.95);border-radius:.625rem;box-shadow:0 .25rem 1rem rgba(0,0,0,.1);aspect-ratio:1 / 2;max-height:100%;margin:0 auto}.metro-canvas[data-v-180b4ea4]{width:100%;height:100%;display:block}@keyframes stationPulse-180b4ea4{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.05)}}@keyframes attractionBounce-180b4ea4{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-.09375rem)}60%{transform:translateY(-.03125rem)}}.station-info-panel[data-v-180b4ea4]{position:fixed;bottom:0;left:0;right:0;background:#fff;border-radius:1.25rem 1.25rem 0 0;padding:1.25rem;box-shadow:0 -.25rem 1rem rgba(0,0,0,.1);z-index:999;max-height:60vh;overflow-y:auto}.station-header[data-v-180b4ea4]{display:flex;align-items:center;justify-content:space-between;margin-bottom:.9375rem}.station-name[data-v-180b4ea4]{font-size:1.25rem;font-weight:700;color:#333}.close-btn[data-v-180b4ea4]{width:1.875rem;height:1.875rem;display:flex;align-items:center;justify-content:center;border-radius:50%;background:#f5f5f5;font-size:1rem;color:#666}.station-details[data-v-180b4ea4]{margin-bottom:.9375rem}.detail-item[data-v-180b4ea4]{display:flex;align-items:center;margin-bottom:.625rem}.label[data-v-180b4ea4]{font-size:.875rem;color:#666;margin-right:.625rem;min-width:5rem}.value[data-v-180b4ea4]{font-size:.875rem;color:#333;font-weight:500}.attractions-section[data-v-180b4ea4]{margin-top:.9375rem}.section-title[data-v-180b4ea4]{font-size:1rem;font-weight:700;color:#333;margin-bottom:.625rem;display:block}.attractions-list[data-v-180b4ea4]{display:flex;flex-wrap:wrap;gap:.625rem}.attraction-tag[data-v-180b4ea4]{background:linear-gradient(135deg,#ff6b35,#ff8a65);color:#fff;padding:.375rem .75rem;border-radius:1.25rem;font-size:.75rem;font-weight:500;box-shadow:0 .125rem .375rem rgba(255,107,53,.3)}.line-info[data-v-180b4ea4]{position:fixed;bottom:.625rem;left:.625rem;right:.625rem;background:rgba(255,255,255,.95);-webkit-backdrop-filter:blur(.625rem);backdrop-filter:blur(.625rem);border-radius:.625rem;padding:.75rem;box-shadow:0 .25rem 1rem rgba(0,0,0,.1);z-index:998;max-height:5.625rem}.line-header[data-v-180b4ea4]{display:flex;align-items:center;margin-bottom:.3125rem}.line-indicator[data-v-180b4ea4]{width:.25rem;height:1.25rem;background:#ff6b35;border-radius:.125rem;margin-right:.625rem}.line-name[data-v-180b4ea4]{font-size:1rem;font-weight:700;color:#333}.line-description[data-v-180b4ea4]{font-size:.8125rem;color:#666;line-height:1.5;margin-bottom:.625rem}.line-stats[data-v-180b4ea4]{display:flex;justify-content:space-between;gap:.625rem}.stat-item[data-v-180b4ea4]{flex:1;text-align:center;padding:.46875rem;background:rgba(255,107,53,.1);border-radius:.375rem;border:.03125rem solid rgba(255,107,53,.2)}.stat-label[data-v-180b4ea4]{display:block;font-size:.6875rem;color:#666;margin-bottom:.25rem}.stat-value[data-v-180b4ea4]{display:block;font-size:.875rem;font-weight:700;color:#ff6b35}@keyframes attractionPulse-180b4ea4{0%,to{opacity:1;transform:scale(1)}50%{opacity:.8;transform:scale(1.05)}}

</style>
