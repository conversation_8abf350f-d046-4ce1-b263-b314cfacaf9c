<template>
  <view class="chongqing-metro-page">
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">重庆地铁二号线旅游图</text>
      <view class="back-button"
            @click="goBack">
        <text class="back-icon">←</text>
      </view>
    </view>

    <!-- 地铁线路图容器 -->
    <view class="map-container">
      <view class="metro-wrapper">
        <!-- Canvas地铁线路图 -->
        <canvas canvas-id="metroCanvas"
                class="metro-canvas"
                @touchend="handleCanvasTouch"></canvas>

      </view>

      <!-- 站点信息面板 -->
      <view v-if="selectedStation"
            class="station-info-panel">
        <view class="station-header">
          <text class="station-name">{{ selectedStation.name }}</text>
          <view class="close-btn"
                @click="closeStationInfo">
            <text>×</text>
          </view>
        </view>

        <view class="station-details">
          <view class="detail-item">
            <text class="label">开通时间：</text>
            <text class="value">{{ selectedStation.openDate }}</text>
          </view>
          <view class="detail-item">
            <text class="label">站台类型：</text>
            <text class="value">{{ selectedStation.platformType }}</text>
          </view>
          <view class="detail-item">
            <text class="label">出入口：</text>
            <text class="value">{{ selectedStation.exits }}个</text>
          </view>
        </view>

        <!-- 周边景点 -->
        <view v-if="selectedStation.attractions && selectedStation.attractions.length > 0"
              class="attractions-section">
          <text class="section-title">周边景点</text>
          <view class="attractions-list">
            <view v-for="attraction in selectedStation.attractions"
                  :key="attraction"
                  class="attraction-tag">
              <text>{{ attraction }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 线路信息 -->
      <view class="line-info"
            v-if="!selectedStation">
        <view class="line-header">
          <view class="line-indicator"></view>
          <text class="line-name">重庆轨道交通2号线</text>
        </view>
        <text class="line-description">连接重庆主城核心区域，途经8个重要旅游站点</text>
        <view class="line-stats">
          <view class="stat-item">
            <text class="stat-label">总长度</text>
            <text class="stat-value">约18.5公里</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">运营时间</text>
            <text class="stat-value">06:30-22:30</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">站点数</text>
            <text class="stat-value">8站</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 站点接口定义
interface Station {
  id: string
  name: string
  x: number
  y: number
  labelOffset: { x: number; y: number }
  openDate: string
  platformType: string
  exits: number
  attractions?: string[]
}

// 选中的站点
const selectedStation = ref<Station | null>(null)

// 站点数据（重庆地铁二号线旅游站点）
const stations = ref<Station[]>([
  {
    id: '2-01',
    name: '较场口',
    x: 200,
    y: 80,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 8,
    attractions: ['较场口夜市', '湖广会馆', '东水门大桥', '洪崖洞'],
  },
  {
    id: '2-02',
    name: '临江门',
    x: 200,
    y: 160,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 4,
    attractions: ['解放碑', '重庆大剧院', '朝天门', '来福士广场'],
  },
  {
    id: '2-03',
    name: '黄花园',
    x: 200,
    y: 240,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 4,
    attractions: ['黄花园大桥', '嘉陵江滨江路', '重庆科技馆'],
  },
  {
    id: '2-04',
    name: '大溪沟',
    x: 200,
    y: 320,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 3,
    attractions: ['重庆人民大礼堂', '三峡博物馆', '人民广场'],
  },
  {
    id: '2-05',
    name: '曾家岩',
    x: 200,
    y: 400,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 4,
    attractions: ['曾家岩书院', '嘉陵江索道', '红岩村'],
  },
  {
    id: '2-06',
    name: '牛角沱',
    x: 200,
    y: 480,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '地下岛式站台',
    exits: 6,
    attractions: ['华新街', '观音桥商圈', '北滨路'],
  },
  {
    id: '2-07',
    name: '李子坝',
    x: 200,
    y: 560,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '高架侧式站台',
    exits: 2,
    attractions: ['李子坝轻轨穿楼', '嘉陵江大桥', '李子坝抗战遗址'],
  },
  {
    id: '2-08',
    name: '佛图关',
    x: 200,
    y: 640,
    labelOffset: { x: 50, y: 0 },
    openDate: '2005年6月18日',
    platformType: '高架岛式站台',
    exits: 2,
    attractions: ['佛图关公园', '嘉陵江观景台', '鹅岭公园'],
  },
])

// 选择站点
const selectStation = (station: Station) => {
  selectedStation.value = station
}

// 关闭站点信息
const closeStationInfo = () => {
  selectedStation.value = null
}

// Canvas绘制函数
const drawMetroLine = () => {
  const ctx = uni.createCanvasContext('metroCanvas')

  // 设置Canvas画布尺寸，确保与逻辑坐标系一致
  // Canvas内部坐标系：400×800
  // DOM显示尺寸：通过CSS的width: 100%, height: 100%自适应容器

  console.log('开始绘制Canvas，内部坐标系: 400×800')

  // 清空画布
  ctx.clearRect(0, 0, 400, 800)

  // 绘制地铁线路背景
  ctx.beginPath()
  ctx.setStrokeStyle('rgba(255, 107, 53, 0.2)')
  ctx.setLineWidth(12)
  ctx.setLineCap('round')
  ctx.moveTo(200, 60)
  ctx.lineTo(200, 660)
  ctx.stroke()

  // 绘制地铁线路主线
  ctx.beginPath()
  ctx.setStrokeStyle('#FF6B35')
  ctx.setLineWidth(6)
  ctx.setLineCap('round')
  ctx.moveTo(200, 60)
  ctx.lineTo(200, 660)
  ctx.stroke()

  // 绘制站点和标签
  stations.value.forEach((station) => {
    console.log(`绘制站点: ${station.name} at (${station.x}, ${station.y})`)

    // 站点阴影
    ctx.beginPath()
    ctx.arc(station.x + 2, station.y + 2, 14, 0, 2 * Math.PI)
    ctx.setFillStyle('rgba(0, 0, 0, 0.1)')
    ctx.fill()

    // 站点外圈
    ctx.beginPath()
    ctx.arc(station.x, station.y, 12, 0, 2 * Math.PI)
    ctx.setFillStyle('white')
    ctx.fill()
    ctx.setStrokeStyle('#FF6B35')
    ctx.setLineWidth(3)
    ctx.stroke()

    // 绘制站点名称背景
    const textWidth = getTextWidth(station.name)
    const labelX = station.x + 24
    const labelY = station.y - 12

    // 站点名称背景矩形
    ctx.beginPath()
    ctx.setFillStyle('rgba(255, 255, 255, 0.95)')
    ctx.fillRect(labelX, labelY, textWidth, 24)
    ctx.setStrokeStyle('rgba(255, 107, 53, 0.2)')
    ctx.setLineWidth(1)
    ctx.strokeRect(labelX, labelY, textWidth, 24)

    // 绘制站点名称文字
    ctx.setFillStyle('#333')
    ctx.setFontSize(14)
    ctx.setTextAlign('left')
    ctx.setTextBaseline('middle')
    ctx.fillText(station.name, labelX + 8, station.y)

    // 绘制景点标识
    if (station.attractions && station.attractions.length > 0) {
      const badgeX = station.x + 12
      const badgeY = station.y - 18

      // 景点标识圆圈
      ctx.beginPath()
      ctx.arc(badgeX, badgeY, 18, 0, 2 * Math.PI)
      ctx.setFillStyle('#FFD700')
      ctx.fill()
      ctx.setStrokeStyle('#FF6B35')
      ctx.setLineWidth(2)
      ctx.stroke()

      // 景点标识文字
      ctx.setFillStyle('#FF6B35')
      ctx.setFontSize(10)
      ctx.setTextAlign('center')
      ctx.setTextBaseline('middle')
      ctx.fillText('景', badgeX, badgeY)
    }
  })

  ctx.draw()
}

// 计算文本宽度
const getTextWidth = (text: string) => {
  // 根据字符数量估算宽度，中文字符约14px，英文字符约8px
  const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
  const otherChars = text.length - chineseChars
  return chineseChars * 14 + otherChars * 8 + 16 // 加16px的padding
}

// 触摸事件处理
const handleCanvasTouch = (e: any) => {
  if (e.type === 'touchend') {
    const touch = e.changedTouches[0]

    // 获取Canvas容器信息，确保触摸坐标转换正确
    uni
      .createSelectorQuery()
      .select('.metro-canvas')
      .boundingClientRect((rect) => {
        if (
          rect &&
          !Array.isArray(rect) &&
          rect.width &&
          rect.height &&
          rect.left !== undefined &&
          rect.top !== undefined
        ) {
          // 关键：Canvas内部坐标系是400×800，DOM显示尺寸是rect.width×rect.height
          // 触摸坐标转换：DOM坐标 → Canvas内部坐标
          const canvasX = ((touch.clientX - rect.left) / rect.width) * 400
          const canvasY = ((touch.clientY - rect.top) / rect.height) * 800

          console.log(
            `触摸点: DOM(${touch.clientX - rect.left}, ${
              touch.clientY - rect.top
            }) → Canvas(${canvasX}, ${canvasY})`
          )
          console.log(`Canvas容器尺寸: ${rect.width} × ${rect.height}`)
          console.log(`Canvas位置: left=${rect.left}, top=${rect.top}`)
          console.log(
            `原始触摸坐标: clientX=${touch.clientX}, clientY=${touch.clientY}`
          )

          // 检查是否点击了某个站点
          stations.value.forEach((station) => {
            // 检查是否点击了站点圆圈
            const circleDistance = Math.sqrt(
              Math.pow(canvasX - station.x, 2) +
                Math.pow(canvasY - station.y, 2)
            )

            // 检查是否点击了站点名称区域
            const textWidth = getTextWidth(station.name)
            const labelX = station.x + 24
            const labelY = station.y - 12
            const inNameArea =
              canvasX >= labelX &&
              canvasX <= labelX + textWidth &&
              canvasY >= labelY &&
              canvasY <= labelY + 24

            // 检查是否点击了景点标识
            let inAttractionArea = false
            if (station.attractions && station.attractions.length > 0) {
              const badgeX = station.x + 12
              const badgeY = station.y - 18
              const badgeDistance = Math.sqrt(
                Math.pow(canvasX - badgeX, 2) + Math.pow(canvasY - badgeY, 2)
              )
              inAttractionArea = badgeDistance <= 18
            }

            // 如果点击了任何相关区域，选中站点
            if (circleDistance <= 20 || inNameArea || inAttractionArea) {
              console.log(`选中站点: ${station.name}, 距离: ${circleDistance}`)
              selectStation(station)
            }
          })
        }
      })
      .exec()
  }
}

// 页面挂载后绘制地铁线路
onMounted(() => {
  setTimeout(() => {
    drawMetroLine()
  }, 100)
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.chongqing-metro-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 顶部标题栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40rpx;
  z-index: 1000;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  position: absolute;
  left: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 32rpx;
  color: #333;
}

/* 地图容器 */
.map-container {
  position: fixed;
  top: 88rpx;
  left: 0;
  right: 0;
  bottom: 220rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.metro-wrapper {
  position: relative;
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  /* 强制1:2的宽高比，与Canvas逻辑坐标系一致 */
  aspect-ratio: 1 / 2;
  max-height: 100%;
  margin: 0 auto;
}

.metro-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* 动画效果 */
@keyframes stationPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes attractionBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3rpx);
  }
  60% {
    transform: translateY(-1rpx);
  }
}

/* 站点信息面板 */
.station-info-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  max-height: 60vh;
  overflow-y: auto;
}

.station-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.station-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  font-size: 32rpx;
  color: #666;
}

.station-details {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  min-width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.attractions-section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.attractions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attraction-tag {
  background: linear-gradient(135deg, #ff6b35, #ff8a65);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

/* 线路信息 */
.line-info {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 998;
  max-height: 180rpx;
}

.line-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.line-indicator {
  width: 8rpx;
  height: 40rpx;
  background: #ff6b35;
  border-radius: 4rpx;
  margin-right: 20rpx;
}

.line-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.line-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.line-stats {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b35;
}

/* SVG动画 */
@keyframes attractionPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
</style>
