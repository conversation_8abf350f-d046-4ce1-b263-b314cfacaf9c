<template>
  <view class="container">
    <!-- WebView 组件 -->
    <web-view :src="url"
              @message="handleMessage"></web-view>

    <!-- 调试信息浮层 -->
    <view class="debug-info"
          v-if="true"
          style="position: fixed; top: 100px; right: 10px; font-size: 12px; color: white; background: rgba(0,0,0,0.7); padding: 5px; border-radius: 3px; z-index: 9999;">
      {{ isChongqingPage ? '绿色模式' : '红色模式' }}
      <br>{{ navTitle }}
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      url: '',
      statusBarHeight: 20, // 默认状态栏高度
      isChongqingPage: false, // 是否是重庆页面
      navTitle: '云游文化', // 默认标题
      originalUrl: '', // 保存原始URL
      checkTimer: null // 定时检查器
    }
  },
  watch: {
    isChongqingPage (newVal, oldVal) {
      console.log('🔄 isChongqingPage 发生变化:', oldVal, '->', newVal);
      console.log('当前时间:', new Date().toLocaleTimeString());
      console.log('调用栈:', new Error().stack);
    }
  },
  onLoad (options) {
    // 获取状态栏高度
    this.getStatusBarHeight();

    if (options && options.url) {
      try {
        this.url = decodeURIComponent(options.url);
        this.originalUrl = this.url; // 保存原始URL
        console.log('加载外部页面:', this.url);

        // 根据URL设置导航栏颜色和标题
        this.setNavBarStyle(this.url);

        // 启动定时检查，防止样式被重置
        this.startStyleCheck();

      } catch (e) {
        console.error('URL解码失败:', e);
        this.url = options.url;
        this.originalUrl = this.url; // 保存原始URL
        console.log('使用原始URL:', this.url);
        this.setNavBarStyle(this.url);

        // 启动定时检查
        this.startStyleCheck();
      }
    } else {
      console.error('未提供URL参数');
      // 如果没有提供URL，显示错误信息并返回
      uni.showToast({
        title: '链接无效',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 根据URL设置导航栏样式
    setNavBarStyle (url) {
      console.log('=== 导航栏样式设置 ===');
      console.log('传入的URL:', url);
      console.log('URL类型:', typeof url);

      // 检查是否是重庆地铁相关的URL
      const keywords = ['chongqing', '重庆', '地铁', '轨道'];
      const matchedKeywords = keywords.filter(keyword => url && url.includes(keyword));

      console.log('匹配的关键词:', matchedKeywords);

      if (url && matchedKeywords.length > 0) {
        this.isChongqingPage = true;
        this.navTitle = '乘着轨道游重庆';
        console.log('✅ 检测到重庆页面，设置绿色导航栏');
        console.log('isChongqingPage:', this.isChongqingPage);

        // 使用原生API设置导航栏
        this.setNativeNavBar('#00a651', '乘着轨道游重庆');

      } else {
        this.isChongqingPage = false;
        this.navTitle = '云游文化';
        console.log('❌ 普通页面，使用默认红色导航栏');
        console.log('isChongqingPage:', this.isChongqingPage);

        // 使用原生API设置导航栏
        this.setNativeNavBar('#ba0001', '云游文化');
      }
      console.log('最终标题:', this.navTitle);
      console.log('=== 设置完成 ===');
    },

    handleMessage (event) {
      console.log('接收到web-view消息:', event);
      console.log('webview消息时的isChongqingPage:', this.isChongqingPage);
    },

    // 设置原生导航栏
    setNativeNavBar (backgroundColor, title) {
      console.log('🎨 设置原生导航栏:', { backgroundColor, title });

      // 设置导航栏标题
      uni.setNavigationBarTitle({
        title: title,
        success: () => {
          console.log('✅ 导航栏标题设置成功:', title);
        },
        fail: (err) => {
          console.error('❌ 导航栏标题设置失败:', err);
        }
      });

      // 设置导航栏颜色
      uni.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: backgroundColor,
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        },
        success: () => {
          console.log('✅ 导航栏颜色设置成功:', backgroundColor);
        },
        fail: (err) => {
          console.error('❌ 导航栏颜色设置失败:', err);
        }
      });
    },

    // 返回上一页
    goBack () {
      uni.navigateBack();
    },
    // 获取状态栏高度
    getStatusBarHeight () {
      // #ifdef APP-PLUS || MP-WEIXIN
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;
      // #endif
    },

    // 启动样式检查定时器
    startStyleCheck () {
      // 清除之前的定时器
      if (this.checkTimer) {
        clearInterval(this.checkTimer);
      }

      // 每2秒检查一次样式状态
      this.checkTimer = setInterval(() => {
        const shouldBeChongqing = this.originalUrl && (
          this.originalUrl.includes('chongqing') ||
          this.originalUrl.includes('重庆') ||
          this.originalUrl.includes('地铁') ||
          this.originalUrl.includes('轨道')
        );

        if (shouldBeChongqing && !this.isChongqingPage) {
          console.log('🔧 检测到样式被重置，重新设置为重庆样式');
          this.isChongqingPage = true;
          this.navTitle = '乘着轨道游重庆';
          this.setNativeNavBar('#00a651', '乘着轨道游重庆');
        }
      }, 2000);

      console.log('✅ 样式检查定时器已启动');
    },

    // 停止样式检查
    stopStyleCheck () {
      if (this.checkTimer) {
        clearInterval(this.checkTimer);
        this.checkTimer = null;
        console.log('🛑 样式检查定时器已停止');
      }
    }
  },

  // 页面销毁时清理定时器
  onUnload () {
    this.stopStyleCheck();
  }
}
</script>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* WebView 样式 */
web-view {
  flex: 1;
  width: 100%;
}

/* 调试信息样式 */
.debug-info {
  pointer-events: none;
}
</style>