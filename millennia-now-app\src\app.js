// app.js
// 注意：这个文件需要改为TypeScript或者在Vue文件中使用统一配置
// 建议将baseURL的获取移到具体的API调用处，使用统一的配置管理
App({
  globalData: {
    userInfo: null,
    // baseURL现在通过统一配置管理，不在这里硬编码
    // 如果需要在globalData中使用，请在具体使用时调用getBaseURL()
  },
  onLaunch() {
    console.log('App Launch')

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.globalData.systemInfo = systemInfo

    // 环境判断现在由统一配置管理，不在这里处理

    // 检查登录状态
    this.checkLoginStatus()
  },
  onShow() {
    console.log('App Show')
  },
  onHide() {
    console.log('App Hide')
  },
  
  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('access_token')
    if (token) {
      this.globalData.isLoggedIn = true
    } else {
      this.globalData.isLoggedIn = false
    }
  }
})