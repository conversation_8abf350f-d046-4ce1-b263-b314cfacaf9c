<template>
  <view class="select-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/location/select</text>
      <text>分类: location</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/location/select')
})
</script>

<style scoped>
.location-select-container[data-v-a466e873]{height:100vh;background-color:#f5f5f5;display:flex;flex-direction:column}.header[data-v-a466e873]{display:flex;align-items:center;justify-content:space-between;background-color:#fff;padding:.625rem .9375rem;border-bottom:.03125rem solid #efefef;position:relative;z-index:100}.header-left[data-v-a466e873],.header-right[data-v-a466e873]{width:2.5rem}.back-btn[data-v-a466e873]{width:1.875rem;height:1.875rem;border-radius:50%;background-color:#f5f5f5;display:flex;align-items:center;justify-content:center;transition:background-color .2s}.back-btn[data-v-a466e873]:active{background-color:#e0e0e0}.back-icon[data-v-a466e873]{font-size:1.25rem;color:#333;font-weight:700}.header-title[data-v-a466e873]{font-size:1rem;font-weight:700;color:#333}.content-area[data-v-a466e873]{flex:1;display:flex;flex-direction:column;overflow:hidden;padding-bottom:4.6875rem}.current-location[data-v-a466e873]{display:flex;align-items:center;justify-content:space-between;background-color:#fff;padding:.9375rem;margin-bottom:.625rem;flex-shrink:0}.location-info[data-v-a466e873]{display:flex;align-items:center}.location-icon[data-v-a466e873]{font-size:1rem;margin-right:.46875rem}.location-text[data-v-a466e873]{font-size:.875rem;color:#333;font-weight:700}.location-detail[data-v-a466e873]{flex:1;text-align:center}.location-name[data-v-a466e873]{font-size:.8125rem;color:#666}.location-loading[data-v-a466e873]{font-size:.8125rem;color:#999}.location-arrow[data-v-a466e873]{font-size:.75rem;color:#999}.use-location-btn[data-v-a466e873]{background-color:#c8161e;border-radius:1rem;padding:.25rem .625rem;margin-left:.625rem}.use-btn-text[data-v-a466e873]{font-size:.75rem;color:#fff;font-weight:700}.selector-container[data-v-a466e873]{flex:1;display:flex;background-color:#fff;overflow:hidden}.selector-section[data-v-a466e873]{flex:1;display:flex;flex-direction:column;border-right:.03125rem solid #efefef}.selector-section[data-v-a466e873]:last-child{border-right:none}.selector-title[data-v-a466e873]{font-size:.875rem;font-weight:700;color:#333;text-align:center;padding:.625rem 0;border-bottom:.03125rem solid #efefef;background-color:#f8f8f8}.selector-list[data-v-a466e873]{flex:1;height:0}.selector-item[data-v-a466e873]{display:flex;align-items:center;justify-content:space-between;padding:.78125rem .625rem;border-bottom:.03125rem solid #f5f5f5}.selector-item.active[data-v-a466e873]{background-color:#fff3f3}.item-text[data-v-a466e873]{font-size:.8125rem;color:#333;flex:1}.selector-item.active .item-text[data-v-a466e873]{color:#c8161e}.item-check[data-v-a466e873]{font-size:.875rem;color:#c8161e;font-weight:700}.confirm-container[data-v-a466e873]{padding:.9375rem;background-color:#fff;border-top:.03125rem solid #efefef;position:fixed;bottom:0;left:0;right:0;z-index:100;box-shadow:0 -.0625rem .3125rem rgba(0,0,0,.1)}.confirm-btn[data-v-a466e873]{width:100%;height:2.75rem;background-color:#c8161e;color:#fff;font-size:1rem;font-weight:700;border-radius:1.375rem;border:none;display:flex;align-items:center;justify-content:center;transition:background-color .2s}.confirm-btn[data-v-a466e873]:active{background-color:#a01419}.confirm-btn[disabled][data-v-a466e873]{background-color:#ccc}.loading-mask[data-v-a466e873]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.3);display:flex;align-items:center;justify-content:center;z-index:1000}.loading-content[data-v-a466e873]{background-color:#fff;padding:1.25rem 1.875rem;border-radius:.375rem}.loading-text[data-v-a466e873]{font-size:.875rem;color:#333}

</style>
