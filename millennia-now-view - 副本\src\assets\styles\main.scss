/* 全局样式 */
:root {
  --primary-color: #C8161E;
  --secondary-color: #333;
  --text-color: #333;
  --bg-color: #f5f5f5;
  --accent-color: #8B4513;
  --light-color: #fff;
  --dark-color: #000;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: 'KaiTi', 'STKaiti', serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 16px;
  color: var(--text-color);
  background-color: var(--bg-color);
  overflow: hidden;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &.primary {
    background-color: var(--primary-color);
    color: var(--light-color);
    
    &:hover {
      background-color: darken(#C8161E, 10%);
    }
  }
  
  &.secondary {
    background-color: var(--secondary-color);
    color: var(--light-color);
    
    &:hover {
      background-color: lighten(#333, 10%);
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

/* 3D场景相关样式 */
.scene-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 界面元素样式 */
.ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  
  & > * {
    pointer-events: auto;
  }
}

/* 加载指示器样式 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--light-color);
  z-index: 9999;
}

/* 响应式布局 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
} 