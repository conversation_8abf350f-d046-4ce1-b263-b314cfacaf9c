<template>
  <view class="memory-detail-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/culture/memory-detail</text>
      <text>分类: culture</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/culture/memory-detail')
})
</script>

<style scoped>
.memory-detail-container[data-v-3c04d2ca]{min-height:100vh;background-color:#f8f9fa}.loading-container[data-v-3c04d2ca]{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:60vh}.loading-spinner[data-v-3c04d2ca]{width:1.875rem;height:1.875rem;border:.1875rem solid #f3f3f3;border-top:.1875rem solid #007aff;border-radius:50%;animation:spin-3c04d2ca 1s linear infinite}.loading-text[data-v-3c04d2ca]{margin-top:.625rem;font-size:.875rem;color:#666}@keyframes spin-3c04d2ca{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.detail-content[data-v-3c04d2ca]{background-color:#fff}.detail-content-section[data-v-3c04d2ca]{background-color:#fff;margin:.625rem;border-radius:.375rem;overflow:hidden}.detail-content-container[data-v-3c04d2ca]{padding:.9375rem;line-height:1.8;font-size:.875rem;color:#333}.header-image-container[data-v-3c04d2ca]{position:relative;width:100%;height:12.5rem;overflow:hidden}.header-image[data-v-3c04d2ca]{width:100%;height:100%}.image-overlay[data-v-3c04d2ca]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,rgba(0,0,0,.7));padding:1.875rem .9375rem .9375rem}.memory-title[data-v-3c04d2ca]{display:block;font-size:1.125rem;font-weight:700;color:#fff;margin-bottom:.3125rem}.memory-year[data-v-3c04d2ca]{display:block;font-size:.875rem;color:rgba(255,255,255,.8)}.info-section[data-v-3c04d2ca]{padding:.9375rem}.section-title[data-v-3c04d2ca]{margin-bottom:.9375rem}.title-text[data-v-3c04d2ca]{display:block;font-size:1.125rem;font-weight:700;color:#333;margin-bottom:.3125rem}.year-text[data-v-3c04d2ca]{display:block;font-size:.875rem;color:#c8161e;background-color:#fff0f0;padding:.25rem .5rem;border-radius:.1875rem;display:inline-block}.description-content[data-v-3c04d2ca]{margin-top:.9375rem}.description-text[data-v-3c04d2ca]{font-size:.9375rem;color:#333;line-height:1.8}.images-section[data-v-3c04d2ca]{padding:.9375rem;background-color:#f8f9fa}.section-header[data-v-3c04d2ca]{margin-bottom:.9375rem}.section-title-text[data-v-3c04d2ca]{font-size:1rem;font-weight:700;color:#333}.images-grid[data-v-3c04d2ca]{display:grid;grid-template-columns:repeat(2,1fr);gap:.625rem}.image-item[data-v-3c04d2ca]{border-radius:.375rem;overflow:hidden;box-shadow:0 .125rem .375rem rgba(0,0,0,.1)}.grid-image[data-v-3c04d2ca]{width:100%;height:9.375rem}.footer-info[data-v-3c04d2ca]{padding:.9375rem;text-align:center;background-color:#f8f9fa}.update-time[data-v-3c04d2ca]{font-size:.75rem;color:#999}.error-container[data-v-3c04d2ca]{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:60vh;padding:1.25rem}.error-text[data-v-3c04d2ca]{font-size:1rem;color:#666;margin-bottom:1.25rem}.retry-btn[data-v-3c04d2ca]{padding:.625rem 1.25rem;background-color:#007aff;color:#fff;border:none;border-radius:.25rem;font-size:.875rem}

</style>
