<template>
  <view class="timeline-edit-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/culture/timeline-edit</text>
      <text>分类: culture</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/culture/timeline-edit')
})
</script>

<style scoped>
.container{background-color:#f8f9fa;min-height:100vh}.form-container{padding-bottom:1.25rem}.form-section{background-color:#fff;margin:.625rem;border-radius:.375rem;padding:.9375rem}.section-title{font-size:1rem;font-weight:700;color:#333;margin-bottom:.9375rem;padding-bottom:.46875rem;border-bottom:.0625rem solid #f0f0f0}.form-item{margin-bottom:1.25rem}.form-item:last-child{margin-bottom:0}.form-label{display:block;font-size:.875rem;color:#333;margin-bottom:.46875rem}.required{color:#f44;font-weight:700}.form-label-with-button{display:flex;justify-content:space-between;align-items:center;margin-bottom:.46875rem}.template-btn{background-color:#007aff;color:#fff;padding:.25rem .5rem;border-radius:.1875rem;font-size:.75rem;border:none}.form-input,.form-textarea{width:100%;height:auto;padding:.625rem;border:1px solid #ddd;border-radius:.25rem;font-size:.875rem;color:#333;background-color:#fff;box-sizing:border-box}.form-textarea{min-height:3.75rem;resize:none;line-height:1.6}.form-desc{font-size:.75rem;color:#999;margin-top:.3125rem;margin-left:.625rem}.image-upload{border:.0625rem dashed #ddd;border-radius:.25rem;overflow:hidden}.image-preview{position:relative}.preview-image{width:100%;height:9.375rem}.image-actions{position:absolute;bottom:0;left:0;right:0;background:rgba(0,0,0,.7);padding:.46875rem;display:flex;justify-content:center;align-items:center;gap:.625rem}.action-btn{color:#fff;font-size:.75rem;padding:.3125rem .625rem;border-radius:.1875rem;background:rgba(255,255,255,.2);border:none;cursor:pointer;white-space:nowrap;min-width:2.5rem;text-align:center}.action-btn.delete{background:rgba(255,59,48,.8)}.upload-btn{display:flex;flex-direction:column;align-items:center;justify-content:center;height:6.25rem;color:#999}.upload-btn.disabled{opacity:.6;pointer-events:none}.upload-icon{font-size:1.875rem;margin-bottom:.3125rem}.upload-text{font-size:.8125rem}.detail-images{display:flex;flex-wrap:wrap;gap:.625rem}.detail-image-item{position:relative;width:6.25rem;height:4.6875rem}.detail-image{width:100%;height:100%;border-radius:.25rem}.detail-image-actions{position:absolute;top:.15625rem;right:.15625rem}.add-detail-image{width:6.25rem;height:4.6875rem;border:.0625rem dashed #ddd;border-radius:.25rem;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#999}.add-detail-image.disabled{opacity:.6;pointer-events:none}.add-icon{font-size:1.25rem;margin-bottom:.15625rem}.add-text{font-size:.75rem}.tags-container{display:flex;flex-wrap:wrap;gap:.46875rem;margin-bottom:.625rem}.tag-item{padding:.3125rem .625rem;background-color:#f8f9fa;color:#666;border-radius:.9375rem;font-size:.75rem;border:1px solid #e9ecef}.tag-item.active{background-color:#007aff;color:#fff;border-color:#007aff}.selected-tags-container{display:flex;flex-wrap:wrap;gap:.46875rem;margin-bottom:.625rem}.selected-tag-item{display:flex;align-items:center;padding:.3125rem .625rem;background-color:#e6f7ff;border:1px solid #91d5ff;border-radius:.9375rem;font-size:.75rem}.tag-text{color:#1890ff;margin-right:.25rem}.remove-tag-btn{color:#ff4d4f;font-size:.875rem;font-weight:700;line-height:1;padding:0 .125rem;border-radius:50%;transition:background-color .3s ease}.remove-tag-btn:active{background-color:rgba(255,77,79,.1)}.custom-tag-input{display:flex;gap:.46875rem;align-items:center}.custom-tag-input .form-input{flex:1}.add-tag-btn{padding:.625rem .9375rem;background-color:#007aff;color:#fff;border:none;border-radius:.25rem;font-size:.875rem}.add-tag-btn:disabled{background-color:#ccc;color:#999}.save-section{padding:.9375rem .625rem}.save-button{width:100%;height:2.75rem;background-color:#007aff;color:#fff;border:none;border-radius:.375rem;font-size:1rem;font-weight:700}.save-button:disabled{background-color:#ccc;color:#999}.loading-container{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.8);display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:1000}.loading-spinner{width:1.875rem;height:1.875rem;border:.1875rem solid #f3f3f3;border-top:.1875rem solid #007aff;border-radius:50%;animation:spin 1s linear infinite;margin-bottom:.625rem}.loading-text{font-size:.875rem;color:#666}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.fullscreen-modal{position:fixed!important;top:0!important;left:0!important;right:0!important;bottom:0!important;width:100vw!important;height:100vh!important;background-color:rgba(0,0,0,.8)!important;z-index:9999!important;display:flex!important;justify-content:center!important;align-items:center!important;overflow:hidden!important}.template-modal{background-color:#fff!important;border-radius:.625rem!important;width:90%!important;max-width:25rem!important;max-height:85vh!important;min-height:70vh!important;overflow:hidden!important;display:flex!important;flex-direction:column!important;box-shadow:0 .3125rem .9375rem rgba(0,0,0,.3)!important;position:relative!important;z-index:10000!important}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:.9375rem;border-bottom:1px solid #eee;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.modal-title{font-size:1rem;font-weight:700;color:#fff}.template-count{font-size:.75rem;font-weight:400;opacity:.8;margin-left:.3125rem}.close-btn{font-size:1.25rem;color:rgba(255,255,255,.8);line-height:1;padding:.3125rem;border-radius:50%;transition:background-color .2s}.close-btn:active{background-color:rgba(255,255,255,.2)}.template-list-container{flex:1!important;height:calc(85vh - 6.25rem)!important;overflow:hidden!important;background-color:#fff!important}.native-scroll{overflow-y:auto!important;padding:.625rem!important;box-sizing:border-box!important}.template-list{width:100%!important;height:calc(85vh - 6.25rem)!important;padding:.625rem!important;background-color:#fff!important;box-sizing:border-box!important}.template-item{background-color:#fff;border:1px solid #e9ecef;border-radius:.375rem;padding:.625rem;margin-bottom:.46875rem;position:relative}.template-item:active{background-color:#f8f9fa;border-color:#007aff;box-shadow:0 .0625rem .25rem rgba(0,122,255,.15)}.template-name{font-size:.9375rem;font-weight:700;color:#333;display:block;margin-bottom:.3125rem}.template-desc{font-size:.8125rem;color:#666;display:block;margin-bottom:.46875rem}.template-preview{font-size:.6875rem;color:#888;background-color:#f8f9fa;padding:.46875rem;border-radius:.25rem;border-left:.1875rem solid #007aff;overflow:hidden;max-height:1.875rem;line-height:1.4}.search-section{padding:.625rem;border-bottom:1px solid #eee;background-color:#f8f9fa}.search-input{width:100%;height:2.1875rem;padding:0 .625rem;border:1px solid #ddd;border-radius:1.09375rem;font-size:.875rem;background-color:#fff}.search-input:focus{border-color:#007aff;box-shadow:0 0 0 .0625rem rgba(0,122,255,.1)}.no-results{text-align:center;padding:1.875rem .625rem;color:#999}.no-results-text{display:block;font-size:.9375rem;margin-bottom:.3125rem}.no-results-desc{display:block;font-size:.75rem;color:#ccc}.modal-actions{display:flex;border-top:1px solid #eee;background-color:#f8f9fa}.cancel-btn{flex:1;padding:.9375rem;border:none;font-size:.875rem;text-align:center;background-color:transparent;color:#666}.native-scroll::-webkit-scrollbar,.template-list::-webkit-scrollbar{width:.25rem}.native-scroll::-webkit-scrollbar-track,.template-list::-webkit-scrollbar-track{background:#f1f1f1;border-radius:.125rem}.native-scroll::-webkit-scrollbar-thumb,.template-list::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:.125rem}.native-scroll::-webkit-scrollbar-thumb:hover,.template-list::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.content-hidden{display:none!important}

</style>
