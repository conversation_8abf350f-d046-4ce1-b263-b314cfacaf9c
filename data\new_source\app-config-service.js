
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","navigationBar":{"titleText":"文旅小程序","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"千载·今知","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#C8161E","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","text":"首页"},{"pagePath":"pages/zhiyin/index","text":"知音阁"},{"pagePath":"pages/user/index","text":"我的"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"首页","type":"default"},"isNVue":false}},{"path":"pages/zhiyin/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#C8161E","titleText":"知音阁","type":"default"},"isNVue":false}},{"path":"pages/user/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"backgroundColor":"#C8161E","titleText":"个人中心","type":"default"},"isNVue":false}},{"path":"pages/location/select","meta":{"navigationBar":{"titleText":"选择城市","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/webview/index","meta":{"navigationBar":{"backgroundColor":"#ba0001","titleText":"云游文化","style":"default","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/culture/history","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"历史文脉","type":"default"},"isNVue":false}},{"path":"pages/culture/chongqing","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"乘着轨道游重庆","type":"default"},"isNVue":false}},{"path":"pages/culture/heritage","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"文化管理","style":"default","type":"default"},"isNVue":false}},{"path":"pages/culture/place-edit","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"地点编辑","type":"default"},"isNVue":false}},{"path":"pages/culture/timeline-edit","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"时间轴编辑","type":"default"},"isNVue":false}},{"path":"pages/culture/heritage-edit","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"文化遗产编辑","type":"default"},"isNVue":false}},{"path":"pages/culture/heritage-detail","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"文化遗产详情","type":"default"},"isNVue":false}},{"path":"pages/culture/heritage-content","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"文化传承","type":"default"},"isNVue":false}},{"path":"pages/culture/memory-edit","meta":{"navigationBar":{"backgroundColor":"#C8161E","titleText":"城市记忆编辑","type":"default"},"isNVue":false}},{"path":"pages/culture/memory-detail","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"城市记忆详情","type":"default"},"isNVue":false}},{"path":"pages/culture/memory-content","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"当代城市记忆","type":"default"},"isNVue":false}},{"path":"pages/culture/index","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#C8161E","titleText":"文化管理","type":"default"},"isNVue":false}},{"path":"pages/rail/cq-line2","meta":{"enablePullDownRefresh":false,"navigationBar":{"backgroundColor":"#00a651","titleText":"重庆轨道2号线","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/admin/users","meta":{"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#C8161E","titleText":"用户管理","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  