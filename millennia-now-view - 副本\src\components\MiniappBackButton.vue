<template>
  <div v-if="isFromMiniapp" class="miniapp-back-button" @click="backToMiniapp">
    <span class="back-icon">←</span> 返回
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 为微信小程序环境添加类型声明
declare global {
  interface Window {
    __wxjs_environment?: string;
    wx?: {
      miniProgram?: {
        navigateBack: (options?: { delta?: number }) => void;
        redirectTo: (options: { url: string }) => void;
      }
    }
  }
}

const router = useRouter();
const isFromMiniapp = ref(false);

// 返回到小程序
const backToMiniapp = () => {
  // 检查是否在小程序环境中
  if (window.__wxjs_environment === 'miniprogram' || isFromMiniapp.value) {
    try {
      // 尝试使用小程序API返回
      if (window.wx && window.wx.miniProgram) {
        window.wx.miniProgram.navigateBack();
      } else {
        console.log('未找到小程序API，尝试使用history返回');
        window.history.go(-1);
      }
    } catch (error) {
      console.error('返回小程序失败:', error);
      window.history.go(-1);
    }
  } else {
    // 普通环境下的返回
    router.back();
  }
};

onMounted(() => {
  // 检查是否从小程序进入
  const urlParams = new URLSearchParams(window.location.search);
  const fromParam = urlParams.get('from') === 'miniapp';
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true';
  
  isFromMiniapp.value = fromParam || fromStorage;
});
</script>

<style scoped lang="scss">
.miniapp-back-button {
  position: fixed;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  z-index: 100;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .back-icon {
    margin-right: 5px;
    font-size: 16px;
  }
}
</style> 