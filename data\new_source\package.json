{"name": "millennia-now-app", "version": "1.0.0", "description": "千载·今知 - 文化传承小程序", "main": "main.ts", "scripts": {"build:app": "uni build --platform app", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin", "dev:app": "uni dev --platform app", "dev:h5": "uni dev --platform h5", "dev:mp-weixin": "uni dev --platform mp-weixin", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-app-plus": "^3.0.0", "vue": "^3.3.0", "pinia": "^2.1.0"}, "devDependencies": {"@dcloudio/types": "^3.4.0", "@dcloudio/uni-automator": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "@types/node": "^20.0.0", "@vue/tsconfig": "^0.4.0", "sass": "^1.69.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vue-tsc": "^1.8.0"}, "uni-app": {"scripts": {}}}