# 当代记忆展厅真实数据接入优化说明

## 🎯 **优化目标**

将当代记忆展厅从模拟数据改为接入真实的后端API数据，提升数据的准确性和实时性。

## 🔧 **主要优化内容**

### 1. **统一配置管理**

#### **新增文件**
- `src/config/api.ts` - 统一的API配置管理
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

#### **功能特性**
- 环境变量支持
- 统一的超时配置
- 图片代理开关
- 调试模式控制

```typescript
// 使用示例
import { getBaseURL, getImageProxyUrl, isDebugMode } from '../config/api'

const apiUrl = getBaseURL() // 自动根据环境返回对应的API地址
const imageUrl = getImageProxyUrl('/path/to/image.jpg') // 自动处理图片代理
```

### 2. **数据处理工具**

#### **新增文件**
- `src/utils/dataProcessor.ts` - 数据验证和转换工具

#### **功能特性**
- 数据验证和清洗
- 统一的数据格式转换
- 图片URL处理
- 年份解析和排序
- 数据统计分析

```typescript
// 使用示例
import { convertMemoryArrayToExhibits, sortExhibitsByYear, getDataStats } from '../utils/dataProcessor'

const exhibits = convertMemoryArrayToExhibits(rawData) // 转换数据格式
const sortedExhibits = sortExhibitsByYear(exhibits) // 按年份排序
const stats = getDataStats(exhibits) // 获取统计信息
```

### 3. **API接口优化**

#### **优化内容**
- 增强错误处理和重试机制
- 添加详细的调试日志
- 优化图片URL处理
- 统一超时配置

#### **API调用流程**
1. 首先尝试标准API (`/heritage/places/{id}/memory`)
2. 如果返回空数据，尝试完整页面数据API (`/heritage/places/by-region/full`)
3. 如果所有API都失败，使用默认数据作为备选

### 4. **数据加载优化**

#### **加载进度显示**
- 添加了详细的加载进度指示器 (0-100%)
- 分阶段显示加载状态

#### **数据处理流程**
```
开始加载 (0%) 
→ 获取区域信息 (20%) 
→ 调用API (40%) 
→ 数据转换 (60%) 
→ 排序和统计 (80%) 
→ 完成 (100%)
```

### 5. **调试和监控**

#### **调试信息**
- 详细的控制台日志（带emoji标识）
- 数据统计信息输出
- 错误追踪和报告

#### **日志示例**
```
🔍 获取当代记忆数据，参数: {province_id: 51, city_id: 4, district_id: 21}
📍 地点信息响应: {id: 123, name: "成都市锦江区"}
🎯 获取地点ID为 123 的当代记忆数据
✅ 当代记忆数据响应: {items: [...], total: 15}
📊 当代记忆数据统计: {总数: 15, 有图片: 12, 有详细图片: 8, 年份范围: "2010-2024"}
🎉 当代记忆数据加载成功: 15 条记录
```

## 🚀 **使用方法**

### 1. **环境配置**

#### **开发环境**
```bash
# .env.development
VITE_API_BASE_URL=http://*************:8000/api/
VITE_IMAGE_PROXY_ENABLED=true
VITE_DEBUG_MODE=true
```

#### **生产环境**
```bash
# .env.production
VITE_API_BASE_URL=https://your-production-api.com/api/
VITE_IMAGE_PROXY_ENABLED=true
VITE_DEBUG_MODE=false
```

### 2. **启动项目**

```bash
# 开发模式
npm run dev

# 生产构建
npm run build
```

### 3. **访问展厅**

```
http://localhost:5173/memory-gallery?province_id=51&city_id=4&district_id=21
```

## 📊 **数据格式**

### **输入数据格式（API返回）**
```typescript
interface MemoryDataItem {
  id: number
  title: string
  description?: string
  image?: string
  year?: string | number
  detail_content?: string
  detail_images?: string[]
  created_at?: string
  updated_at?: string
  place_id?: number
  is_active?: boolean
  sort_order?: number
}
```

### **展品数据格式（展厅使用）**
```typescript
interface ExhibitData {
  id: number
  title: string
  description: string
  image: string
  year: string | number
  period: string
  content: string
  detail_images: string[]
  memory_type: string
  significance: string
  // ... 其他字段
}
```

## 🔍 **故障排除**

### **常见问题**

1. **API连接失败**
   - 检查 `.env` 文件中的API地址配置
   - 确认后端服务是否正常运行
   - 查看浏览器控制台的网络请求

2. **图片显示异常**
   - 检查图片代理配置
   - 确认图片URL格式是否正确
   - 查看控制台的图片加载错误

3. **数据为空**
   - 检查区域ID参数是否正确
   - 确认后端数据库中是否有对应的数据
   - 查看API响应内容

### **调试方法**

1. **开启调试模式**
   ```bash
   VITE_DEBUG_MODE=true
   ```

2. **查看控制台日志**
   - 数据加载过程
   - API调用详情
   - 错误信息

3. **检查网络请求**
   - 浏览器开发者工具 → Network
   - 查看API请求和响应

## ✅ **优化效果**

- ✅ 真实数据接入，提升内容准确性
- ✅ 统一配置管理，便于环境切换
- ✅ 完善的错误处理，提升系统稳定性
- ✅ 详细的调试信息，便于问题排查
- ✅ 数据验证和清洗，确保数据质量
- ✅ 图片代理支持，解决跨域问题
- ✅ 加载进度显示，提升用户体验

## 🔄 **后续优化建议**

1. **缓存机制** - 添加数据缓存，减少API调用
2. **离线支持** - 支持离线模式，提升可用性
3. **数据预加载** - 预加载相关数据，提升响应速度
4. **错误重试** - 添加自动重试机制
5. **性能监控** - 添加性能监控和分析
