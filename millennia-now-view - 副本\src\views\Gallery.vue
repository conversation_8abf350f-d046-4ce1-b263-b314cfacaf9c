<template>
  <div class="gallery-container">
    <!-- 3D场景容器 -->
    <div class="scene-container">
      <canvas ref="sceneCanvas"></canvas>
    </div>
    
    <!-- 用户界面元素 -->
    <div class="ui-container">
      <!-- 使用小程序返回按钮组件 -->
      <MiniappBackButton />
      
      <!-- 普通返回按钮（当不是从小程序进入时显示） -->
      <div class="back-button" @click="goBack" v-if="!isFromMiniapp">
        <span class="icon">←</span>
        <span class="text">返回</span>
      </div>
      
      <!-- 展厅标题 -->
      <div class="gallery-title">
        <h1>{{ galleryTitle }}</h1>
        <p>{{ galleryDescription }}</p>
      </div>
      
      <!-- 加载进度指示器 -->
      <div class="loading-progress" v-if="loading">
        <div class="loading-text">加载中 {{ Math.floor(loadingProgress) }}%</div>
        <div class="progress-bar">
          <div class="progress" :style="{ width: loadingProgress + '%' }"></div>
        </div>
      </div>
      
      <!-- 展品信息面板 -->
      <div class="exhibit-panel" v-if="selectedExhibit" :class="{ 'active': selectedExhibit }">
        <div class="panel-header">
          <h2>{{ selectedExhibit.title }}</h2>
          <button class="close-btn" @click="closeExhibitPanel">×</button>
        </div>
        <div class="panel-content">
          <img :src="selectedExhibit.image" alt="展品图片" class="exhibit-image">
          <div class="exhibit-info">
            <p class="description">{{ selectedExhibit.description }}</p>
            <p class="metadata"><strong>年代:</strong> {{ selectedExhibit.year }}</p>
            <p class="metadata"><strong>类型:</strong> {{ selectedExhibit.type }}</p>
            <p class="metadata"><strong>来源:</strong> {{ selectedExhibit.source }}</p>
          </div>
          <div class="panel-actions">
            <router-link :to="'/detail/' + selectedExhibit.id" class="detail-btn">查看详情</router-link>
          </div>
        </div>
      </div>
      
      <!-- 操作提示 -->
      <div class="controls-hint" v-if="!loading && !selectedExhibit">
        <p>使用 WASD 或方向键移动，鼠标左键点击展品查看详情</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import * as THREE from 'three';
import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls';
import { getMockHistoryData, getHistoryData } from '@/api/history';
import MiniappBackButton from '../components/MiniappBackButton.vue';

const router = useRouter();
const route = useRoute();

// 小程序环境标记
const isFromMiniapp = ref(false);

// 检查是否从小程序进入
const checkFromMiniapp = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const fromParam = urlParams.get('from') === 'miniapp';
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true';
  
  isFromMiniapp.value = fromParam || fromStorage;
};

// 获取展厅类型
const galleryType = computed(() => {
  return (route.query.type as string) || 'culture';
});

// 展厅标题和描述
const galleryTitle = computed(() => {
  switch (galleryType.value) {
    case 'history': return '历史文脉';
    case 'memory': return '当代记忆';
    default: return '文化传承';
  }
});

const galleryDescription = computed(() => {
  switch (galleryType.value) {
    case 'history': return '探索地区发展历史';
    case 'memory': return '城市影像数字档案';
    default: return '传统文化数字保护';
  }
});

// 场景相关变量
const sceneCanvas = ref<HTMLCanvasElement | null>(null);
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: PointerLockControls;
let raycaster: THREE.Raycaster;
let clock = new THREE.Clock();
let animationFrameId: number;

// 加载状态
const loading = ref(true);
const loadingProgress = ref(0);

// 移动控制变量
const moveForward = ref(false);
const moveBackward = ref(false);
const moveLeft = ref(false);
const moveRight = ref(false);
const canJump = ref(false);

let velocity = new THREE.Vector3();
let direction = new THREE.Vector3();
let prevTime = performance.now();

// 展品数据
const exhibits = ref<any[]>([]);
const exhibitObjects: THREE.Object3D[] = [];
const selectedExhibit = ref<any>(null);

// 初始化3D场景
const initScene = async () => {
  if (!sceneCanvas.value) return;
  
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x111111);
  scene.fog = new THREE.Fog(0x111111, 10, 50);
  
  // 添加环境光和方向光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
  scene.add(ambientLight);
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(5, 10, 7.5);
  directionalLight.castShadow = true;
  scene.add(directionalLight);
  
  // 添加点光源
  const pointLight1 = new THREE.PointLight(0xffffff, 1, 20);
  pointLight1.position.set(0, 3, 0);
  scene.add(pointLight1);
  
  const pointLight2 = new THREE.PointLight(0xffffff, 1, 20);
  pointLight2.position.set(20, 3, 0);
  scene.add(pointLight2);
  
  // 设置相机
  camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  camera.position.set(0, 1.6, 0); // 人眼高度约1.6米
  
  // 设置渲染器
  renderer = new THREE.WebGLRenderer({ 
    canvas: sceneCanvas.value,
    antialias: true
  });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.shadowMap.enabled = true;
  
  // 使用指针锁定控制
  controls = new PointerLockControls(camera, renderer.domElement);
  scene.add(controls.getObject());
  
  // 设置射线投射器用于交互
  raycaster = new THREE.Raycaster();
  
  // 添加键盘事件监听
  document.addEventListener('keydown', onKeyDown);
  document.addEventListener('keyup', onKeyUp);
  
  // 添加点击事件监听
  renderer.domElement.addEventListener('click', onMouseClick);
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', onWindowResize);
  
  // 创建走廊展厅
  await createGallery();
  
  // 添加背景音乐
  if (galleryType.value === 'history') {
    playAmbientAudio('traditional-chinese');
  } else if (galleryType.value === 'memory') {
    playAmbientAudio('modern-ambient');
  } else {
    playAmbientAudio('soft-ambient');
  }
  
  // 开始动画循环
  animate();
  
  // 自动启用控制
  setTimeout(() => {
    controls.lock();
  }, 1000);
};

// 播放背景音乐
const audioElement = ref<HTMLAudioElement | null>(null);
const playAmbientAudio = (type: string) => {
  // 如果已经有音频在播放，先停止
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value = null;
  }
  
  // 创建音频元素
  const audio = new Audio();
  audio.loop = true;
  audio.volume = 0.3;
  
  // 根据展厅类型设置不同的背景音乐
  switch (type) {
    case 'traditional-chinese':
      audio.src = 'https://cdn.pixabay.com/download/audio/2022/03/10/audio_1b91c3782b.mp3?filename=chinese-traditional-melody-guzheng-bamboo-flute-140573.mp3';
      break;
    case 'modern-ambient':
      audio.src = 'https://cdn.pixabay.com/download/audio/2021/10/25/audio_dab1799053.mp3?filename=cinematic-documentary-115669.mp3';
      break;
    default:
      audio.src = 'https://cdn.pixabay.com/download/audio/2022/01/18/audio_d16415a8f0.mp3?filename=ambient-piano-amp-strings-10711.mp3';
  }
  
  // 播放音频
  audio.play().catch(error => {
    console.error('无法播放背景音乐:', error);
  });
  
  // 保存引用以便稍后停止
  audioElement.value = audio;
};

// 停止背景音乐
const stopAmbientAudio = () => {
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value = null;
  }
};

// 创建走廊展厅
const createGallery = async () => {
  // 根据展厅类型设置不同风格
  switch (galleryType.value) {
    case 'history':
      createHistoryGallery();
      break;
    case 'memory':
      createMemoryGallery();
      break;
    default:
      createCultureGallery();
      break;
  }
  
  // 加载展品数据
  await loadExhibits();
};

// 创建历史文脉展厅 - 古典中式风格
const createHistoryGallery = () => {
  // 地面 - 木地板材质
  const floorGeometry = new THREE.PlaneGeometry(100, 10);
  const floorMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B4513, // 深棕色木地板
    roughness: 0.8,
    metalness: 0.2
  });
  const floor = new THREE.Mesh(floorGeometry, floorMaterial);
  floor.rotation.x = -Math.PI / 2;
  floor.position.y = 0;
  floor.receiveShadow = true;
  scene.add(floor);
  
  // 天花板 - 古典木质天花板
  const ceilingGeometry = new THREE.PlaneGeometry(100, 10);
  const ceilingMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x5C4033, // 褐色木质
    roughness: 0.8,
    metalness: 0.2
  });
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
  ceiling.rotation.x = Math.PI / 2;
  ceiling.position.y = 4;
  ceiling.receiveShadow = true;
  scene.add(ceiling);
  
  // 添加天花板装饰花纹
  const ceilingDecorGeometry = new THREE.PlaneGeometry(100, 10);
  const ceilingDecorMaterial = new THREE.MeshStandardMaterial({
    color: 0x8B4513,
    roughness: 0.8,
    metalness: 0.3,
    transparent: true,
    opacity: 0.3
  });
  const ceilingDecor = new THREE.Mesh(ceilingDecorGeometry, ceilingDecorMaterial);
  ceilingDecor.rotation.x = Math.PI / 2;
  ceilingDecor.position.y = 3.99;
  scene.add(ceilingDecor);
  
  // 左墙 - 米色墙面
  const leftWallGeometry = new THREE.PlaneGeometry(100, 4);
  const leftWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xF5F5DC, // 米色
    roughness: 0.8,
    metalness: 0.2
  });
  const leftWall = new THREE.Mesh(leftWallGeometry, leftWallMaterial);
  leftWall.position.set(0, 2, 5);
  leftWall.rotation.y = Math.PI;
  leftWall.receiveShadow = true;
  scene.add(leftWall);
  
  // 右墙
  const rightWallGeometry = new THREE.PlaneGeometry(100, 4);
  const rightWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xF5F5DC, // 米色
    roughness: 0.8,
    metalness: 0.2
  });
  const rightWall = new THREE.Mesh(rightWallGeometry, rightWallMaterial);
  rightWall.position.set(0, 2, -5);
  rightWall.receiveShadow = true;
  scene.add(rightWall);
  
  // 墙壁装饰 - 添加中国风图案
  const addWallDecoration = (x: number, side: number) => {
    const decorGeometry = new THREE.PlaneGeometry(3, 1.5);
    const decorMaterial = new THREE.MeshStandardMaterial({
      color: 0xC8161E, // 中国红
      roughness: 0.8,
      metalness: 0.3,
      transparent: true,
      opacity: 0.2
    });
    const decoration = new THREE.Mesh(decorGeometry, decorMaterial);
    decoration.position.set(x, 2.2, side * 4.9);
    decoration.rotation.y = side === 1 ? Math.PI : 0;
    scene.add(decoration);
  };
  
  // 在两边墙上添加装饰
  for (let i = -45; i <= 45; i += 15) {
    addWallDecoration(i, 1); // 左墙
    addWallDecoration(i + 7.5, -1); // 右墙
  }
  
  // 尽头墙 - 带有中式图案
  const endWallGeometry = new THREE.PlaneGeometry(10, 4);
  const endWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xF5F5DC,
    roughness: 0.8,
    metalness: 0.2
  });
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial);
  endWall.position.set(50, 2, 0);
  endWall.rotation.y = -Math.PI / 2;
  endWall.receiveShadow = true;
  scene.add(endWall);
  
  // 起始墙
  const startWallGeometry = new THREE.PlaneGeometry(10, 4);
  const startWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xF5F5DC,
    roughness: 0.8,
    metalness: 0.2
  });
  const startWall = new THREE.Mesh(startWallGeometry, startWallMaterial);
  startWall.position.set(-50, 2, 0);
  startWall.rotation.y = Math.PI / 2;
  startWall.receiveShadow = true;
  scene.add(startWall);
  
  // 添加中式装饰柱子
  for (let i = -40; i <= 40; i += 20) {
    createChinesePillar(i, 0, 4.8);
    createChinesePillar(i, 0, -4.8);
  }
  
  // 添加传统宫灯
  for (let i = -30; i <= 30; i += 20) {
    createTraditionalLantern(i, 3.5, 0);
  }
  
  // 添加中式装饰屏风
  createScreenDivider(-10, 0, 0, 0.5);
  createScreenDivider(20, 0, 0, 0.5);
};

// 创建文化传承展厅 - 现代简约风格
const createCultureGallery = () => {
  // 地面 - 大理石地面
  const floorGeometry = new THREE.PlaneGeometry(100, 10);
  const floorMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xDDDDDD, // 浅灰色大理石
    roughness: 0.2,
    metalness: 0.8
  });
  const floor = new THREE.Mesh(floorGeometry, floorMaterial);
  floor.rotation.x = -Math.PI / 2;
  floor.position.y = 0;
  floor.receiveShadow = true;
  scene.add(floor);
  
  // 天花板 - 现代风格
  const ceilingGeometry = new THREE.PlaneGeometry(100, 10);
  const ceilingMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF, // 纯白色
    roughness: 0.8,
    metalness: 0.2
  });
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
  ceiling.rotation.x = Math.PI / 2;
  ceiling.position.y = 4;
  ceiling.receiveShadow = true;
  scene.add(ceiling);
  
  // 左墙 - 白色墙面
  const leftWallGeometry = new THREE.PlaneGeometry(100, 4);
  const leftWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF, // 纯白色
    roughness: 0.8,
    metalness: 0.2
  });
  const leftWall = new THREE.Mesh(leftWallGeometry, leftWallMaterial);
  leftWall.position.set(0, 2, 5);
  leftWall.rotation.y = Math.PI;
  leftWall.receiveShadow = true;
  scene.add(leftWall);
  
  // 右墙
  const rightWallGeometry = new THREE.PlaneGeometry(100, 4);
  const rightWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF, // 纯白色
    roughness: 0.8,
    metalness: 0.2
  });
  const rightWall = new THREE.Mesh(rightWallGeometry, rightWallMaterial);
  rightWall.position.set(0, 2, -5);
  rightWall.receiveShadow = true;
  scene.add(rightWall);
  
  // 尽头墙
  const endWallGeometry = new THREE.PlaneGeometry(10, 4);
  const endWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF,
    roughness: 0.8,
    metalness: 0.2
  });
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial);
  endWall.position.set(50, 2, 0);
  endWall.rotation.y = -Math.PI / 2;
  endWall.receiveShadow = true;
  scene.add(endWall);
  
  // 起始墙
  const startWallGeometry = new THREE.PlaneGeometry(10, 4);
  const startWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0xFFFFFF,
    roughness: 0.8,
    metalness: 0.2
  });
  const startWall = new THREE.Mesh(startWallGeometry, startWallMaterial);
  startWall.position.set(-50, 2, 0);
  startWall.rotation.y = Math.PI / 2;
  startWall.receiveShadow = true;
  scene.add(startWall);
  
  // 添加现代装饰灯带
  for (let i = -45; i <= 45; i += 10) {
    createLightStrip(i, 3.9, 0, 0xC8161E);
  }
};

// 创建当代记忆展厅 - 工业风格
const createMemoryGallery = () => {
  // 地面 - 混凝土地面
  const floorGeometry = new THREE.PlaneGeometry(100, 10);
  const floorMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x555555, // 深灰色混凝土
    roughness: 0.9,
    metalness: 0.1
  });
  const floor = new THREE.Mesh(floorGeometry, floorMaterial);
  floor.rotation.x = -Math.PI / 2;
  floor.position.y = 0;
  floor.receiveShadow = true;
  scene.add(floor);
  
  // 天花板 - 工业风格
  const ceilingGeometry = new THREE.PlaneGeometry(100, 10);
  const ceilingMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x333333, // 深灰色
    roughness: 0.8,
    metalness: 0.5
  });
  const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
  ceiling.rotation.x = Math.PI / 2;
  ceiling.position.y = 4;
  ceiling.receiveShadow = true;
  scene.add(ceiling);
  
  // 左墙 - 砖墙
  const leftWallGeometry = new THREE.PlaneGeometry(100, 4);
  const leftWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B4513, // 砖红色
    roughness: 0.9,
    metalness: 0.1
  });
  const leftWall = new THREE.Mesh(leftWallGeometry, leftWallMaterial);
  leftWall.position.set(0, 2, 5);
  leftWall.rotation.y = Math.PI;
  leftWall.receiveShadow = true;
  scene.add(leftWall);
  
  // 右墙
  const rightWallGeometry = new THREE.PlaneGeometry(100, 4);
  const rightWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B4513, // 砖红色
    roughness: 0.9,
    metalness: 0.1
  });
  const rightWall = new THREE.Mesh(rightWallGeometry, rightWallMaterial);
  rightWall.position.set(0, 2, -5);
  rightWall.receiveShadow = true;
  scene.add(rightWall);
  
  // 尽头墙
  const endWallGeometry = new THREE.PlaneGeometry(10, 4);
  const endWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B4513,
    roughness: 0.9,
    metalness: 0.1
  });
  const endWall = new THREE.Mesh(endWallGeometry, endWallMaterial);
  endWall.position.set(50, 2, 0);
  endWall.rotation.y = -Math.PI / 2;
  endWall.receiveShadow = true;
  scene.add(endWall);
  
  // 起始墙
  const startWallGeometry = new THREE.PlaneGeometry(10, 4);
  const startWallMaterial = new THREE.MeshStandardMaterial({ 
    color: 0x8B4513,
    roughness: 0.9,
    metalness: 0.1
  });
  const startWall = new THREE.Mesh(startWallGeometry, startWallMaterial);
  startWall.position.set(-50, 2, 0);
  startWall.rotation.y = Math.PI / 2;
  startWall.receiveShadow = true;
  scene.add(startWall);
  
  // 添加工业风格的吊灯
  for (let i = -40; i <= 40; i += 10) {
    createHangingLight(i, 3.8, 0);
  }
};

// 创建柱子
const createPillar = (x: number, y: number, z: number, color: number) => {
  const geometry = new THREE.BoxGeometry(0.5, 4, 0.5);
  const material = new THREE.MeshStandardMaterial({
    color: color,
    roughness: 0.8,
    metalness: 0.2
  });
  const pillar = new THREE.Mesh(geometry, material);
  pillar.position.set(x, y + 2, z);
  pillar.castShadow = true;
  pillar.receiveShadow = true;
  scene.add(pillar);
};

// 创建中式柱子
const createChinesePillar = (x: number, y: number, z: number) => {
  // 柱身
  const pillarGeometry = new THREE.CylinderGeometry(0.4, 0.4, 4, 16);
  const pillarMaterial = new THREE.MeshStandardMaterial({
    color: 0x8B4513, // 深棕色木柱
    roughness: 0.8,
    metalness: 0.2
  });
  const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
  pillar.position.set(x, y + 2, z);
  pillar.castShadow = true;
  pillar.receiveShadow = true;
  scene.add(pillar);
  
  // 柱顶装饰
  const capGeometry = new THREE.BoxGeometry(1, 0.2, 1);
  const capMaterial = new THREE.MeshStandardMaterial({
    color: 0xC8161E, // 中国红
    roughness: 0.7,
    metalness: 0.3
  });
  const cap = new THREE.Mesh(capGeometry, capMaterial);
  cap.position.set(x, y + 4.1, z);
  cap.castShadow = true;
  scene.add(cap);
  
  // 柱底装饰
  const baseGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8);
  const baseMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000, // 黑色底座
    roughness: 0.5,
    metalness: 0.5
  });
  const base = new THREE.Mesh(baseGeometry, baseMaterial);
  base.position.set(x, y + 0.05, z);
  base.receiveShadow = true;
  scene.add(base);
};

// 创建灯带
const createLightStrip = (x: number, y: number, z: number, color: number) => {
  const geometry = new THREE.BoxGeometry(0.1, 0.1, 8);
  const material = new THREE.MeshStandardMaterial({
    color: color,
    emissive: color,
    emissiveIntensity: 1
  });
  const light = new THREE.Mesh(geometry, material);
  light.position.set(x, y, z);
  scene.add(light);
  
  // 添加点光源
  const pointLight = new THREE.PointLight(color, 0.5, 10);
  pointLight.position.set(x, y - 0.5, z);
  scene.add(pointLight);
};

// 创建吊灯
const createHangingLight = (x: number, y: number, z: number) => {
  // 灯罩
  const coneGeometry = new THREE.ConeGeometry(0.5, 0.8, 16);
  const coneMaterial = new THREE.MeshStandardMaterial({
    color: 0x333333,
    roughness: 0.5,
    metalness: 0.8
  });
  const cone = new THREE.Mesh(coneGeometry, coneMaterial);
  cone.position.set(x, y - 0.4, z);
  scene.add(cone);
  
  // 灯线
  const lineGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.5, 8);
  const lineMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000,
    roughness: 0.5,
    metalness: 0.8
  });
  const line = new THREE.Mesh(lineGeometry, lineMaterial);
  line.position.set(x, y - 0.05, z);
  scene.add(line);
  
  // 灯光
  const pointLight = new THREE.PointLight(0xFFFFFF, 0.8, 8);
  pointLight.position.set(x, y - 0.8, z);
  scene.add(pointLight);
};

// 创建传统宫灯
const createTraditionalLantern = (x: number, y: number, z: number) => {
  // 灯笼主体
  const lanternGeometry = new THREE.SphereGeometry(0.3, 16, 16);
  const lanternMaterial = new THREE.MeshStandardMaterial({
    color: 0xC8161E, // 中国红
    emissive: 0xC8161E,
    emissiveIntensity: 0.5,
    roughness: 0.7,
    metalness: 0.3
  });
  const lantern = new THREE.Mesh(lanternGeometry, lanternMaterial);
  lantern.position.set(x, y, z);
  lantern.castShadow = true;
  scene.add(lantern);
  
  // 灯笼挂绳
  const stringGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.5, 8);
  const stringMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000, // 黑色
    roughness: 0.5,
    metalness: 0.5
  });
  const string = new THREE.Mesh(stringGeometry, stringMaterial);
  string.position.set(x, y + 0.25, z);
  scene.add(string);
  
  // 点光源
  const light = new THREE.PointLight(0xFFCC66, 0.8, 5);
  light.position.set(x, y, z);
  scene.add(light);
};

// 创建中式屏风
const createScreenDivider = (x: number, y: number, z: number, scale: number = 1) => {
  // 屏风框架
  const frameGeometry = new THREE.BoxGeometry(0.1 * scale, 3 * scale, 4 * scale);
  const frameMaterial = new THREE.MeshStandardMaterial({
    color: 0x5C4033, // 褐色木质
    roughness: 0.8,
    metalness: 0.2
  });
  const frame = new THREE.Mesh(frameGeometry, frameMaterial);
  frame.position.set(x, y + (1.5 * scale), z);
  frame.castShadow = true;
  frame.receiveShadow = true;
  scene.add(frame);
  
  // 屏风装饰面
  const screenGeometry = new THREE.PlaneGeometry(2.8 * scale, 2.8 * scale);
  const screenMaterial = new THREE.MeshStandardMaterial({
    color: 0xFFFFF0, // 米白色
    roughness: 0.8,
    metalness: 0.2,
    transparent: true,
    opacity: 0.9
  });
  const screen = new THREE.Mesh(screenGeometry, screenMaterial);
  screen.position.set(x, y + (1.5 * scale), z);
  screen.rotation.y = Math.PI / 2;
  screen.castShadow = true;
  scene.add(screen);
};

// 加载展品数据
const loadExhibits = async () => {
  try {
    loading.value = true;
    
    // 根据展厅类型加载不同的数据
    if (galleryType.value === 'history') {
      // 加载历史文脉数据
      await loadHistoryData();
    } else if (galleryType.value === 'memory') {
      // 加载当代记忆数据
      await loadMemoryData();
    } else {
      // 加载文化传承数据
      await loadCultureData();
    }
    
    // 创建展品展示
    createExhibitDisplays();
    
    loading.value = false;
  } catch (error) {
    console.error('加载展品数据失败:', error);
    
    // 使用模拟数据（如果API调用失败）
    exhibits.value = Array(20).fill(null).map((_, index) => ({
      id: index + 1,
      title: `文化展品 ${index + 1}`,
      description: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。这件展品具有深厚的历史意义和艺术价值，是中华文明的重要组成部分。',
      image: `https://picsum.photos/seed/${index + 100}/800/600`,
      year: `${1000 + Math.floor(Math.random() * 1000)}年`,
      type: ['绘画', '雕塑', '陶瓷', '书法', '织物'][Math.floor(Math.random() * 5)],
      source: ['故宫博物院', '上海博物馆', '陕西历史博物馆', '南京博物院', '湖南省博物馆'][Math.floor(Math.random() * 5)]
    }));
    
    // 创建展品展示
    createExhibitDisplays();
    
    loading.value = false;
  }
};

// 加载历史文脉数据
const loadHistoryData = async () => {
  try {
    console.log('正在加载历史文脉数据...');
    // 获取真实历史文脉数据
    const historyData = await getHistoryData({
      province_id: 51, // 默认使用北京市的数据
      city_id: 4,
      district_id: 21,
      page: 1,
      page_size: 50
    });
    
    // 转换为展品格式
    exhibits.value = historyData.items.map((item: any) => ({
      id: item.id,
      title: item.title,
      description: item.description,
      image: item.image || `https://picsum.photos/seed/${item.id + 100}/800/600`, // 如果没有图片，使用占位图
      year: item.year,
      type: '历史文脉',
      source: item.heritage_tags?.join(', ') || '历史记载',
      content: item.detail || item.description,
      period: item.period,
      detail_images: item.detail_images || [],
      importance: item.sort_order || 5 // 使用排序顺序作为重要性指标
    }));
    
    // 按照年代排序（古至今）
    exhibits.value.sort((a, b) => {
      // 尝试将年份转换为可比较的数字
      const yearA = parseYearToNumber(a.year);
      const yearB = parseYearToNumber(b.year);
      return yearA - yearB;
    });
    
    console.log('历史文脉数据加载成功:', exhibits.value.length);
    return exhibits.value;
  } catch (error) {
    console.error('加载历史文脉数据失败:', error);
    
    // 如果API调用失败，使用模拟数据
    const mockData = getMockHistoryData();
    
    exhibits.value = mockData.items.map((item: any) => ({
      id: item.id,
      title: item.title,
      description: item.description,
      image: item.image,
      year: item.year,
      type: item.type || '历史文脉',
      source: item.source,
      content: item.content,
      period: item.period,
      importance: item.importance || 5
    }));
    
    // 按照年代排序（古至今）
    exhibits.value.sort((a, b) => {
      const yearA = parseYearToNumber(a.year);
      const yearB = parseYearToNumber(b.year);
      return yearA - yearB;
    });
    
    console.log('使用模拟历史文脉数据:', exhibits.value.length);
    return exhibits.value;
  }
};

// 将年份字符串转换为可比较的数字
const parseYearToNumber = (yearStr: string): number => {
  if (!yearStr) return 0;
  
  // 处理"公元前"年份
  if (yearStr.includes('公元前')) {
    const match = yearStr.match(/公元前(\d+)/);
    if (match && match[1]) {
      return -parseInt(match[1], 10);
    }
  }
  
  // 处理包含范围的年份，如"1911-1949年"，取前面的年份
  if (yearStr.includes('-')) {
    const match = yearStr.match(/(\d+)-\d+/);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }
  }
  
  // 处理普通年份
  const match = yearStr.match(/(\d+)/);
  if (match && match[1]) {
    return parseInt(match[1], 10);
  }
  
  return 0;
};

// 加载文化传承数据
const loadCultureData = async () => {
  try {
    // 使用模拟数据
    const mockCultureItems = Array(15).fill(null).map((_, index) => ({
      id: index + 101,
      title: `文化传承展品 ${index + 1}`,
      description: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。',
      image: `https://picsum.photos/seed/${index + 200}/800/600`,
      year: '',
      type: ['非物质文化遗产', '传统工艺', '民间艺术', '传统节日', '传统医药'][Math.floor(Math.random() * 5)],
      source: ['国家级非遗名录', '省级非遗名录', '市级非遗名录'][Math.floor(Math.random() * 3)],
      content: '这是一件珍贵的文化遗产展品，代表了中国传统文化的精髓。这件展品具有深厚的历史意义和艺术价值，是中华文明的重要组成部分。通过这件展品，我们可以了解到中国传统文化的独特魅力和深厚底蕴。',
      detail_images: [
        `https://picsum.photos/seed/${index + 300}/800/600`,
        `https://picsum.photos/seed/${index + 301}/800/600`
      ]
    }));
    
    exhibits.value = mockCultureItems;
    console.log('文化传承数据加载成功:', exhibits.value.length);
    
    return exhibits.value;
  } catch (error) {
    console.error('加载文化传承数据失败:', error);
    throw error;
  }
};

// 加载当代记忆数据
const loadMemoryData = async () => {
  try {
    // 使用模拟数据
    const mockMemoryItems = Array(15).fill(null).map((_, index) => ({
      id: index + 201,
      title: `当代记忆展品 ${index + 1}`,
      description: '这是一段珍贵的当代记忆，记录了中国现代化进程中的重要瞬间。',
      image: `https://picsum.photos/seed/${index + 400}/800/600`,
      year: `${1950 + Math.floor(Math.random() * 70)}年`,
      type: '城市记忆',
      source: '',
      content: '这是一段珍贵的当代记忆，记录了中国现代化进程中的重要瞬间。通过这段记忆，我们可以了解到中国人民在新时代建设中的奋斗历程和取得的伟大成就。',
      detail_images: [
        `https://picsum.photos/seed/${index + 500}/800/600`,
        `https://picsum.photos/seed/${index + 501}/800/600`
      ]
    }));
    
    exhibits.value = mockMemoryItems;
    console.log('当代记忆数据加载成功:', exhibits.value.length);
    
    return exhibits.value;
  } catch (error) {
    console.error('加载当代记忆数据失败:', error);
    throw error;
  }
};

// 创建展品展示
const createExhibitDisplays = () => {
  // 清空现有展品
  exhibitObjects.forEach(obj => scene.remove(obj));
  exhibitObjects.length = 0;
  
  // 根据展厅类型决定展示方式
  if (galleryType.value === 'history') {
    // 历史文脉展厅：在柱子之间展示展品
    createHistoryExhibits();
  } else {
    // 其他展厅：在左右墙上展示展品
    createWallExhibits();
  }
};

// 在柱子之间展示历史文脉展品
const createHistoryExhibits = () => {
  // 计算柱子位置（与createHistoryGallery中的柱子位置保持一致）
  const pillarPositions = [];
  for (let i = -40; i <= 40; i += 20) {
    pillarPositions.push(i);
  }
  
  // 确保有足够的展品数据
  if (!exhibits.value || exhibits.value.length === 0) return;
  
  // 在每两个柱子之间放置一个展品
  for (let i = 0; i < pillarPositions.length - 1; i++) {
    const exhibit = exhibits.value[i % exhibits.value.length];
    
    // 计算展品位置（两柱子之间的中点）
    const posX = (pillarPositions[i] + pillarPositions[i + 1]) / 2;
    
    // 创建地面装饰图案
    createFloorDecoration(posX, 0.01, 0, 2.5);
    
    // 创建展品展示台
    const pedestalGeometry = new THREE.BoxGeometry(3, 1, 1.5);
    const pedestalMaterial = new THREE.MeshStandardMaterial({ 
      color: 0x5C4033, // 褐色木质
      roughness: 0.8,
      metalness: 0.2
    });
    const pedestal = new THREE.Mesh(pedestalGeometry, pedestalMaterial);
    pedestal.position.set(posX, 0.5, 0); // 放在地面上方0.5高度
    pedestal.castShadow = true;
    pedestal.receiveShadow = true;
    scene.add(pedestal);
    exhibitObjects.push(pedestal);
    
    // 添加展台上的装饰图案
    const pedestalTopGeometry = new THREE.PlaneGeometry(3, 1.5);
    const pedestalTopMaterial = new THREE.MeshStandardMaterial({
      color: 0xC8161E,
      roughness: 0.7,
      metalness: 0.3,
      transparent: true,
      opacity: 0.2
    });
    const pedestalTop = new THREE.Mesh(pedestalTopGeometry, pedestalTopMaterial);
    pedestalTop.position.set(posX, 1.01, 0);
    pedestalTop.rotation.x = -Math.PI / 2;
    scene.add(pedestalTop);
    
    // 创建展品框架
    const frameGeometry = new THREE.BoxGeometry(2.2, 1.8, 0.1);
    const frameMaterial = new THREE.MeshStandardMaterial({ 
      color: 0x8B4513, // 深褐色木框
      roughness: 0.5,
      metalness: 0.5
    });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.position.set(posX, 2.2, 0); // 放在展示台上方
    frame.castShadow = true;
    frame.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit };
    scene.add(frame);
    exhibitObjects.push(frame);
    
    // 创建展品画布（使用纹理）
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
      exhibit.image || `https://picsum.photos/seed/${i + 100}/800/600`,
      (texture) => {
        const aspectRatio = texture.image.width / texture.image.height;
        const canvasGeometry = new THREE.PlaneGeometry(2, 2 / aspectRatio);
        const canvasMaterial = new THREE.MeshBasicMaterial({ 
          map: texture,
          side: THREE.DoubleSide
        });
        const canvas = new THREE.Mesh(canvasGeometry, canvasMaterial);
        canvas.position.set(posX, 2.2, 0);
        canvas.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit };
        scene.add(canvas);
        exhibitObjects.push(canvas);
        
        // 添加展品标题
        createTextLabel(exhibit.title, posX, 1.0, 0, 1);
        
        // 添加年代标记
        if (exhibit.year) {
          createTextLabel(exhibit.year, posX, 3.3, 0, 1, 0.8, '#8B4513');
        }
        
        // 添加小型聚光灯照亮展品
        const spotLight = new THREE.SpotLight(0xFFFFCC, 1, 10, Math.PI / 6, 0.5, 1);
        spotLight.position.set(posX, 3.5, 0);
        spotLight.target = canvas;
        spotLight.castShadow = true;
        scene.add(spotLight);
      },
      (xhr) => {
        // 更新加载进度
        loadingProgress.value = (xhr.loaded / xhr.total) * 100;
      },
      (error) => {
        console.error('加载纹理失败:', error);
      }
    );
  }
};

// 在左右墙上展示展品（原有的展示方式）
const createWallExhibits = () => {
  // 在左右墙上创建展品
  exhibits.value.forEach((exhibit, index) => {
    // 确定展品位置（左右墙交替）
    const side = index % 2 === 0 ? 1 : -1; // 1代表左墙，-1代表右墙
    const position = -40 + Math.floor(index / 2) * 8; // 沿走廊分布
    
    // 创建展品框架
    const frameGeometry = new THREE.BoxGeometry(0.1, 2, 1.5);
    const frameMaterial = new THREE.MeshStandardMaterial({ 
      color: 0x8B4513,
      roughness: 0.5,
      metalness: 0.5
    });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.position.set(position, 2, side * 4.95);
    frame.castShadow = true;
    frame.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit };
    scene.add(frame);
    exhibitObjects.push(frame);
    
    // 创建展品画布（使用纹理）
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
      exhibit.image || `https://picsum.photos/seed/${index + 100}/800/600`,
      (texture) => {
        const aspectRatio = texture.image.width / texture.image.height;
        const canvasGeometry = new THREE.PlaneGeometry(aspectRatio * 1.4, 1.4);
        const canvasMaterial = new THREE.MeshBasicMaterial({ 
          map: texture,
          side: THREE.DoubleSide
        });
        const canvas = new THREE.Mesh(canvasGeometry, canvasMaterial);
        canvas.position.set(position, 2, side * 4.9);
        canvas.rotation.y = side === 1 ? Math.PI : 0;
        canvas.userData = { isExhibit: true, exhibitId: exhibit.id, exhibit };
        scene.add(canvas);
        exhibitObjects.push(canvas);
        
        // 添加展品标题
        createTextLabel(exhibit.title, position, 1.1, side * 4.85, side);
      },
      (xhr) => {
        // 更新加载进度
        loadingProgress.value = (xhr.loaded / xhr.total) * 100;
      },
      (error) => {
        console.error('加载纹理失败:', error);
      }
    );
  });
};

// 创建文本标签
const createTextLabel = (text: string, x: number, y: number, z: number, side: number, scale: number = 1, bgColor: string = '#ffffff') => {
  // 创建画布并绘制文本
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return;
  
  canvas.width = 256;
  canvas.height = 64;
  
  context.fillStyle = bgColor;
  context.fillRect(0, 0, canvas.width, canvas.height);
  
  context.font = '24px KaiTi, STKaiti, serif';
  context.fillStyle = '#000000';
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  context.fillText(text, canvas.width / 2, canvas.height / 2);
  
  // 创建纹理和材质
  const texture = new THREE.CanvasTexture(canvas);
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.DoubleSide,
    transparent: true
  });
  
  // 创建平面几何体
  const geometry = new THREE.PlaneGeometry(1 * scale, 0.25 * scale);
  const mesh = new THREE.Mesh(geometry, material);
  mesh.position.set(x, y, z);
  mesh.rotation.y = side === 1 ? Math.PI : 0;
  
  scene.add(mesh);
  exhibitObjects.push(mesh);
};

// 创建地面装饰图案
const createFloorDecoration = (x: number, y: number, z: number, size: number = 2) => {
  const geometry = new THREE.PlaneGeometry(size, size);
  const material = new THREE.MeshStandardMaterial({
    color: 0xC8161E, // 中国红
    roughness: 0.7,
    metalness: 0.3,
    transparent: true,
    opacity: 0.2
  });
  
  const decoration = new THREE.Mesh(geometry, material);
  decoration.position.set(x, y, z);
  decoration.rotation.x = -Math.PI / 2;
  scene.add(decoration);
};

// 键盘按下事件处理
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = true;
      break;
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = true;
      break;
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = true;
      break;
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = true;
      break;
    case 'Space':
      if (canJump.value) {
        velocity.y += 350;
      }
      canJump.value = false;
      break;
    case 'Escape':
      controls.unlock();
      break;
  }
};

// 键盘释放事件处理
const onKeyUp = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'ArrowUp':
    case 'KeyW':
      moveForward.value = false;
      break;
    case 'ArrowLeft':
    case 'KeyA':
      moveLeft.value = false;
      break;
    case 'ArrowDown':
    case 'KeyS':
      moveBackward.value = false;
      break;
    case 'ArrowRight':
    case 'KeyD':
      moveRight.value = false;
      break;
  }
};

// 鼠标点击事件处理
const onMouseClick = () => {
  if (!controls.isLocked) {
    controls.lock();
    return;
  }
  
  // 使用射线检测点击的对象
  const raycaster = new THREE.Raycaster();
  const mouse = new THREE.Vector2();
  
  // 设置射线起点和方向（从相机发出）
  raycaster.setFromCamera(mouse, camera);
  
  // 检测与展品的交叉
  const intersects = raycaster.intersectObjects(exhibitObjects);
  
  if (intersects.length > 0) {
    const object = intersects[0].object;
    if (object.userData && object.userData.isExhibit) {
      // 显示展品详情
      selectedExhibit.value = object.userData.exhibit;
      controls.unlock(); // 解锁控制以便用户可以与UI交互
    }
  }
};

// 窗口大小变化处理
const onWindowResize = () => {
  if (!camera || !renderer) return;
  
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
};

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate);
  
  if (controls.isLocked) {
    // 计算时间增量
    const time = performance.now();
    const delta = (time - prevTime) / 1000;
    
    // 应用阻尼
    velocity.x -= velocity.x * 10.0 * delta;
    velocity.z -= velocity.z * 10.0 * delta;
    velocity.y -= 9.8 * 100.0 * delta; // 应用重力
    
    // 根据按键状态计算方向
    direction.z = Number(moveForward.value) - Number(moveBackward.value);
    direction.x = Number(moveRight.value) - Number(moveLeft.value);
    direction.normalize(); // 确保对角线移动不会更快
    
    // 应用移动速度
    if (moveForward.value || moveBackward.value) velocity.z -= direction.z * 400.0 * delta;
    if (moveLeft.value || moveRight.value) velocity.x -= direction.x * 400.0 * delta;
    
    // 移动控制器
    controls.moveRight(-velocity.x * delta);
    controls.moveForward(-velocity.z * delta);
    
    // 限制移动范围（保持在走廊内）
    const position = controls.getObject().position;
    
    // X轴限制（走廊长度）
    if (position.x < -49) position.x = -49;
    if (position.x > 49) position.x = 49;
    
    // Z轴限制（走廊宽度）
    if (position.z < -4.5) position.z = -4.5;
    if (position.z > 4.5) position.z = 4.5;
    
    // Y轴限制（高度）
    if (position.y < 1.6) {
      velocity.y = 0;
      position.y = 1.6;
      canJump.value = true;
    }
    
    prevTime = time;
  }
  
  renderer.render(scene, camera);
};

// 返回首页
const goBack = () => {
  router.push('/');
};

// 关闭展品面板
const closeExhibitPanel = () => {
  selectedExhibit.value = null;
  controls.lock(); // 重新锁定控制
};

// 组件挂载时初始化场景
onMounted(() => {
  // 检查是否从小程序进入
  checkFromMiniapp();
  initScene();
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  document.removeEventListener('keydown', onKeyDown);
  document.removeEventListener('keyup', onKeyUp);
  renderer.domElement.removeEventListener('click', onMouseClick);
  window.removeEventListener('resize', onWindowResize);
  
  cancelAnimationFrame(animationFrameId);
  
  if (controls) {
    controls.dispose();
  }
  
  if (renderer) {
    renderer.dispose();
  }
  
  // 停止背景音乐
  stopAmbientAudio();
});
</script>

<style scoped lang="scss">
.gallery-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.scene-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
  
  & > * {
    pointer-events: auto;
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  .icon {
    margin-right: 5px;
    font-size: 1.2rem;
  }
}

.gallery-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  
  h1 {
    font-size: 2rem;
    margin: 0 0 5px 0;
    font-weight: bold;
  }
  
  p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }
}

.loading-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  
  .loading-text {
    margin-bottom: 10px;
    font-size: 1.2rem;
  }
  
  .progress-bar {
    width: 300px;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    
    .progress {
      height: 100%;
      background-color: var(--primary-color);
      transition: width 0.3s ease;
    }
  }
}

.exhibit-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 800px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
  
  .panel-header {
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 1.5rem;
    }
    
    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 1.8rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
    }
  }
  
  .panel-content {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    
    .exhibit-image {
      width: 100%;
      max-height: 400px;
      object-fit: contain;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    
    .exhibit-info {
      .description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 20px;
      }
      
      .metadata {
        margin: 10px 0;
        font-size: 0.9rem;
      }
    }
  }
  
  .panel-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    
    .detail-btn {
      padding: 10px 25px;
      background-color: var(--primary-color);
      color: white;
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: darken(#C8161E, 10%);
        transform: translateY(-2px);
      }
    }
  }
}

.controls-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .exhibit-panel {
    width: 95%;
    
    .panel-content {
      max-height: 80vh;
      
      .exhibit-image {
        max-height: 300px;
      }
    }
  }
}
</style> 