#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的uni-app项目配置文件
"""

import json
from pathlib import Path

class ProjectConfigCreator:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
    
    def create_package_json(self):
        """创建package.json"""
        package_config = {
            "name": "millennia-now-app",
            "version": "1.0.0",
            "description": "千载·今知 - 文化传承小程序",
            "main": "main.ts",
            "scripts": {
                "build:app": "uni build --platform app",
                "build:h5": "uni build --platform h5",
                "build:mp-weixin": "uni build --platform mp-weixin",
                "dev:app": "uni dev --platform app",
                "dev:h5": "uni dev --platform h5",
                "dev:mp-weixin": "uni dev --platform mp-weixin",
                "type-check": "vue-tsc --noEmit"
            },
            "dependencies": {
                "@dcloudio/uni-app": "^3.0.0",
                "@dcloudio/uni-components": "^3.0.0",
                "@dcloudio/uni-h5": "^3.0.0",
                "@dcloudio/uni-mp-weixin": "^3.0.0",
                "@dcloudio/uni-app-plus": "^3.0.0",
                "vue": "^3.3.0",
                "pinia": "^2.1.0"
            },
            "devDependencies": {
                "@dcloudio/types": "^3.4.0",
                "@dcloudio/uni-automator": "^3.0.0",
                "@dcloudio/uni-cli-shared": "^3.0.0",
                "@dcloudio/vite-plugin-uni": "^3.0.0",
                "@types/node": "^20.0.0",
                "@vue/tsconfig": "^0.4.0",
                "sass": "^1.69.0",
                "typescript": "^5.0.0",
                "vite": "^4.4.0",
                "vue-tsc": "^1.8.0"
            },
            "uni-app": {
                "scripts": {}
            }
        }
        
        with open(self.project_path / "package.json", 'w', encoding='utf-8') as f:
            json.dump(package_config, f, ensure_ascii=False, indent=2)
        
        print("✓ 创建package.json完成")
    
    def create_tsconfig_json(self):
        """创建TypeScript配置"""
        tsconfig = {
            "extends": "@vue/tsconfig/tsconfig.json",
            "compilerOptions": {
                "target": "ES2020",
                "useDefineForClassFields": True,
                "module": "ESNext",
                "lib": ["ES2020", "DOM", "DOM.Iterable"],
                "skipLibCheck": True,
                "moduleResolution": "bundler",
                "allowImportingTsExtensions": True,
                "resolveJsonModule": True,
                "isolatedModules": True,
                "noEmit": True,
                "jsx": "preserve",
                "strict": True,
                "noUnusedLocals": False,
                "noUnusedParameters": False,
                "noFallthroughCasesInSwitch": True,
                "baseUrl": ".",
                "paths": {
                    "@/*": ["./src/*"],
                    "@/components/*": ["./src/components/*"],
                    "@/utils/*": ["./src/utils/*"],
                    "@/api/*": ["./src/api/*"],
                    "@/store/*": ["./src/store/*"],
                    "@/types/*": ["./src/types/*"]
                },
                "types": ["@dcloudio/types"]
            },
            "include": [
                "src/**/*.ts",
                "src/**/*.d.ts",
                "src/**/*.tsx",
                "src/**/*.vue"
            ],
            "exclude": [
                "node_modules",
                "dist",
                "**/*.js"
            ]
        }
        
        with open(self.project_path / "tsconfig.json", 'w', encoding='utf-8') as f:
            json.dump(tsconfig, f, ensure_ascii=False, indent=2)
        
        print("✓ 创建tsconfig.json完成")
    
    def create_vite_config(self):
        """创建Vite配置"""
        vite_config = '''import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/uni.scss";'
      }
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0'
  }
})
'''
        
        with open(self.project_path / "vite.config.ts", 'w', encoding='utf-8') as f:
            f.write(vite_config)
        
        print("✓ 创建vite.config.ts完成")
    
    def create_env_files(self):
        """创建环境配置文件"""
        # .env.development
        env_dev = '''# 开发环境配置
NODE_ENV=development
VITE_APP_TITLE=千载·今知
VITE_API_BASE_URL=http://localhost:5001
VITE_MINIO_ENDPOINT=http://localhost:9000
'''
        
        # .env.production
        env_prod = '''# 生产环境配置
NODE_ENV=production
VITE_APP_TITLE=千载·今知
VITE_API_BASE_URL=https://api.millennia-now.com
VITE_MINIO_ENDPOINT=https://minio.millennia-now.com
'''
        
        with open(self.project_path / ".env.development", 'w', encoding='utf-8') as f:
            f.write(env_dev)
        
        with open(self.project_path / ".env.production", 'w', encoding='utf-8') as f:
            f.write(env_prod)
        
        print("✓ 创建环境配置文件完成")
    
    def create_gitignore(self):
        """创建.gitignore文件"""
        gitignore_content = '''# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
unpackage/
.temp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Environment variables
.env.local
.env.*.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port
'''
        
        with open(self.project_path / ".gitignore", 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        print("✓ 创建.gitignore完成")
    
    def create_readme(self):
        """创建README.md"""
        readme_content = '''# 千载·今知 (Millennia Now)

> 文化传承小程序 - 从APK反编译重构的完整源代码

## 项目简介

千载·今知是一个文化传承主题的uni-app应用，通过现代化的交互方式展示传统文化内容。本项目通过APK反编译技术，完整还原了应用的源代码结构。

## 功能特色

### 🚇 重庆轨道2号线文化之旅
- **8个特色站点**: 较场口、临江门、黄花园、大溪沟、曾家岩、牛角沱、李子坝、佛图关
- **网红景点**: 轻轨穿楼（李子坝）、开往春天的地铁（佛图关）
- **文化展示**: 每个站点的历史文化背景和周边景点介绍
- **交互体验**: 站点详情弹窗、图片预览、线路选择器

### 🏛️ 文化遗产
- 历史文化内容管理
- 文化遗产详情展示
- 文化记忆编辑功能
- 时间线编辑器

### 🎵 知音阁
- 文化音频内容
- 传统文化知识分享

### 👤 用户中心
- 个人信息管理
- 用户偏好设置

## 技术栈

- **框架**: uni-app (Vue 3 + TypeScript)
- **构建工具**: Vite
- **样式**: SCSS
- **状态管理**: Pinia
- **开发语言**: TypeScript

## 项目结构

```
data/new_source/
├── pages/                  # 页面文件
│   ├── index/             # 首页
│   ├── rail/              # 轨道交通
│   │   └── cq-line2.vue   # 重庆2号线
│   ├── culture/           # 文化模块
│   ├── zhiyin/            # 知音阁
│   ├── user/              # 用户中心
│   ├── location/          # 位置选择
│   ├── webview/           # 网页视图
│   └── admin/             # 管理功能
├── static/                # 静态资源
│   ├── icons/             # 图标文件
│   └── images/            # 图片资源
├── components/            # 组件库
├── utils/                 # 工具函数
├── api/                   # API接口
├── store/                 # 状态管理
├── types/                 # 类型定义
├── App.vue               # 应用入口
├── main.ts               # 主入口文件
├── pages.json            # 页面配置
├── manifest.json         # 应用配置
└── uni.scss              # 全局样式
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# H5开发
npm run dev:h5

# APP开发
npm run dev:app

# 微信小程序开发
npm run dev:mp-weixin
```

### 构建打包
```bash
# H5构建
npm run build:h5

# APP构建
npm run build:app

# 微信小程序构建
npm run build:mp-weixin
```

## 重要说明

本项目通过APK反编译技术重构，包含以下特点：

1. **完整还原**: 保持了原应用的所有功能和页面结构
2. **现代化重构**: 使用TypeScript和Vue 3重写了所有组件
3. **标准化结构**: 遵循uni-app标准项目结构
4. **可维护性**: 添加了完整的类型定义和文档

## 许可证

本项目仅用于学习和研究目的。

## 更新日志

### v1.0.0 (2025-01-30)
- 🎉 完成APK反编译和源代码重构
- ✨ 重庆2号线功能完整还原
- 🔧 添加TypeScript支持
- 📦 配置完整的开发环境
'''
        
        with open(self.project_path / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✓ 创建README.md完成")
    
    def run(self):
        """执行所有配置文件创建"""
        print("📝 开始创建项目配置文件...")
        
        self.create_package_json()
        self.create_tsconfig_json()
        self.create_vite_config()
        self.create_env_files()
        self.create_gitignore()
        self.create_readme()
        
        print("✅ 项目配置文件创建完成!")

if __name__ == "__main__":
    creator = ProjectConfigCreator("data/new_source")
    creator.run()
