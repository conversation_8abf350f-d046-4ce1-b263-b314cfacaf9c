<template>
  <view class="index-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/zhiyin/index</text>
      <text>分类: zhiyin</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/zhiyin/index')
})
</script>

<style scoped>
.container{background-color:#f5f5f5;min-height:100vh}.action-bar{display:flex;flex-direction:column;background-color:#fff;padding:.625rem .9375rem;margin-bottom:.625rem;position:sticky;top:0;z-index:100}.post-btn{display:flex;align-items:center;background-color:#f5f5f5;padding:.5rem .9375rem;border-radius:1.25rem;margin-bottom:.625rem}.post-icon{margin-right:.3125rem;font-size:1rem}.post-text{color:#666;font-size:.875rem}.filter-tabs{display:flex;justify-content:space-around;border-bottom:.03125rem solid #efefef}.tab-item{padding:.5rem .625rem;font-size:.875rem;color:#666;position:relative}.tab-item.active{color:#c8161e;font-weight:700}.tab-item.active:after{content:"";position:absolute;bottom:0;left:20%;width:60%;height:.1875rem;background-color:#c8161e;border-radius:.09375rem}.post-list{height:calc(100vh - 5.9375rem)}.post-item{background-color:#fff;margin-bottom:.625rem;padding:.9375rem}.post-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:.625rem}.user-info{display:flex;align-items:center}.avatar{width:2.5rem;height:2.5rem;border-radius:50%;margin-right:.625rem}.user-detail{display:flex;flex-direction:column}.username{font-size:.875rem;font-weight:700;color:#333;margin-bottom:.125rem}.user-tag{font-size:.625rem;color:#fff;background-color:#c8161e;padding:.0625rem .3125rem;border-radius:.3125rem;margin-left:.3125rem;margin-bottom:.125rem}.post-time{font-size:.6875rem;color:#999}.more-icon{font-size:1.125rem;color:#999}.post-content{margin-bottom:.625rem}.post-text{font-size:.875rem;color:#333;line-height:1.6;margin-bottom:.625rem}.post-images{display:flex;flex-wrap:wrap;margin-bottom:.625rem}.post-images-1 .post-image{width:100%;height:12.5rem;border-radius:.375rem}.post-images-2 .post-image,.post-images-4 .post-image{width:10.46875rem;height:10.46875rem;border-radius:.375rem;margin-right:.625rem;margin-bottom:.625rem}.post-images-3 .post-image,.post-images-multi .post-image{width:6.875rem;height:6.875rem;border-radius:.375rem;margin-right:.46875rem;margin-bottom:.46875rem}.post-images-2 .post-image:nth-child(2n),.post-images-4 .post-image:nth-child(2n),.post-images-3 .post-image:nth-child(3n),.post-images-multi .post-image:nth-child(3n){margin-right:0}.post-location{display:flex;align-items:center;margin-top:.3125rem}.location-icon{margin-right:.3125rem;font-size:.75rem}.location-text{font-size:.75rem;color:#007aff}.post-actions{display:flex;justify-content:space-around;padding:.625rem 0;border-top:.03125rem solid #efefef;border-bottom:.03125rem solid #efefef;margin-bottom:.625rem}.action-item{display:flex;align-items:center}.action-icon{font-size:1rem;margin-right:.3125rem}.action-icon.liked{color:#c8161e}.action-count{font-size:.75rem;color:#666}.post-comments{background-color:#f9f9f9;padding:.625rem;border-radius:.375rem}.comment-item{margin-bottom:.3125rem}.comment-username{font-size:.75rem;font-weight:700;color:#333}.comment-content{font-size:.75rem;color:#333}.view-more{text-align:center;margin-top:.3125rem}.view-more uni-text{font-size:.75rem;color:#999}.loading-state{text-align:center;padding:.9375rem 0}.loading-state uni-text{font-size:.75rem;color:#999}.post-modal{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1000}.modal-mask{position:absolute;width:100%;height:100%;background-color:rgba(0,0,0,.5)}.modal-content{position:absolute;bottom:0;width:100%;background-color:#fff;border-radius:.9375rem .9375rem 0 0;overflow:hidden}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:.9375rem;border-bottom:.03125rem solid #efefef}.modal-header uni-text{font-size:1rem;font-weight:700;color:#333}.close-btn{font-size:1.25rem;color:#999}.modal-body{padding:.9375rem}.post-textarea{width:100%;height:6.25rem;padding:.625rem;font-size:.875rem;border:.03125rem solid #efefef;border-radius:.375rem;margin-bottom:.625rem}.image-list{display:flex;flex-wrap:wrap}.image-item,.add-image{width:5rem;height:5rem;margin-right:.625rem;margin-bottom:.625rem;position:relative}.image-item uni-image{width:100%;height:100%;border-radius:.25rem}.delete-icon{position:absolute;top:-.3125rem;right:-.3125rem;width:1.25rem;height:1.25rem;background-color:rgba(0,0,0,.5);border-radius:50%;color:#fff;text-align:center;line-height:1.125rem;font-size:.75rem}.add-image{display:flex;justify-content:center;align-items:center;border:.03125rem dashed #cdcdcd;border-radius:.25rem}.add-icon{font-size:1.875rem;color:#cdcdcd}.location-picker{display:flex;align-items:center;margin-top:.625rem;padding:.625rem 0;border-top:.03125rem solid #efefef}.location-picker uni-text{font-size:.875rem;color:#666}.modal-footer{padding:.625rem .9375rem 1.5625rem}.publish-btn{background-color:#c8161e;color:#fff;font-size:1rem;height:2.5rem;line-height:2.5rem;border-radius:1.25rem}.publish-btn[disabled]{background-color:#f5f5f5;color:#999}

</style>
