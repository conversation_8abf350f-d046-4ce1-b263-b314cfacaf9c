if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then(a=>t.resolve(e()).then(()=>a),a=>t.resolve(e()).then(()=>{throw a}))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...a){uni.__log__?uni.__log__(e,t,...a):console[e].apply(console,[...a,t])}const a=t=>(a,i=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,a,i)},i=a("onShow"),l=a("onHide"),n=a("onLaunch"),o=a("onError"),c=a("onPageNotFound"),r=a("onLoad"),s={province:"",city:"请选择城市",district:"",street:"",fullAddress:"请选择城市",longitude:0,latitude:0},d="location_info",m=e.reactive({location:{...s},isLocating:!1,hasPermission:!1});m.location=function(){try{const e=uni.getStorageSync(d);if(e){const t=JSON.parse(e);return{...s,...t}}}catch(e){t("warn","at store/modules/location.ts:57","读取位置信息失败:",e)}return{...s}}();const u="041adb9cf3c9a5f127439604674ea4bd",v="https://restapi.amap.com";const p={get currentLocation(){return{...m.location}},get fullAddress(){return m.location.fullAddress},get isLocating(){return m.isLocating},get hasLocationPermission(){return m.hasPermission},setLocation(e){m.location={...s,...e},function(e){try{uni.setStorageSync(d,JSON.stringify(e))}catch(a){t("warn","at store/modules/location.ts:44","保存位置信息失败:",a)}}(m.location)},clearLocation(){m.location={...s};try{uni.removeStorageSync(d)}catch(e){t("warn","at store/modules/location.ts:195","清除位置信息失败:",e)}},async getCurrentLocation(){m.isLocating=!0;try{return new Promise((e,a)=>{const i={type:"wgs84",altitude:!1,geocode:!1,timeout:15e3,enableHighAccuracy:!0};t("log","at store/modules/location.ts:214","开始GPS定位，参数:",i),uni.getLocation({...i,success:async a=>{t("log","at store/modules/location.ts:219","GPS定位成功:",a),m.hasPermission=!0;const{longitude:i,latitude:l}=a;try{const a=await async function(e,a){return new Promise((i,l)=>{t("log","at store/modules/location.ts:82","开始逆地理编码:",`${e},${a}`),uni.request({url:`${v}/v3/geocode/regeo`,method:"GET",data:{key:u,location:`${e},${a}`,extensions:"base",coordsys:"wgs84"},success:e=>{t("log","at store/modules/location.ts:95","逆地理编码响应:",e);const a=e.data;if(a&&"1"===a.status&&a.regeocode){const e=a.regeocode.addressComponent,t=e.province||"";let l=e.city||"";Array.isArray(l)&&0===l.length&&(l=t),i({province:t,city:l,district:e.district||"",street:e.township||"",fullAddress:a.regeocode.formatted_address||""})}else t("error","at store/modules/location.ts:116","逆地理编码API返回错误:",a),l(new Error(`逆地理编码失败: ${(null==a?void 0:a.info)||"未知错误"}`))},fail:e=>{t("error","at store/modules/location.ts:121","逆地理编码请求失败:",e),l(new Error("逆地理编码请求失败"))}})})}(i,l),n={province:a.province||"",city:a.city||"",district:a.district||"",street:a.street||"",fullAddress:a.fullAddress||`${a.province}${a.city}${a.district}`,longitude:i,latitude:l};this.setLocation(n),e(n)}catch(n){t("warn","at store/modules/location.ts:239","逆地理编码失败:",n);const a={...m.location,longitude:i,latitude:l};this.setLocation(a),e(a)}},fail:async i=>{t("warn","at store/modules/location.ts:252","GPS定位失败，尝试IP定位:",i),m.hasPermission=!1;try{const a=await async function(){return new Promise((e,a)=>{t("log","at store/modules/location.ts:131","开始IP定位"),uni.request({url:`${v}/v3/ip`,method:"GET",data:{key:u},success:i=>{t("log","at store/modules/location.ts:140","IP定位响应:",i);const l=i.data;l&&"1"===l.status?e({province:l.province||"",city:l.city||""}):(t("error","at store/modules/location.ts:149","IP定位API返回错误:",l),a(new Error(`IP定位失败: ${(null==l?void 0:l.info)||"未知错误"}`)))},fail:e=>{t("error","at store/modules/location.ts:154","IP定位请求失败:",e),a(new Error("IP定位请求失败"))}})})}(),i={province:a.province||"",city:a.city||"",district:"",street:"",fullAddress:`${a.province}${a.city}`,longitude:0,latitude:0};this.setLocation(i),e(i)}catch(l){t("error","at store/modules/location.ts:270","IP定位也失败:",l),a(new Error("定位失败，请手动选择城市"))}},complete:()=>{m.isLocating=!1}})})}catch(e){throw m.isLocating=!1,e}},updatePermissionStatus(e){m.hasPermission=e}},g={development:{baseURL:"http://**************:8001/api",timeout:3e4},testing:{baseURL:"https://test-api.millennia-now.com/api",timeout:2e4},production:{baseURL:"https://api.millennia-now.com/api",timeout:15e3}};function h(){const e=function(){try{return"undefined"!=typeof __PRODUCTION__&&__PRODUCTION__?"production":"undefined"!=typeof __TESTING__&&__TESTING__?"testing":"development"}catch(e){return t("warn","at config/api.ts:83","获取App环境信息失败，使用开发环境配置:",e),"development"}return"development"}();return g[e]}function y(){return h().baseURL}const _=y(),E=async()=>{try{const e=await uni.request({url:`${_}/provinces`,method:"GET"});if(200===e.statusCode)return e.data.items;throw new Error("获取省份列表失败")}catch(e){throw t("error","at api/admin_divisions.ts:78","获取省份列表失败:",e),e}},w=async e=>{try{const t=await uni.request({url:`${_}/cities`,method:"GET",data:{province_id:e}});if(200===t.statusCode)return t.data.items;throw new Error("获取城市列表失败")}catch(a){throw t("error","at api/admin_divisions.ts:100","获取城市列表失败:",a),a}},N=async(e,a)=>{try{const t=await uni.request({url:`${_}/districts`,method:"GET",data:{province_id:e,city_id:a}});if(200===t.statusCode)return t.data.items;throw new Error("获取区县列表失败")}catch(i){throw t("error","at api/admin_divisions.ts:123","获取区县列表失败:",i),i}},f=y();function k(e){return new Promise((a,i)=>{const l=e.url.startsWith("http")?e.url:`${f}${e.url}`,n={"Content-Type":"application/json",...e.header};try{const e=uni.getStorageSync("access_token");e&&(n.Authorization=`Bearer ${e}`)}catch(c){t("warn","at api/heritage.ts:34","获取token失败:",c)}let o=e.data;e.includeBinaryUrls&&"GET"===e.method&&(o={...o,include_binary_urls:!0}),uni.request({url:l,method:e.method||"GET",data:o,header:n,timeout:3e4,success:n=>{var o;t("log","at api/heritage.ts:54",`[${e.method||"GET"}] ${l}:`,n),n.statusCode>=200&&n.statusCode<300?a(n.data):(t("error","at api/heritage.ts:60","请求失败:",n),i(new Error(`HTTP ${n.statusCode}: ${(null==(o=n.data)?void 0:o.message)||"请求失败"}`)))},fail:a=>{var n;t("error","at api/heritage.ts:65",`[${e.method||"GET"}] ${l} 请求失败:`,{errMsg:a.errMsg,errno:a.errno,errCode:a.errCode,statusCode:a.statusCode,error:a}),(null==(n=a.errMsg)?void 0:n.includes("ERR_CONNECTION_RESET"))||-101===a.errno?uni.showToast({title:"服务器连接中断，请稍后重试",icon:"none",duration:3e3}):uni.showToast({title:"网络连接失败，请检查网络设置",icon:"none",duration:3e3}),i(new Error(a.errMsg||"网络请求失败"))}})})}async function V(e,a=!0){try{const i={};e.province_id&&(i.province_id=e.province_id),e.city_id&&(i.city_id=e.city_id),e.district_id&&(i.district_id=e.district_id),i.include_binary_data=a;const l=await k({url:"/heritage/places/by-region/full",method:"GET",data:i});return l&&"object"==typeof l&&"statusCode"in l&&404===l.statusCode?(t("log","at api/heritage.ts:245","当前区域无数据"),null):l}catch(i){return t("error","at api/heritage.ts:252","获取文化遗产页面数据失败:",i),null}}async function x(e,a=!0){try{const i="string"==typeof e?parseInt(e,10):e;if(isNaN(i))return t("error","at api/heritage.ts:302","无效的place ID:",e),null;const l=await k({url:`/heritage/places/${i}/full`,method:"GET",data:{include_binary_data:a}});return l&&"object"==typeof l&&"statusCode"in l&&404===l.statusCode?(t("log","at api/heritage.ts:315","当前区域无数据"),null):l}catch(i){return t("error","at api/heritage.ts:322","获取文化遗产页面数据失败:",i),null}}const C={provinceId:0,provinceName:"",cityId:0,cityName:"",districtId:0,districtName:""},B="region_ids",b=e.reactive({regionIds:{...C},isLoading:!1,heritagePlace:null,heritageLoading:!1});async function I(e,a){try{const t=await w(e);let i=t.find(e=>e.name===a);if(i)return i;const l=a.replace(/[市区县自治州地区盟]/g,"");return i=t.find(e=>e.name.replace(/[市区县自治州地区盟]/g,"")===l||e.name.includes(l)||l.includes(e.name.replace(/[市区县自治州地区盟]/g,""))),i||null}catch(i){return t("error","at store/modules/region.ts:121","匹配城市失败:",i),null}}async function D(e,a){try{const i=await(async(e,a)=>{try{const t=await uni.request({url:`${_}/districts/search`,method:"GET",data:{province_id:e,district_name:a}});if(200===t.statusCode)return t.data.items;throw new Error("搜索区县失败")}catch(i){throw t("error","at api/admin_divisions.ts:146","搜索区县失败:",i),i}})(e,a);if(0===i.length)return t("warn","at store/modules/region.ts:162","未找到匹配的区县:",a),null;const l=i[0];t("log","at store/modules/region.ts:168","找到匹配的区县:",l);const n=(await w(e)).find(e=>e.city_id===l.city_id);return n?(t("log","at store/modules/region.ts:179","找到区县对应的城市:",n),{district:l,city:n}):(t("warn","at store/modules/region.ts:175","未找到区县对应的城市:",l.city_id),null)}catch(i){return t("error","at store/modules/region.ts:182","通过区县查找城市失败:",i),null}}b.regionIds=function(){try{const e=uni.getStorageSync(B);if(e){const t=JSON.parse(e);return{...C,...t}}}catch(e){t("warn","at store/modules/region.ts:66","读取区域ID信息失败:",e)}return{...C}}();const T={get currentRegionIds(){return{...b.regionIds}},get isLoading(){return b.isLoading},setRegionIds(e){b.regionIds={...C,...e},function(e){try{uni.setStorageSync(B,JSON.stringify(e))}catch(a){t("warn","at store/modules/region.ts:53","保存区域ID信息失败:",a)}}(b.regionIds),t("log","at store/modules/region.ts:203","区域ID已更新:",b.regionIds)},clearRegionIds(){b.regionIds={...C};try{uni.removeStorageSync(B)}catch(e){t("warn","at store/modules/region.ts:212","清除区域ID信息失败:",e)}t("log","at store/modules/region.ts:214","区域ID已清空")},async matchRegionIdsByLocation(e){b.isLoading=!0;try{t("log","at store/modules/region.ts:229","开始匹配区域ID:",e);const a=await async function(e){try{const t=await E();let a=t.find(t=>t.name===e);if(a)return a;const i=e.replace(/[省市自治区特别行政区]/g,"");return a=t.find(e=>e.name.replace(/[省市自治区特别行政区]/g,"")===i||e.name.includes(i)||i.includes(e.name.replace(/[省市自治区特别行政区]/g,""))),a||null}catch(a){return t("error","at store/modules/region.ts:95","匹配省份失败:",a),null}}(e.province);if(!a)return t("warn","at store/modules/region.ts:234","未找到匹配的省份:",e.province),null;t("log","at store/modules/region.ts:238","匹配到省份:",a);const i=["北京","上海","天津","重庆"].some(e=>a.name.includes(e));let l=null,n=null;if(i&&e.district){t("log","at store/modules/region.ts:250","检测到直辖市，尝试通过区县反推城市");const i=await D(a.province_id,e.district);if(i)l=i.city,n=i.district,t("log","at store/modules/region.ts:258","直辖市通过区县匹配成功 - 城市:",l,"区县:",n);else if(t("log","at store/modules/region.ts:260","直辖市通过区县匹配失败，尝试传统方式"),l=await I(a.province_id,e.city),!l&&(l=await I(a.province_id,a.name),!l)){const e=await w(a.province_id);t("log","at store/modules/region.ts:271","直辖市城市列表:",e),e.length>0&&(l=e[0],t("log","at store/modules/region.ts:274","使用直辖市第一个城市:",l))}}else l=await I(a.province_id,e.city);if(!l){t("warn","at store/modules/region.ts:285","未找到匹配的城市:",e.city);const i={provinceId:a.province_id,provinceName:a.name,cityId:0,cityName:"",districtId:0,districtName:""};return this.setRegionIds(i),i}t("log","at store/modules/region.ts:299","匹配到城市:",l),!n&&e.district&&(n=await async function(e,a,i){try{const t=await N(e,a);let l=t.find(e=>e.name===i);if(l)return l;const n=i.replace(/[区县市旗]/g,"");return l=t.find(e=>e.name.replace(/[区县市旗]/g,"")===n||e.name.includes(n)||n.includes(e.name.replace(/[区县市旗]/g,""))),l||null}catch(l){return t("error","at store/modules/region.ts:147","匹配区县失败:",l),null}}(a.province_id,l.city_id,e.district),t("log","at store/modules/region.ts:308","匹配到区县:",n));const o={provinceId:a.province_id,provinceName:a.name,cityId:l.city_id,cityName:l.name,districtId:(null==n?void 0:n.district_id)||0,districtName:(null==n?void 0:n.name)||""};return this.setRegionIds(o),t("log","at store/modules/region.ts:322","区域ID匹配成功:",o),o}catch(a){return t("error","at store/modules/region.ts:326","匹配区域ID失败:",a),null}finally{b.isLoading=!1}},setSelectedRegionIds(e,a,i=null){const l={provinceId:(null==e?void 0:e.province_id)||0,provinceName:(null==e?void 0:e.name)||"",cityId:(null==a?void 0:a.city_id)||0,cityName:(null==a?void 0:a.name)||"",districtId:(null==i?void 0:i.district_id)||0,districtName:(null==i?void 0:i.name)||""};this.setRegionIds(l),this.clearHeritagePlace(),t("log","at store/modules/region.ts:358","手动设置区域ID:",l),t("log","at store/modules/region.ts:359","已清空heritage place，将使用新区域ID获取数据")},get fullRegionName(){const{provinceName:e,cityName:t,districtName:a}=b.regionIds;let i=e;return t&&t!==e&&(i+=t),a&&(i+=a),i||"请选择地区"},get hasCompleteRegion(){return!(!b.regionIds.provinceId||!b.regionIds.cityId)},get hasDistrict(){return!!b.regionIds.districtId},get currentHeritagePlace(){return b.heritagePlace},get isHeritageLoading(){return b.heritageLoading},async fetchHeritagePlace(){const{provinceId:e,cityId:a,districtId:i}=b.regionIds;if(!e)return t("warn","at store/modules/region.ts:412","没有省份ID，无法获取heritage place"),null;b.heritageLoading=!0;try{t("log","at store/modules/region.ts:419","开始获取heritage place:",{provinceId:e,cityId:a,districtId:i});const l=await async function(e,a=!0){try{const i={};e.province_id&&(i.province_id=e.province_id),e.city_id&&(i.city_id=e.city_id),e.district_id&&(i.district_id=e.district_id);const l=await k({url:"/heritage/places/by-region",method:"GET",data:i,includeBinaryUrls:a});return l&&"object"==typeof l&&"statusCode"in l&&404===l.statusCode?(t("log","at api/heritage.ts:204","当前区域未构建数据"),null):l}catch(i){return t("error","at api/heritage.ts:211","获取文化遗产地点信息失败:",i),null}}({province_id:e||void 0,city_id:a||void 0,district_id:i||void 0});return b.heritagePlace=l,t("log","at store/modules/region.ts:428","获取heritage place成功:",l),l}catch(l){return t("error","at store/modules/region.ts:432","获取heritage place失败:",l),b.heritagePlace=null,null}finally{b.heritageLoading=!1}},clearHeritagePlace(){b.heritagePlace=null,t("log","at store/modules/region.ts:445","heritage place信息已清空")},async updateRegionAndFetchHeritage(e){return this.setRegionIds(e),await this.fetchHeritagePlace()}},S=(e,t)=>{const a=e.__vccOpts||e;for(const[i,l]of t)a[i]=l;return a},M=S(e.defineComponent({__name:"index",setup(a){const i=e.ref(!1),l=()=>{i.value=!i.value,i.value||setTimeout(()=>{},50)},n=e.computed(()=>{const e=p.currentLocation;return e.district?e.district:e.city&&"请选择城市"!==e.city?e.city:e.province||"选择城市"}),o=()=>{uni.navigateTo({url:"/pages/location/select"})},c=()=>{const e=T.currentRegionIds,t=`当前区域ID信息：\n省份：${e.provinceName}(${e.provinceId||"未设置"})\n城市：${e.cityName}(${e.cityId||"未设置"})\n区县：${e.districtName}(${e.districtId||"未设置"})\n\n完整名称：${T.fullRegionName}\n是否完整：${T.hasCompleteRegion?"是":"否"}\n有区县信息：${T.hasDistrict?"是":"否"}`;uni.showModal({title:"区域ID信息",content:t,showCancel:!1,confirmText:"知道了"})},r=()=>{uni.navigateTo({url:"/pages/search/index"})},s=()=>{var e,a,l;t("log","at pages/index/index.vue:270","点击了云游文化按钮"),i.value=!1;const n=T.currentRegionIds,o=T.fullRegionName||"默认地区",c=["from=miniapp",`regionName=${encodeURIComponent(o)}`,`provinceId=${encodeURIComponent((null==(e=n.provinceId)?void 0:e.toString())||"")}`,`cityId=${encodeURIComponent((null==(a=n.cityId)?void 0:a.toString())||"")}`,`districtId=${encodeURIComponent((null==(l=n.districtId)?void 0:l.toString())||"")}`].join("&"),r=`${y().replace(":8443/api",":5173/")}?${c}`;t("log","at pages/index/index.vue:292","跳转URL:",r),t("log","at pages/index/index.vue:295","App环境，使用plus打开外部浏览器"),plus.runtime.openURL(r)},d=()=>{t("log","at pages/index/index.vue:329","点击了千载·今知AI问答按钮"),i.value=!1,uni.showToast({title:"功能开发中...",icon:"none",duration:2e3})},m=()=>{t("log","at pages/index/index.vue:343","点击了乘着轨道游重庆按钮"),i.value=!1,uni.showToast({title:"正在加载轨道旅游图...",icon:"loading",duration:1e3}),setTimeout(()=>{t("log","at pages/index/index.vue:356","跳转到重庆轨道2号线页面（应用内）"),uni.navigateTo({url:"/pages/rail/cq-line2",success:()=>{t("log","at pages/index/index.vue:362","成功跳转到重庆轨道2号线页面")},fail:e=>{t("error","at pages/index/index.vue:365","跳转失败:",e),uni.showToast({title:"页面跳转失败，请重试",icon:"none",duration:2e3})}})},800)},u=e=>{let t="/pages/culture/history";switch(e){case"history":t="/pages/culture/history";break;case"culture":t="/pages/culture/heritage-content";break;case"memory":t="/pages/culture/memory-content"}uni.navigateTo({url:t})},v=e.ref(!1),g=e.ref(""),h=e.ref(0);let _=null,E=null;const w=e.ref(!1),N=e.ref(!1);function f(e){if(!e)return;E&&clearTimeout(E),_&&clearTimeout(_);const a=function(e){let t=T.fullRegionName||"";if(!t){const a=p.currentLocation;t=[a.province||"",a.city||"",a.district||e].filter(Boolean).join("")}return t}(e);t("log","at pages/index/index.vue:422","显示区域名称:",a),g.value=a||e,v.value=!0,h.value=0,w.value=!1,E=setTimeout(()=>{h.value=1},600),E=setTimeout(()=>{h.value=2},1200),E=setTimeout(()=>{h.value=3},1800),E=setTimeout(()=>{h.value=4},3e3),_=setTimeout(()=>{v.value=!1,h.value=0,setTimeout(()=>{w.value=!0},300)},6e3)}function k(){_&&clearTimeout(_),E&&clearTimeout(E),v.value=!1,h.value=0,w.value=!0}const V=y().replace("/api",""),x=`${V}/static/image/bak.png`,C=`${V}/static/image/wenmai.png`,B=`${V}/static/image/chuancheng.png`;e.watch(()=>p.currentLocation.fullAddress,(e,t)=>{if(e&&e!==t){f(p.currentLocation.district)}}),e.watch(()=>p.currentLocation.district,(e,a)=>{e&&e!==a&&v.value&&(t("log","at pages/index/index.vue:508","动画进行中检测到城市变化，重新触发动画:",e),f(e))});return e.onMounted(()=>{(()=>{const e=uni.getSystemInfoSync(),t=e.screenHeight,a=e.screenWidth;N.value=a<=480||t<=600})();const e=p.currentLocation.district;e?f(e):setTimeout(()=>{w.value=!0},500),setTimeout(()=>{N.value&&setTimeout(()=>{N.value=!1},3e3)},2e3)}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"container",style:e.normalizeStyle({backgroundImage:`url(${x})`})},[e.createElementVNode("view",{class:"location-search-bar"},[e.createElementVNode("view",{class:"location-section",onClick:o},[e.createElementVNode("text",{class:"location-icon"},"📍"),e.createElementVNode("text",{class:"location-name"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"location-arrow"},"▼")]),e.unref(T).hasCompleteRegion?(e.openBlock(),e.createElementBlock("view",{key:0,class:"debug-button",onClick:c},[e.createElementVNode("text",{class:"debug-text"},"ID")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"search-section",onClick:r},[e.createElementVNode("text",{class:"search-icon"},"🔍"),e.createElementVNode("text",{class:"search-placeholder"},"搜索景点、活动、美食...")])]),v.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"welcome-popup",onClick:k},[e.createElementVNode("view",{class:"welcome-content"},[e.createElementVNode("view",{class:e.normalizeClass(["welcome-title",{step1:h.value>=1}])},"欢迎来到",2),e.createElementVNode("view",{class:e.normalizeClass(["welcome-district",{step2:h.value>=2}])},[e.createElementVNode("span",{class:"district-text"},e.toDisplayString(g.value),1),e.createElementVNode("view",{class:"particle-container"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{key:t,class:"particle",style:e.normalizeStyle({"--particle-delay":.2+.1*t+"s"})},null,4)),64))])],2),e.createElementVNode("view",{class:e.normalizeClass(["welcome-desc",{step3:h.value>=3}])},"让我们一起探索当地的文化吧",2),e.createElementVNode("view",{class:e.normalizeClass(["welcome-seal",{show:h.value>=4}])},null,2)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"ink-animation-area"},[N.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"scroll-indicator"},[e.createElementVNode("view",{class:"scroll-arrow"}),e.createElementVNode("text",{class:"scroll-text"},"向下滑动查看更多")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"ink-sections-container"},[e.createElementVNode("view",{class:e.normalizeClass(["ink-section history-section",{"animate-in":w.value}]),onClick:a[0]||(a[0]=e=>u("history"))},[e.createElementVNode("view",{class:"scroll-container"},[e.createElementVNode("view",{class:"scroll-handle top-handle"}),e.createElementVNode("view",{class:"scroll-paper",style:e.normalizeStyle({backgroundImage:`url('${C}')`})},[e.createElementVNode("view",{class:"edge-shadow-left"}),e.createElementVNode("view",{class:"edge-shadow-right"}),e.createElementVNode("view",{class:"section-content"},[e.createElementVNode("text",{class:"ink-title"},"历史文脉"),e.createElementVNode("view",{class:"ink-decoration"},[e.createElementVNode("view",{class:"decoration-line"}),e.createElementVNode("text",{class:"decoration-symbol"},"探索地区发展历史"),e.createElementVNode("view",{class:"decoration-line"})])]),e.createElementVNode("view",{class:"ink-animation"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{class:"ink-drop",key:t})),64))])],4),e.createElementVNode("view",{class:"scroll-handle bottom-handle"})])],2),e.createElementVNode("view",{class:e.normalizeClass(["ink-section culture-section",{"animate-in":w.value}]),onClick:a[1]||(a[1]=e=>u("culture"))},[e.createElementVNode("view",{class:"culture-container"},[e.createElementVNode("view",{class:"culture-paper",style:e.normalizeStyle({backgroundImage:`url('${B}')`})},[e.createElementVNode("view",{class:"edge-shadow-top"}),e.createElementVNode("view",{class:"edge-shadow-bottom"}),e.createElementVNode("view",{class:"section-content"},[e.createElementVNode("text",{class:"ink-title"},"文化传承"),e.createElementVNode("view",{class:"ink-decoration"},[e.createElementVNode("view",{class:"decoration-line"}),e.createElementVNode("text",{class:"decoration-symbol"},"传统文化数字保护"),e.createElementVNode("view",{class:"decoration-line"})])]),e.createElementVNode("view",{class:"ink-animation culture-ink"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{class:"ink-splash",key:t})),64))])],4)])],2),e.createElementVNode("view",{class:e.normalizeClass(["ink-section memory-section",{"animate-in":w.value}]),onClick:a[2]||(a[2]=e=>u("memory"))},[e.createElementVNode("view",{class:"memory-container"},[e.createElementVNode("view",{class:"memory-paper"},[e.createElementVNode("view",{class:"section-content",style:e.normalizeStyle({backgroundImage:`url('${e.unref(y)().replace("/api","")}/static/image/memory.jpg')`})},[e.createElementVNode("text",{class:"ink-title"},"当代记忆"),e.createElementVNode("view",{class:"ink-decoration"},[e.createElementVNode("view",{class:"decoration-line"}),e.createElementVNode("text",{class:"decoration-symbol"},"城市影像数字档案"),e.createElementVNode("view",{class:"decoration-line"})])],4),e.createElementVNode("view",{class:"ink-animation"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{class:"ink-memory",key:t})),64))])])])],2)]),e.createElementVNode("view",{class:"menu-container"},[i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"menu-backdrop",onClick:l})):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"floating-menu"},[e.createElementVNode("view",{class:"menu-button",onClick:e.withModifiers(l,["stop"])},[e.createElementVNode("text",{class:"menu-icon"},"☰")]),i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["menu-panel",{show:i.value}])},[e.createElementVNode("view",{class:"menu-item",onClick:s},[e.createElementVNode("text",{class:"menu-item-icon"},"🌍"),e.createElementVNode("text",{class:"menu-item-text"},"云游文化")]),e.createElementVNode("view",{class:"menu-item",onClick:d},[e.createElementVNode("text",{class:"menu-item-icon"},"🤖"),e.createElementVNode("text",{class:"menu-item-text"},"千载·今知AI问答")]),e.createElementVNode("view",{class:"menu-item",onClick:m},[e.createElementVNode("text",{class:"menu-item-icon"},"🚇"),e.createElementVNode("text",{class:"menu-item-text"},"乘着轨道游重庆")])],2)):e.createCommentVNode("",!0)])])])],4))}}),[["__scopeId","data-v-24c0937c"]]),A=e.defineComponent({__name:"index",setup(t){const a=e.ref(["推荐","关注","附近"]),i=e.ref(0),l=e.ref([{id:"1",username:"武汉文旅",isOfficial:!0,avatar:"/static/images/avatars/official.jpg",time:"2小时前",content:"【黄鹤楼秋色正好】秋高气爽，登楼远眺，长江滚滚，气势磅礴。现在正是黄鹤楼最美的季节，各位游客千万不要错过这个绝佳的游览时机！",images:["/static/images/posts/post1-1.jpg","/static/images/posts/post1-2.jpg","/static/images/posts/post1-3.jpg"],location:"黄鹤楼",likes:287,comments:42,isLiked:!1,commentList:[{username:"旅行者小王",content:"美景美如画，下周一定去打卡！"},{username:"江城老李",content:"这个角度拍得真不错，请问是在哪个位置拍的？"}]},{id:"2",username:"小明同学",isOfficial:!1,avatar:"/static/images/avatars/user1.jpg",time:"昨天",content:"东湖绿道骑行太舒服了！一路上风景如画，空气清新，绝对是周末放松的好去处。分享一些沿途的美景，希望大家也能来体验！",images:["/static/images/posts/post2-1.jpg","/static/images/posts/post2-2.jpg","/static/images/posts/post2-3.jpg","/static/images/posts/post2-4.jpg"],location:"东湖绿道",likes:156,comments:23,isLiked:!0,commentList:[{username:"绿道骑士",content:"天气好的时候去真是享受，路线请问全程多长？"},{username:"摄影爱好者",content:"第三张照片的构图太棒了！"}]},{id:"3",username:"美食猎人",isOfficial:!1,avatar:"/static/images/avatars/user2.jpg",time:"3天前",content:"户部巷美食一日游！从早吃到晚，热干面、小龙虾、豆皮、鸭脖...样样都不能错过！武汉美食真的太丰富了，吃货们快来打卡~",images:["/static/images/posts/post3-1.jpg","/static/images/posts/post3-2.jpg"],location:"户部巷",likes:321,comments:67,isLiked:!1,commentList:[{username:"吃货小李",content:"看饿了！请问第一张图是哪家店的热干面？"},{username:"老武汉",content:"户部巷人气旺，建议错峰前往，不然要排很久队"},{username:"旅游达人",content:"收藏了，下次去武汉一定按图索骥！"}]}]),n=e.ref(!1),o=e.ref(!1),c=e.ref(!1),r=e.ref(!1),s=e.ref({content:"",images:[],location:""}),d=e.computed(()=>""!==s.value.content.trim()||s.value.images.length>0),m=()=>{n.value||c.value||(n.value=!0,setTimeout(()=>{if(l.value.length>=15)c.value=!0;else{const e=JSON.parse(JSON.stringify(l.value.slice(0,2)));e.forEach((e,t)=>{e.id=`new-${l.value.length+t}`,e.time="刚刚"}),l.value=[...l.value,...e]}n.value=!1},1e3))},u=()=>{setTimeout(()=>{o.value=!1,uni.showToast({title:"刷新成功",icon:"none"})},1e3)},v=e=>{uni.showToast({title:"评论功能开发中",icon:"none"})},p=()=>{r.value=!0},g=()=>{r.value=!1,s.value={content:"",images:[],location:""}},h=()=>{uni.chooseImage({count:9-s.value.images.length,success:e=>{s.value.images=[...s.value.images,...e.tempFilePaths]}})},y=()=>{uni.chooseLocation({success:e=>{s.value.location=e.name},fail:()=>{uni.showToast({title:"获取位置失败",icon:"none"})}})},_=()=>{if(!d.value)return;const e={id:`new-${Date.now()}`,username:"我",isOfficial:!1,avatar:"/static/images/avatars/me.jpg",time:"刚刚",content:s.value.content,images:s.value.images,location:s.value.location,likes:0,comments:0,isLiked:!1,commentList:[]};l.value.unshift(e),g(),uni.showToast({title:"发布成功",icon:"success"})};return(t,E)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"action-bar"},[e.createElementVNode("view",{class:"post-btn",onClick:p},[e.createElementVNode("text",{class:"post-icon"},"✏️"),e.createElementVNode("text",{class:"post-text"},"发布动态")]),e.createElementVNode("view",{class:"filter-tabs"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,(t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["tab-item",i.value===a?"active":""]),onClick:e=>(e=>{i.value!==e&&(i.value=e,o.value=!0,setTimeout(()=>{o.value=!1},1e3))})(a)},[e.createElementVNode("text",null,e.toDisplayString(t),1)],10,["onClick"]))),128))])]),e.createElementVNode("scroll-view",{"scroll-y":"",class:"post-list",onScrolltolower:m,"refresher-enabled":"","refresher-triggered":o.value,onRefresherrefresh:u},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"post-item",key:a},[e.createElementVNode("view",{class:"post-header"},[e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("image",{class:"avatar",src:t.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"user-detail"},[e.createElementVNode("text",{class:"username"},e.toDisplayString(t.username),1),t.isOfficial?(e.openBlock(),e.createElementBlock("text",{key:0,class:"user-tag"},"官方")):e.createCommentVNode("",!0),e.createElementVNode("text",{class:"post-time"},e.toDisplayString(t.time),1)])]),e.createElementVNode("text",{class:"more-icon"},"⋮")]),e.createElementVNode("view",{class:"post-content"},[e.createElementVNode("text",{class:"post-text"},e.toDisplayString(t.content),1),e.createElementVNode("view",{class:e.normalizeClass(["post-images",`post-images-${t.images.length>4?"multi":t.images.length}`])},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.images,(a,i)=>(e.openBlock(),e.createElementBlock("image",{key:i,src:a,mode:"aspectFill",class:"post-image",onClick:e=>{return a=t.images,l=i,void uni.previewImage({urls:a,current:a[l]});var a,l}},null,8,["src","onClick"]))),128))],2),t.location?(e.openBlock(),e.createElementBlock("view",{key:0,class:"post-location"},[e.createElementVNode("text",{class:"location-icon"},"📍"),e.createElementVNode("text",{class:"location-text"},e.toDisplayString(t.location),1)])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"post-actions"},[e.createElementVNode("view",{class:"action-item",onClick:e=>(e=>{const t=l.value[e];t.isLiked=!t.isLiked,t.likes+=t.isLiked?1:-1})(a)},[e.createElementVNode("text",{class:e.normalizeClass(["action-icon",t.isLiked?"liked":""])},e.toDisplayString(t.isLiked?"❤️":"🤍"),3),e.createElementVNode("text",{class:"action-count"},e.toDisplayString(t.likes),1)],8,["onClick"]),e.createElementVNode("view",{class:"action-item",onClick:e=>v(t.id)},[e.createElementVNode("text",{class:"action-icon"},"💬"),e.createElementVNode("text",{class:"action-count"},e.toDisplayString(t.comments),1)],8,["onClick"]),e.createElementVNode("view",{class:"action-item",onClick:e=>{uni.showToast({title:"分享功能开发中",icon:"none"})}},[e.createElementVNode("text",{class:"action-icon"},"🔄"),e.createElementVNode("text",{class:"action-count"},"分享")],8,["onClick"])]),t.commentList&&t.commentList.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"post-comments"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.commentList,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"comment-item",key:a},[e.createElementVNode("text",{class:"comment-username"},e.toDisplayString(t.username)+"：",1),e.createElementVNode("text",{class:"comment-content"},e.toDisplayString(t.content),1)]))),128)),t.comments>t.commentList.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"view-more",onClick:e=>v(t.id)},[e.createElementVNode("text",null,"查看全部"+e.toDisplayString(t.comments)+"条评论",1)],8,["onClick"])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)]))),128)),n.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-state"},[e.createElementVNode("text",null,"加载中...")])):e.createCommentVNode("",!0),!n.value&&c.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"loading-state"},[e.createElementVNode("text",null,"没有更多内容了")])):e.createCommentVNode("",!0)],40,["refresher-triggered"]),r.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"post-modal"},[e.createElementVNode("view",{class:"modal-mask",onClick:g}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",null,"发布动态"),e.createElementVNode("text",{class:"close-btn",onClick:g},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.withDirectives(e.createElementVNode("textarea",{class:"post-textarea","onUpdate:modelValue":E[0]||(E[0]=e=>s.value.content=e),placeholder:"分享你的旅行体验...",maxlength:"500"},null,512),[[e.vModelText,s.value.content]]),e.createElementVNode("view",{class:"image-picker"},[e.createElementVNode("view",{class:"image-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value.images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"image-item",key:a},[e.createElementVNode("image",{src:t,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("text",{class:"delete-icon",onClick:e=>(e=>{s.value.images.splice(e,1)})(a)},"×",8,["onClick"])]))),128)),s.value.images.length<9?(e.openBlock(),e.createElementBlock("view",{key:0,class:"add-image",onClick:h},[e.createElementVNode("text",{class:"add-icon"},"+")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"location-picker",onClick:y},[e.createElementVNode("text",{class:"location-icon"},"📍"),s.value.location?(e.openBlock(),e.createElementBlock("text",{key:0},e.toDisplayString(s.value.location),1)):(e.openBlock(),e.createElementBlock("text",{key:1},"添加位置"))])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"publish-btn",onClick:_,disabled:!d.value},"发布",8,["disabled"])])])])):e.createCommentVNode("",!0)]))}}),P=y();var F=(e=>(e.GUEST="GUEST",e.DISTRICT_ADMIN="DISTRICT_ADMIN",e.CITY_ADMIN="CITY_ADMIN",e.PROVINCE_ADMIN="PROVINCE_ADMIN",e.SUPER_ADMIN="SUPER_ADMIN",e))(F||{});const L="access_token",$="refresh_token",U="user_info",R=e.reactive({user:null,isAuthenticated:!1,isLoading:!1});function q(e){try{uni.setStorageSync(L,e.access_token),uni.setStorageSync($,e.refresh_token),uni.setStorageSync(U,JSON.stringify(e.user)),R.user=e.user,R.isAuthenticated=!0}catch(a){t("error","at store/modules/auth.ts:36","保存认证信息失败:",a)}}function j(){try{uni.removeStorageSync(L),uni.removeStorageSync($),uni.removeStorageSync(U),R.user=null,R.isAuthenticated=!1}catch(e){t("error","at store/modules/auth.ts:69","清除认证信息失败:",e)}}const O={get currentUser(){return R.user},get isAuthenticated(){return R.isAuthenticated},get isLoading(){return R.isLoading},get isAdmin(){return!!R.user&&["DISTRICT_ADMIN","CITY_ADMIN","PROVINCE_ADMIN","SUPER_ADMIN"].includes(R.user.role)},get isSuperAdmin(){var e;return"SUPER_ADMIN"===(null==(e=R.user)?void 0:e.role)},get userRoleDisplayName(){if(!R.user)return"未登录";return{GUEST:"游客",DISTRICT_ADMIN:"区县管理员",CITY_ADMIN:"市级管理员",PROVINCE_ADMIN:"省级管理员",SUPER_ADMIN:"超级管理员"}[R.user.role]||R.user.role},async init(){if(function(){try{const e=uni.getStorageSync(L),a=uni.getStorageSync(U);if(e&&a){const e=JSON.parse(a);t("log","at store/modules/auth.ts:48","🔍 从本地存储加载的用户信息:",e),t("log","at store/modules/auth.ts:49","🔍 本地存储的模块权限:",e.module_permissions),R.user=e,R.isAuthenticated=!0}}catch(e){t("error","at store/modules/auth.ts:54","加载认证信息失败:",e),j()}}(),R.isAuthenticated)try{R.isLoading=!0;const e=await(async()=>{var e;try{const t=uni.getStorageSync("access_token"),a=await uni.request({url:`${P}/auth/me`,method:"GET",header:{Authorization:`Bearer ${t}`}});if(200===a.statusCode)return a.data;throw new Error((null==(e=a.data)?void 0:e.detail)||"获取用户信息失败")}catch(a){throw t("error","at api/auth.ts:196","获取用户信息失败:",a),a}})();t("log","at store/modules/auth.ts:123","🔍 AuthManager 获取的用户信息:",e),t("log","at store/modules/auth.ts:124","🔍 模块权限字段:",e.module_permissions),R.user=e,uni.setStorageSync(U,JSON.stringify(e))}catch(e){t("warn","at store/modules/auth.ts:128","获取用户信息失败，可能token已过期:",e),j()}finally{R.isLoading=!1}},async loginWithWechat(e,a,i){try{R.isLoading=!0;const l=await(async e=>{var a;try{const t=await uni.request({url:`${P}/auth/wechat/login`,method:"POST",data:e,header:{"Content-Type":"application/json"}});if(200===t.statusCode)return t.data;throw new Error((null==(a=t.data)?void 0:a.detail)||"登录失败")}catch(i){throw t("error","at api/auth.ts:99","微信登录失败:",i),i}})({code:e,encrypted_data:a,iv:i});q(l),t("log","at store/modules/auth.ts:147","微信登录成功:",l.user)}catch(l){throw t("error","at store/modules/auth.ts:149","微信登录失败:",l),l}finally{R.isLoading=!1}},async logout(){try{const e=uni.getStorageSync($);e&&await(async e=>{var a;try{const t=await uni.request({url:`${P}/auth/logout`,method:"POST",data:{refresh_token:e},header:{"Content-Type":"application/json"}});if(200!==t.statusCode)throw new Error((null==(a=t.data)?void 0:a.detail)||"登出失败")}catch(i){throw t("error","at api/auth.ts:173","登出失败:",i),i}})(e)}catch(e){t("warn","at store/modules/auth.ts:164","服务端登出失败:",e)}finally{j(),t("log","at store/modules/auth.ts:167","用户已登出")}},async loginWithPassword(e,a){R.isLoading=!0;try{const i={phone:e,password:a},l=await(async e=>{var a;try{const t=await uni.request({url:`${P}/auth/password/login`,method:"POST",data:e,header:{"Content-Type":"application/json"}});if(200===t.statusCode)return t.data;throw new Error((null==(a=t.data)?void 0:a.detail)||"登录失败")}catch(i){throw t("error","at api/auth.ts:257","账号密码登录失败:",i),i}})(i);q(l),t("log","at store/modules/auth.ts:179","账号密码登录成功")}catch(i){throw t("error","at store/modules/auth.ts:181","账号密码登录失败:",i),i}finally{R.isLoading=!1}},async register(e,a,i){R.isLoading=!0;try{const l={phone:e,password:a,nickname:i},n=await(async e=>{var a;try{const t=await uni.request({url:`${P}/auth/register`,method:"POST",data:e,header:{"Content-Type":"application/json"}});if(201===t.statusCode)return t.data;throw new Error((null==(a=t.data)?void 0:a.detail)||"注册失败")}catch(i){throw t("error","at api/auth.ts:280","用户注册失败:",i),i}})(l);q(n),t("log","at store/modules/auth.ts:196","用户注册成功")}catch(l){throw t("error","at store/modules/auth.ts:198","用户注册失败:",l),l}finally{R.isLoading=!1}},updateUser(e){R.user=e,uni.setStorageSync(U,JSON.stringify(e))},hasPermission(e){if(!R.user)return!1;const t={GUEST:0,DISTRICT_ADMIN:1,CITY_ADMIN:2,PROVINCE_ADMIN:3,SUPER_ADMIN:4};return(t[R.user.role]||0)>=(t[e]||0)},get managementArea(){return R.user&&this.isAdmin?this.userRoleDisplayName:""}};O.init();const z=S(e.defineComponent({__name:"index",setup(a){const i=e.ref(!1),l=e.ref(!1),n=e.ref(!1),o=e.reactive({phone:"",password:""}),c=e.reactive({phone:"",password:"",confirmPassword:""}),r=e.reactive({nickname:"",gender:0,phone:""}),s=e.ref(""),d=e.ref(""),m=e.ref(""),u=e.ref(""),v=[{label:"未知",value:0},{label:"男",value:1},{label:"女",value:2}],p=e.computed(()=>""!==o.phone.trim()&&""!==o.password.trim()&&!s.value),g=e.computed(()=>""!==c.phone.trim()&&""!==c.password.trim()&&""!==c.confirmPassword.trim()&&!d.value&&!m.value&&!u.value),h={ancient_books:"古籍管理",paintings:"书画珍品",archives:"档案故事",videos:"影像文献"},y=()=>{var e;const t=null==(e=O.currentUser)?void 0:e.nickname;return t&&t.length>0?t.charAt(0).toUpperCase():"用"},_=()=>{const e=O.currentUser;if(!e||!O.isAdmin)return"";const t=[];return e.province_name&&t.push(e.province_name),e.city_name&&t.push(e.city_name),e.district_name&&t.push(e.district_name),t.length>0?t.join(" - "):"全国"},E=e=>{const t=O.currentUser;if(!t||"GUEST"===t.role)return!1;if("SUPER_ADMIN"===t.role)return!0;const a=t.module_permissions;return!!a&&!0===a[e]},w=()=>{l.value=!0,n.value=!1,o.phone="",o.password="",s.value=""},N=()=>{l.value=!1,n.value=!1,o.phone="",o.password="",c.phone="",c.password="",c.confirmPassword="",s.value="",d.value="",m.value="",u.value=""},f=()=>{n.value=!0,s.value="",o.phone&&(c.phone=o.phone)},k=()=>{n.value=!1,d.value="",m.value="",u.value="",c.phone&&(o.phone=c.phone)},V=()=>o.phone?/^1[3-9]\d{9}$/.test(o.phone)?(s.value="",!0):(s.value="请输入正确的手机号格式",!1):(s.value="请输入手机号",!1),x=()=>c.phone?/^1[3-9]\d{9}$/.test(c.phone)?(d.value="",!0):(d.value="请输入正确的手机号格式",!1):(d.value="请输入手机号",!1),C=()=>c.password?c.password.length<6||c.password.length>20?(m.value="密码长度应为6-20位",!1):(m.value="",!0):(m.value="请输入密码",!1),B=()=>c.confirmPassword?c.confirmPassword!==c.password?(u.value="两次输入的密码不一致",!1):(u.value="",!0):(u.value="请再次输入密码",!1),b=async()=>{try{if(!V())return;if(!o.password.trim())return void uni.showToast({title:"请输入密码",icon:"none"});uni.showLoading({title:"登录中..."}),await O.loginWithPassword(o.phone,o.password),uni.showToast({title:"登录成功",icon:"success"}),N()}catch(e){t("error","at pages/user/index.vue:511","登录失败:",e),uni.showToast({title:"登录失败，请检查账号密码",icon:"none"})}finally{uni.hideLoading()}},I=async()=>{try{if(!x())return;if(!C())return;if(!B())return;uni.showLoading({title:"注册中..."}),await O.register(c.phone,c.password),uni.showToast({title:"注册成功，已自动登录",icon:"success"}),N()}catch(e){t("error","at pages/user/index.vue:542","注册失败:",e),uni.showToast({title:"注册失败，请重试",icon:"none"})}finally{uni.hideLoading()}},D=async()=>{uni.showModal({title:"确认退出",content:"确定要退出登录吗？",success:async e=>{if(e.confirm)try{await O.logout(),uni.showToast({title:"已退出登录",icon:"success"})}catch(a){t("error","at pages/user/index.vue:566","退出登录失败:",a)}}})},T=()=>{var e,t,a;O.isAuthenticated?(r.nickname=(null==(e=O.currentUser)?void 0:e.nickname)||"",r.gender=(null==(t=O.currentUser)?void 0:t.gender)||0,r.phone=(null==(a=O.currentUser)?void 0:a.phone)||"",i.value=!0):uni.showToast({title:"请先登录",icon:"none"})},S=e=>{r.gender=parseInt(e.detail.value)},M=()=>{i.value=!1},A=async()=>{try{if(!r.nickname.trim())return void uni.showToast({title:"请输入昵称",icon:"none"});uni.showLoading({title:"保存中..."});const e={nickname:r.nickname.trim(),gender:r.gender},a=await(async e=>{var a;try{const t=uni.getStorageSync("access_token"),i=await uni.request({url:`${P}/auth/me`,method:"PUT",data:e,header:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}});if(200===i.statusCode)return i.data;throw new Error((null==(a=i.data)?void 0:a.detail)||"更新用户信息失败")}catch(i){throw t("error","at api/auth.ts:221","更新用户信息失败:",i),i}})(e);O.updateUser(a),i.value=!1,uni.showToast({title:"保存成功",icon:"success"})}catch(e){t("error","at pages/user/index.vue:634","保存失败:",e),uni.showToast({title:"保存失败",icon:"none"})}finally{uni.hideLoading()}},F=()=>{uni.navigateTo({url:"/pages/admin/users"})},L=()=>{if(!O.isAuthenticated||!O.isAdmin)return void uni.showToast({title:"权限不足",icon:"none"});const e=O.currentUser;if(!e)return void uni.showToast({title:"用户信息获取失败",icon:"none"});const t=e.module_permissions||{ancient_books:!1,paintings:!1,archives:!1,videos:!1},a={role:e.role,province_name:e.province_name||"",province_id:e.province_id||"",city_name:e.city_name||"",city_id:e.city_id||"",district_name:e.district_name||"",district_id:e.district_id||"",user_id:e.id||"",ancient_books_permission:t.ancient_books?"true":"false",paintings_permission:t.paintings?"true":"false",archives_permission:t.archives?"true":"false",videos_permission:t.videos?"true":"false"},i=Object.keys(a).map(e=>`${e}=${encodeURIComponent(a[e])}`).join("&");uni.navigateTo({url:`/pages/culture/heritage?${i}`})},$=()=>{uni.showModal({title:"关于我们",content:"千载·今知小程序\n版本:1.0.0\n参赛人:钟宇柠（重庆邮电大学）",showCancel:!1,confirmText:"知道了"})};return(t,a)=>{var P,U,R,q;return e.openBlock(),e.createElementBlock("view",{class:"user-container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"user-avatar"},[(null==(P=e.unref(O).currentUser)?void 0:P.avatar_url)?(e.openBlock(),e.createElementBlock("image",{key:0,src:e.unref(O).currentUser.avatar_url,class:"avatar-img"},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"avatar-placeholder"},[e.createElementVNode("text",{class:"avatar-text"},e.toDisplayString(y()),1)]))]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"user-name"},e.toDisplayString((null==(U=e.unref(O).currentUser)?void 0:U.nickname)||"未设置昵称"),1),e.createElementVNode("view",{class:"user-role"},e.toDisplayString(e.unref(O).userRoleDisplayName),1),(null==(R=e.unref(O).currentUser)?void 0:R.phone)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"user-phone"},e.toDisplayString(e.unref(O).currentUser.phone),1)):e.createCommentVNode("",!0),e.unref(O).isAdmin&&e.unref(O).currentUser?(e.openBlock(),e.createElementBlock("view",{key:1,class:"admin-authority"},[e.createElementVNode("view",{class:"authority-section"},[e.createElementVNode("text",{class:"authority-title"},"管辖区域："),e.createElementVNode("text",{class:"authority-value"},e.toDisplayString(_()),1)]),"SUPER_ADMIN"!==e.unref(O).currentUser.role?(e.openBlock(),e.createElementBlock("view",{key:0,class:"authority-section"},[e.createElementVNode("text",{class:"authority-title"},"管理权限："),e.createElementVNode("view",{class:"module-permissions"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(h,(t,a)=>e.createElementVNode("view",{key:a,class:e.normalizeClass(["permission-tag",{active:E(a)}])},e.toDisplayString(t),3)),64))])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"authority-section"},[e.createElementVNode("text",{class:"authority-title"},"管理权限："),e.createElementVNode("view",{class:"super-admin-permissions"},[e.createElementVNode("text",{class:"super-admin-text"},"全部权限")])]))])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"user-actions"},[e.unref(O).isAuthenticated?(e.openBlock(),e.createElementBlock("button",{key:1,onClick:D,class:"logout-btn"}," 退出登录 ")):(e.openBlock(),e.createElementBlock("button",{key:0,onClick:w,class:"login-btn"}," 点击登录 "))])]),e.createElementVNode("view",{class:"menu-section"},[e.createElementVNode("view",{class:"menu-title"},"个人中心"),e.createElementVNode("view",{class:"menu-item",onClick:T},[e.createElementVNode("view",{class:"menu-icon"},"👤"),e.createElementVNode("view",{class:"menu-text"},"编辑资料"),e.createElementVNode("view",{class:"menu-arrow"},">")])]),e.unref(O).isAdmin?(e.openBlock(),e.createElementBlock("view",{key:0,class:"menu-section"},[e.createElementVNode("view",{class:"menu-title"},"管理功能"),e.createElementVNode("view",{class:"menu-item",onClick:F},[e.createElementVNode("view",{class:"menu-icon"},"👥"),e.createElementVNode("view",{class:"menu-text"},"用户管理"),e.createElementVNode("view",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:L},[e.createElementVNode("view",{class:"menu-icon"},"🎨"),e.createElementVNode("view",{class:"menu-text"},"文化管理"),e.createElementVNode("view",{class:"menu-arrow"},">")])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"menu-section"},[e.createElementVNode("view",{class:"menu-title"},"其他"),e.createElementVNode("view",{class:"menu-item",onClick:$},[e.createElementVNode("view",{class:"menu-icon"},"ℹ️"),e.createElementVNode("view",{class:"menu-text"},"关于我们"),e.createElementVNode("view",{class:"menu-arrow"},">")])]),l.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"popup-overlay",onClick:N},[e.createElementVNode("view",{class:"login-popup",onClick:a[5]||(a[5]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-title"},e.toDisplayString(n.value?"用户注册":"用户登录"),1),n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"register-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"手机号"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[2]||(a[2]=e=>c.phone=e),class:"form-input",placeholder:"请输入手机号",maxlength:"11",type:"number","confirm-type":"next",onBlur:x},null,544),[[e.vModelText,c.phone]]),d.value?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(d.value),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>c.password=e),class:"form-input",placeholder:"请输入密码（6-20位）",type:"password","confirm-type":"next",onBlur:C},null,544),[[e.vModelText,c.password]]),m.value?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(m.value),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"确认密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[4]||(a[4]=e=>c.confirmPassword=e),class:"form-input",placeholder:"请再次输入密码",type:"password","confirm-type":"done",onBlur:B,onConfirm:I},null,544),[[e.vModelText,c.confirmPassword]]),u.value?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(u.value),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"popup-actions"},[e.createElementVNode("button",{onClick:N,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:I,class:"confirm-btn",disabled:!g.value},"注册",8,["disabled"])]),e.createElementVNode("view",{class:"switch-mode"},[e.createElementVNode("text",{class:"switch-text"},"已有账号？"),e.createElementVNode("text",{class:"switch-link",onClick:k},"立即登录")])])):(e.openBlock(),e.createElementBlock("view",{key:0,class:"login-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"手机号"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>o.phone=e),class:"form-input",placeholder:"请输入手机号",maxlength:"11",type:"number","confirm-type":"next",onBlur:V},null,544),[[e.vModelText,o.phone]]),s.value?(e.openBlock(),e.createElementBlock("text",{key:0,class:"error-text"},e.toDisplayString(s.value),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"密码"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>o.password=e),class:"form-input",placeholder:"请输入密码",type:"password","confirm-type":"done",onConfirm:b},null,544),[[e.vModelText,o.password]])]),e.createElementVNode("view",{class:"popup-actions"},[e.createElementVNode("button",{onClick:N,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:b,class:"confirm-btn",disabled:!p.value},"登录",8,["disabled"])]),e.createElementVNode("view",{class:"switch-mode"},[e.createElementVNode("text",{class:"switch-text"},"还没有账号？"),e.createElementVNode("text",{class:"switch-link",onClick:f},"立即注册")])]))])])):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"popup-overlay",onClick:M},[e.createElementVNode("view",{class:"profile-edit-popup",onClick:a[7]||(a[7]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-title"},"编辑资料"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"昵称"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>r.nickname=e),class:"form-input",placeholder:"请输入昵称",maxlength:"20",type:"text","confirm-type":"done"},null,512),[[e.vModelText,r.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"性别"),e.createElementVNode("picker",{value:r.gender,range:v,"range-key":"label",onChange:S},[e.createElementVNode("view",{class:"form-input picker-input"},e.toDisplayString((null==(q=v[r.gender])?void 0:q.label)||"请选择"),1)],40,["value"])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"手机号"),e.createElementVNode("view",{class:"form-input phone-readonly"},e.toDisplayString(r.phone||"未设置"),1),e.createElementVNode("text",{class:"phone-tip"},"手机号为登录账号，不可修改")]),e.createElementVNode("view",{class:"popup-actions"},[e.createElementVNode("button",{onClick:M,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:A,class:"confirm-btn"},"保存")])])])):e.createCommentVNode("",!0)])}}}),[["__scopeId","data-v-c39a9b20"]]),G=S(e.defineComponent({__name:"select",setup(a){const i=e.ref(!1),l=e.ref(""),n=e.ref(null),o=e.ref(null),c=e.ref(null),r=e.ref([]),s=e.ref([]),d=e.ref([]);e.onMounted(async()=>{await m(),await h()});const m=async()=>{try{i.value=!0,r.value=await E()}catch(e){uni.showToast({title:"加载省份失败",icon:"none"})}finally{i.value=!1}},u=async e=>{n.value=e,o.value=null,c.value=null,s.value=[],d.value=[];try{i.value=!0,s.value=await w(e.province_id)}catch(t){uni.showToast({title:"加载城市失败",icon:"none"})}finally{i.value=!1}},v=async e=>{o.value=e,c.value=null,d.value=[];try{i.value=!0,d.value=await N(n.value.province_id,e.city_id)}catch(t){uni.showToast({title:"加载区县失败",icon:"none"})}finally{i.value=!1}},g=e=>{c.value=e},h=async()=>{try{i.value=!0;const a=await p.getCurrentLocation();l.value=a.fullAddress;try{const e=await T.matchRegionIdsByLocation({province:a.province,city:a.city,district:a.district});if(e){if(t("log","at pages/location/select.vue:215","选择页面: 定位后自动匹配区域ID成功，开始回显"),e.provinceId>0){const a=r.value.find(t=>t.province_id===e.provinceId);if(a){if(t("log","at pages/location/select.vue:223","回显省份:",a.name),await u(a),e.cityId>0){const a=s.value.find(t=>t.city_id===e.cityId);if(a){if(t("log","at pages/location/select.vue:232","回显城市:",a.name),await v(a),e.districtId>0){const a=d.value.find(t=>t.district_id===e.districtId);a&&(t("log","at pages/location/select.vue:241","回显区县:",a.name),g(a))}}else t("warn","at pages/location/select.vue:246","未找到匹配的城市, cityId:",e.cityId)}}else t("warn","at pages/location/select.vue:253","未找到匹配的省份, provinceId:",e.provinceId)}t("log","at pages/location/select.vue:259","选择页面: 自动回显完成")}}catch(e){t("warn","at pages/location/select.vue:262","选择页面: 定位后自动匹配区域ID失败:",e)}}catch(a){l.value="定位失败，请手动选择"}finally{i.value=!1}},y=()=>{var e,t,a;o.value?(n.value,o.value,c.value,_(),p.setLocation({province:(null==(e=n.value)?void 0:e.name)||"",city:(null==(t=o.value)?void 0:t.name)||"",district:(null==(a=c.value)?void 0:a.name)||"",fullAddress:_(),street:"",longitude:0,latitude:0}),T.setSelectedRegionIds(n.value,o.value,c.value),uni.showToast({title:"已选择"+_(),icon:"success"}),setTimeout(()=>{uni.navigateBack()},500)):uni.showToast({title:"请选择城市",icon:"none"})},_=()=>{var e;let t=(null==(e=n.value)?void 0:e.name)||"";return o.value&&(t+=o.value.name),c.value&&(t+=c.value.name),t},f=()=>{uni.navigateBack()};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"location-select-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-left",onClick:f},[e.createElementVNode("view",{class:"back-btn"},[e.createElementVNode("text",{class:"back-icon"},"←")])]),e.createElementVNode("text",{class:"header-title"},"选择城市"),e.createElementVNode("view",{class:"header-right"})]),e.createElementVNode("view",{class:"content-area"},[e.createElementVNode("view",{class:"current-location",onClick:h},[e.createElementVNode("view",{class:"location-info"},[e.createElementVNode("text",{class:"location-icon"},"📍"),e.createElementVNode("text",{class:"location-text"},"当前位置")]),e.createElementVNode("view",{class:"location-detail"},[l.value?(e.openBlock(),e.createElementBlock("text",{key:0,class:"location-name"},e.toDisplayString(l.value),1)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"location-loading"},"正在定位..."))]),e.createElementVNode("text",{class:"location-arrow"},">")]),e.createElementVNode("view",{class:"selector-container"},[e.createElementVNode("view",{class:"selector-section"},[e.createElementVNode("text",{class:"selector-title"},"省份"),e.createElementVNode("scroll-view",{class:"selector-list","scroll-y":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(r.value,t=>{var a,i;return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["selector-item",{active:(null==(a=n.value)?void 0:a.province_id)===t.province_id}]),key:t.province_id,onClick:e=>u(t)},[e.createElementVNode("text",{class:"item-text"},e.toDisplayString(t.name),1),(null==(i=n.value)?void 0:i.province_id)===t.province_id?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-check"},"✓")):e.createCommentVNode("",!0)],10,["onClick"])}),128))])]),n.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"selector-section"},[e.createElementVNode("text",{class:"selector-title"},"城市"),e.createElementVNode("scroll-view",{class:"selector-list","scroll-y":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,t=>{var a,i;return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["selector-item",{active:(null==(a=o.value)?void 0:a.city_id)===t.city_id}]),key:t.city_id,onClick:e=>v(t)},[e.createElementVNode("text",{class:"item-text"},e.toDisplayString(t.name),1),(null==(i=o.value)?void 0:i.city_id)===t.city_id?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-check"},"✓")):e.createCommentVNode("",!0)],10,["onClick"])}),128))])])):e.createCommentVNode("",!0),o.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"selector-section"},[e.createElementVNode("text",{class:"selector-title"},"区县"),e.createElementVNode("scroll-view",{class:"selector-list","scroll-y":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,t=>{var a,i;return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["selector-item",{active:(null==(a=c.value)?void 0:a.district_id)===t.district_id}]),key:t.district_id,onClick:e=>g(t)},[e.createElementVNode("text",{class:"item-text"},e.toDisplayString(t.name),1),(null==(i=c.value)?void 0:i.district_id)===t.district_id?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-check"},"✓")):e.createCommentVNode("",!0)],10,["onClick"])}),128))])])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"confirm-container"},[e.createElementVNode("button",{class:"confirm-btn",disabled:!o.value,onClick:y}," 确认选择 ",8,["disabled"])]),i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-mask"},[e.createElementVNode("view",{class:"loading-content"},[e.createElementVNode("text",{class:"loading-text"},"加载中...")])])):e.createCommentVNode("",!0)]))}}),[["__scopeId","data-v-a466e873"]]);const H=S({data:()=>({url:"",statusBarHeight:20,isChongqingPage:!1,navTitle:"云游文化",originalUrl:"",checkTimer:null}),watch:{isChongqingPage(e,a){t("log","at pages/webview/index.vue:31","🔄 isChongqingPage 发生变化:",a,"->",e),t("log","at pages/webview/index.vue:32","当前时间:",(new Date).toLocaleTimeString()),t("log","at pages/webview/index.vue:33","调用栈:",(new Error).stack)}},onLoad(e){if(this.getStatusBarHeight(),e&&e.url)try{this.url=decodeURIComponent(e.url),this.originalUrl=this.url,t("log","at pages/webview/index.vue:44","加载外部页面:",this.url),this.setNavBarStyle(this.url),this.startStyleCheck()}catch(a){t("error","at pages/webview/index.vue:53","URL解码失败:",a),this.url=e.url,this.originalUrl=this.url,t("log","at pages/webview/index.vue:56","使用原始URL:",this.url),this.setNavBarStyle(this.url),this.startStyleCheck()}else t("error","at pages/webview/index.vue:63","未提供URL参数"),uni.showToast({title:"链接无效",icon:"none"}),setTimeout(()=>{uni.navigateBack()},1500)},methods:{setNavBarStyle(e){t("log","at pages/webview/index.vue:77","=== 导航栏样式设置 ==="),t("log","at pages/webview/index.vue:78","传入的URL:",e),t("log","at pages/webview/index.vue:79","URL类型:",typeof e);const a=["chongqing","重庆","地铁","轨道"].filter(t=>e&&e.includes(t));t("log","at pages/webview/index.vue:85","匹配的关键词:",a),e&&a.length>0?(this.isChongqingPage=!0,this.navTitle="乘着轨道游重庆",t("log","at pages/webview/index.vue:90","✅ 检测到重庆页面，设置绿色导航栏"),t("log","at pages/webview/index.vue:91","isChongqingPage:",this.isChongqingPage),this.setNativeNavBar("#00a651","乘着轨道游重庆")):(this.isChongqingPage=!1,this.navTitle="云游文化",t("log","at pages/webview/index.vue:99","❌ 普通页面，使用默认红色导航栏"),t("log","at pages/webview/index.vue:100","isChongqingPage:",this.isChongqingPage),this.setNativeNavBar("#ba0001","云游文化")),t("log","at pages/webview/index.vue:105","最终标题:",this.navTitle),t("log","at pages/webview/index.vue:106","=== 设置完成 ===")},handleMessage(e){t("log","at pages/webview/index.vue:110","接收到web-view消息:",e),t("log","at pages/webview/index.vue:111","webview消息时的isChongqingPage:",this.isChongqingPage)},setNativeNavBar(e,a){t("log","at pages/webview/index.vue:116","🎨 设置原生导航栏:",{backgroundColor:e,title:a}),uni.setNavigationBarTitle({title:a,success:()=>{t("log","at pages/webview/index.vue:122","✅ 导航栏标题设置成功:",a)},fail:e=>{t("error","at pages/webview/index.vue:125","❌ 导航栏标题设置失败:",e)}}),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:e,animation:{duration:300,timingFunc:"easeIn"},success:()=>{t("log","at pages/webview/index.vue:138","✅ 导航栏颜色设置成功:",e)},fail:e=>{t("error","at pages/webview/index.vue:141","❌ 导航栏颜色设置失败:",e)}})},goBack(){uni.navigateBack()},getStatusBarHeight(){const e=uni.getSystemInfoSync();this.statusBarHeight=e.statusBarHeight||20},startStyleCheck(){this.checkTimer&&clearInterval(this.checkTimer),this.checkTimer=setInterval(()=>{this.originalUrl&&(this.originalUrl.includes("chongqing")||this.originalUrl.includes("重庆")||this.originalUrl.includes("地铁")||this.originalUrl.includes("轨道"))&&!this.isChongqingPage&&(t("log","at pages/webview/index.vue:175","🔧 检测到样式被重置，重新设置为重庆样式"),this.isChongqingPage=!0,this.navTitle="乘着轨道游重庆",this.setNativeNavBar("#00a651","乘着轨道游重庆"))},2e3),t("log","at pages/webview/index.vue:182","✅ 样式检查定时器已启动")},stopStyleCheck(){this.checkTimer&&(clearInterval(this.checkTimer),this.checkTimer=null,t("log","at pages/webview/index.vue:190","🛑 样式检查定时器已停止"))}},onUnload(){this.stopStyleCheck()}},[["render",function(t,a,i,l,n,o){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("web-view",{src:n.url,onMessage:a[0]||(a[0]=(...e)=>o.handleMessage&&o.handleMessage(...e))},null,40,["src"]),(e.openBlock(),e.createElementBlock("view",{key:0,class:"debug-info",style:{position:"fixed",top:"100px",right:"10px","font-size":"12px",color:"white",background:"rgba(0,0,0,0.7)",padding:"5px","border-radius":"3px","z-index":"9999"}},[e.createTextVNode(e.toDisplayString(n.isChongqingPage?"绿色模式":"红色模式")+" ",1),e.createElementVNode("br"),e.createTextVNode(e.toDisplayString(n.navTitle),1)]))])}],["__scopeId","data-v-7f77755d"]]);function Y(e){return e?W(e):""}function W(e){return e?(e.startsWith("data:image/")||e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/static/"),e):""}function X(e){return e?W(e):"images/no-image.svg"}const J=e.defineComponent({__name:"history",setup(a){const i=e.ref(null),l=e.ref(!0),n=e.ref(!1),o=e.ref([]),c=(e,a)=>{if(!e||0===e.length)return;((e,a)=>{if(!e||0===e.length)return void t("error","at pages/culture/history.vue:226","预览图片列表为空");const i=e.filter(e=>!!e);0!==i.length?uni.previewImage({urls:i,current:i[a]||i[0],success:()=>{t("log","at pages/culture/history.vue:246","图片预览成功")},fail:e=>{t("error","at pages/culture/history.vue:249","图片预览失败:",e),uni.showToast({title:"图片预览失败",icon:"none"})}}):uni.showToast({title:"没有可预览的图片",icon:"none"})})(e.map(e=>Y(e)),a)},r=e=>e?Y(e):"/static/images/no-image.svg",s=(e,a)=>{t("log","at pages/culture/history.vue:278","图片加载失败:",e);const i=e.target||e.currentTarget;i&&(i.src="/static/images/no-image.svg")},d=async()=>{var e;l.value=!0;try{let a=null;const c=getCurrentPages(),r=c[c.length-1],s=null==(e=null==r?void 0:r.options)?void 0:e.placeId;if(s)t("log","at pages/culture/history.vue:299","使用URL中的placeId获取数据:",s),a=await x(s);else{const e=T.currentRegionIds;if(e.provinceId)t("log","at pages/culture/history.vue:306","根据当前选择的区域ID获取历史文脉数据:",e),a=await V({province_id:e.provinceId||void 0,city_id:e.cityId||void 0,district_id:e.districtId||void 0});else{const e=T.currentHeritagePlace;e&&(t("log","at pages/culture/history.vue:317","备用方案：使用缓存的heritage place获取数据:",e.id),a=await x(e.id))}}if(!a)return uni.showToast({title:"当前地区未构建该功能",icon:"none",duration:2e3}),void setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},2e3);i.value={placeId:a.place_info.id,placeName:a.place_info.place_name,placeDesc:a.place_info.place_desc||"",headerBgImage:a.place_info.header_bg_image||"/static/images/no-image.svg",introduction:a.place_info.introduction||"",footerText:a.place_info.footer_text||"",timelineData:(a.timeline_data||[]).map(e=>({id:e.id,period:e.period,year:e.year,title:e.title,description:e.description,image:e.image||"/static/images/no-image.svg",heritages:e.heritage_tags||[],hasDetail:e.has_detail,isExpanded:!1,detail:e.detail,detailImages:e.detail_images||[],sortOrder:e.sort_order||0})).sort((e,t)=>e.sortOrder-t.sortOrder)},t("log","at pages/culture/history.vue:369","历史文脉页面数据加载成功:",i.value),l.value=!1,n.value=!1,setTimeout(()=>{(()=>{var e,a;!n.value&&(null==(a=null==(e=i.value)?void 0:e.timelineData)?void 0:a.length)&&(n.value=!0,o.value=[],i.value.timelineData.forEach((e,a)=>{setTimeout(()=>{o.value.push(a),a===i.value.timelineData.length-1&&t("log","at pages/culture/history.vue:203","时间轴动画完成，所有项目可见")},800*(a+1))}),setTimeout(()=>{i.value&&i.value.timelineData&&(i.value.timelineData.map((e,t)=>t).forEach(e=>{o.value.includes(e)||o.value.push(e)}),t("log","at pages/culture/history.vue:218","安全检查：确保所有时间轴项目可见"))},8e3))})()},500)}catch(a){t("error","at pages/culture/history.vue:379","数据加载失败:",a),uni.showToast({title:"当前地区未构建该功能",icon:"none",duration:2e3}),setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},2e3),l.value=!1}};return e.onMounted(()=>{d()}),(a,n)=>{var d,m,u;return i.value||l.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"container"},[i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"header-section"},[e.createElementVNode("image",{class:"header-bg",src:i.value.headerBgImage,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"place-name"},e.toDisplayString(i.value.placeName),1),e.createElementVNode("text",{class:"place-desc"},e.toDisplayString(i.value.placeDesc),1)])])):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"intro-section"},[e.createElementVNode("text",{class:"intro-text"},e.toDisplayString((null==(d=i.value)?void 0:d.introduction)||""),1)])):e.createCommentVNode("",!0),i.value||l.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"timeline-section"},[e.createElementVNode("view",{class:"timeline-title"},[e.createElementVNode("text",null,"历史文脉溯源")]),e.createElementVNode("view",{class:"timeline"},[i.value&&i.value.timelineData&&0!==i.value.timelineData.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},"暂无历史文脉数据")])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((null==(m=i.value)?void 0:m.timelineData)||[],(a,l)=>{var n,d;return e.withDirectives((e.openBlock(),e.createElementBlock("view",{class:"timeline-item",key:l},[e.createElementVNode("view",{class:"time-marker"},[e.createElementVNode("view",{class:"time-dot"}),l!==((null==(d=null==(n=i.value)?void 0:n.timelineData)?void 0:d.length)||0)-1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"time-line"})):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:e.normalizeClass(["time-card",{clickable:a.hasDetail}]),onClick:n=>a.hasDetail?(a=>{i.value&&i.value.timelineData&&(o.value.includes(a)||o.value.push(a),i.value.timelineData[a].isExpanded=!i.value.timelineData[a].isExpanded,e.nextTick(()=>{var e;t("log","at pages/culture/history.vue:179","展开状态已更新:",a,null==(e=i.value)?void 0:e.timelineData[a].isExpanded)}))})(l):null},[e.createElementVNode("view",{class:"time-period"},[e.createElementVNode("text",{class:"period-name"},e.toDisplayString(a.period),1),e.createElementVNode("text",{class:"period-year"},e.toDisplayString(a.year),1)]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("image",{src:r(a.image),mode:"aspectFill",class:"card-image",onError:e=>s(e)},null,40,["src","onError"]),e.createElementVNode("view",{class:"card-text"},[e.createElementVNode("text",{class:"card-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"card-desc"},e.toDisplayString(a.description),1)])]),a.heritages&&a.heritages.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"heritage-tags"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.heritages,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"tag",key:a},e.toDisplayString(t),1))),128))])):e.createCommentVNode("",!0),a.hasDetail?(e.openBlock(),e.createElementBlock("view",{key:1,class:"expand-section"},[e.createElementVNode("text",{class:"expand-text"},e.toDisplayString(a.isExpanded?"收起":"展开更多"),1),e.createElementVNode("text",{class:e.normalizeClass(["expand-icon",{expanded:a.isExpanded}])},"▼",2)])):e.createCommentVNode("",!0),a.isExpanded&&a.detail?(e.openBlock(),e.createElementBlock("view",{key:2,class:"detail-content"},[e.createElementVNode("rich-text",{nodes:a.detail},null,8,["nodes"]),a.detailImages&&a.detailImages.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.detailImages,(t,i)=>(e.openBlock(),e.createElementBlock("image",{key:i,src:r(t),mode:"aspectFill",class:"detail-image",onError:e=>s(e),onClick:e=>c(a.detailImages,i)},null,40,["src","onError","onClick"]))),128))])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)],10,["onClick"])])),[[e.vShow,o.value.includes(l)]])}),128))])])):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createElementBlock("view",{key:3,class:"footer"},[e.createElementVNode("text",{class:"footer-text"},e.toDisplayString((null==(u=i.value)?void 0:u.footerText)||""),1)])):e.createCommentVNode("",!0),l.value?(e.openBlock(),e.createElementBlock("view",{key:4,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)}}}),K=S(e.defineComponent({__name:"chongqing",setup(a){const i=e.ref(null),l=e.ref([{id:"2-01",name:"较场口",x:200,y:80,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:8,attractions:["较场口夜市","湖广会馆","东水门大桥","洪崖洞"]},{id:"2-02",name:"临江门",x:200,y:160,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:4,attractions:["解放碑","重庆大剧院","朝天门","来福士广场"]},{id:"2-03",name:"黄花园",x:200,y:240,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:4,attractions:["黄花园大桥","嘉陵江滨江路","重庆科技馆"]},{id:"2-04",name:"大溪沟",x:200,y:320,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:3,attractions:["重庆人民大礼堂","三峡博物馆","人民广场"]},{id:"2-05",name:"曾家岩",x:200,y:400,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:4,attractions:["曾家岩书院","嘉陵江索道","红岩村"]},{id:"2-06",name:"牛角沱",x:200,y:480,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"地下岛式站台",exits:6,attractions:["华新街","观音桥商圈","北滨路"]},{id:"2-07",name:"李子坝",x:200,y:560,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"高架侧式站台",exits:2,attractions:["李子坝轻轨穿楼","嘉陵江大桥","李子坝抗战遗址"]},{id:"2-08",name:"佛图关",x:200,y:640,labelOffset:{x:50,y:0},openDate:"2005年6月18日",platformType:"高架岛式站台",exits:2,attractions:["佛图关公园","嘉陵江观景台","鹅岭公园"]}]),n=()=>{i.value=null},o=e=>{const t=(e.match(/[\u4e00-\u9fa5]/g)||[]).length;return 14*t+8*(e.length-t)+16},c=e=>{if("touchend"===e.type){const a=e.changedTouches[0];uni.createSelectorQuery().select(".metro-canvas").boundingClientRect(e=>{if(e&&!Array.isArray(e)&&e.width&&e.height&&void 0!==e.left&&void 0!==e.top){const n=(a.clientX-e.left)/e.width*400,c=(a.clientY-e.top)/e.height*800;t("log","at pages/culture/chongqing.vue:337",`触摸点: DOM(${a.clientX-e.left}, ${a.clientY-e.top}) → Canvas(${n}, ${c})`),t("log","at pages/culture/chongqing.vue:342",`Canvas容器尺寸: ${e.width} × ${e.height}`),t("log","at pages/culture/chongqing.vue:343",`Canvas位置: left=${e.left}, top=${e.top}`),t("log","at pages/culture/chongqing.vue:344",`原始触摸坐标: clientX=${a.clientX}, clientY=${a.clientY}`),l.value.forEach(e=>{const a=Math.sqrt(Math.pow(n-e.x,2)+Math.pow(c-e.y,2)),l=o(e.name),r=e.x+24,s=e.y-12,d=n>=r&&n<=r+l&&c>=s&&c<=s+24;let m=!1;if(e.attractions&&e.attractions.length>0){const t=e.x+12,a=e.y-18;m=Math.sqrt(Math.pow(n-t,2)+Math.pow(c-a,2))<=18}(a<=20||d||m)&&(t("log","at pages/culture/chongqing.vue:379",`选中站点: ${e.name}, 距离: ${a}`),(e=>{i.value=e})(e))})}}).exec()}};e.onMounted(()=>{setTimeout(()=>{(()=>{const e=uni.createCanvasContext("metroCanvas");t("log","at pages/culture/chongqing.vue:218","开始绘制Canvas，内部坐标系: 400×800"),e.clearRect(0,0,400,800),e.beginPath(),e.setStrokeStyle("rgba(255, 107, 53, 0.2)"),e.setLineWidth(12),e.setLineCap("round"),e.moveTo(200,60),e.lineTo(200,660),e.stroke(),e.beginPath(),e.setStrokeStyle("#FF6B35"),e.setLineWidth(6),e.setLineCap("round"),e.moveTo(200,60),e.lineTo(200,660),e.stroke(),l.value.forEach(a=>{t("log","at pages/culture/chongqing.vue:243",`绘制站点: ${a.name} at (${a.x}, ${a.y})`),e.beginPath(),e.arc(a.x+2,a.y+2,14,0,2*Math.PI),e.setFillStyle("rgba(0, 0, 0, 0.1)"),e.fill(),e.beginPath(),e.arc(a.x,a.y,12,0,2*Math.PI),e.setFillStyle("white"),e.fill(),e.setStrokeStyle("#FF6B35"),e.setLineWidth(3),e.stroke();const i=o(a.name),l=a.x+24,n=a.y-12;if(e.beginPath(),e.setFillStyle("rgba(255, 255, 255, 0.95)"),e.fillRect(l,n,i,24),e.setStrokeStyle("rgba(255, 107, 53, 0.2)"),e.setLineWidth(1),e.strokeRect(l,n,i,24),e.setFillStyle("#333"),e.setFontSize(14),e.setTextAlign("left"),e.setTextBaseline("middle"),e.fillText(a.name,l+8,a.y),a.attractions&&a.attractions.length>0){const t=a.x+12,i=a.y-18;e.beginPath(),e.arc(t,i,18,0,2*Math.PI),e.setFillStyle("#FFD700"),e.fill(),e.setStrokeStyle("#FF6B35"),e.setLineWidth(2),e.stroke(),e.setFillStyle("#FF6B35"),e.setFontSize(10),e.setTextAlign("center"),e.setTextBaseline("middle"),e.fillText("景",t,i)}}),e.draw()})()},100)});const r=()=>{uni.navigateBack()};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"chongqing-metro-page"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"重庆地铁二号线旅游图"),e.createElementVNode("view",{class:"back-button",onClick:r},[e.createElementVNode("text",{class:"back-icon"},"←")])]),e.createElementVNode("view",{class:"map-container"},[e.createElementVNode("view",{class:"metro-wrapper"},[e.createElementVNode("canvas",{"canvas-id":"metroCanvas",class:"metro-canvas",onTouchend:c},null,32)]),i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-info-panel"},[e.createElementVNode("view",{class:"station-header"},[e.createElementVNode("text",{class:"station-name"},e.toDisplayString(i.value.name),1),e.createElementVNode("view",{class:"close-btn",onClick:n},[e.createElementVNode("text",null,"×")])]),e.createElementVNode("view",{class:"station-details"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"label"},"开通时间："),e.createElementVNode("text",{class:"value"},e.toDisplayString(i.value.openDate),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"label"},"站台类型："),e.createElementVNode("text",{class:"value"},e.toDisplayString(i.value.platformType),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"label"},"出入口："),e.createElementVNode("text",{class:"value"},e.toDisplayString(i.value.exits)+"个",1)])]),i.value.attractions&&i.value.attractions.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"attractions-section"},[e.createElementVNode("text",{class:"section-title"},"周边景点"),e.createElementVNode("view",{class:"attractions-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value.attractions,t=>(e.openBlock(),e.createElementBlock("view",{key:t,class:"attraction-tag"},[e.createElementVNode("text",null,e.toDisplayString(t),1)]))),128))])])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),i.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"line-info"},[e.createElementVNode("view",{class:"line-header"},[e.createElementVNode("view",{class:"line-indicator"}),e.createElementVNode("text",{class:"line-name"},"重庆轨道交通2号线")]),e.createElementVNode("text",{class:"line-description"},"连接重庆主城核心区域，途经8个重要旅游站点"),e.createElementVNode("view",{class:"line-stats"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"总长度"),e.createElementVNode("text",{class:"stat-value"},"约18.5公里")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"运营时间"),e.createElementVNode("text",{class:"stat-value"},"06:30-22:30")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"站点数"),e.createElementVNode("text",{class:"stat-value"},"8站")])])]))])]))}}),[["__scopeId","data-v-180b4ea4"]]),Q="/static/icons/add.svg",Z="/static/icons/edit.svg",ee="/static/icons/delete.svg",te=e.defineComponent({__name:"heritage",setup(a){var i,l,n;const o=e.ref(null),c=e.ref(!0),r=e.ref(!1),s=e.ref([]),d=e.ref(!1),m=e.ref({role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:"",user_id:""}),u=e.ref({province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),v=e.ref(!1),p=e.ref({provinceIndex:-1,cityIndex:-1,districtIndex:-1}),g=e.ref([]),h=e.ref([]),y=e.ref([]),_=e.computed(()=>"SUPER_ADMIN"===m.value.role),f=e.computed(()=>{const e=m.value;return["SUPER_ADMIN","PROVINCE_ADMIN"].includes(e.role)&&h.value.length>0}),C=e.computed(()=>{const e=m.value;return["SUPER_ADMIN","PROVINCE_ADMIN","CITY_ADMIN"].includes(e.role)&&y.value.length>0}),B=e.computed(()=>{const e=u.value,t=m.value;return!!e.district_id||(!(!e.city_id||e.district_id||!["SUPER_ADMIN","PROVINCE_ADMIN"].includes(t.role))||!(!e.province_id||e.city_id||"SUPER_ADMIN"!==t.role))}),b=e=>X(e),I=(e,t)=>{const a=e.target||e.currentTarget;a&&(a.src="/static/images/no-image.svg")},D=async()=>{var e,t;c.value=!0;try{let i=null;const l=(null==(e=S.value)?void 0:e.placeId)||(null==(t=S.value)?void 0:t.place_id);if(l)i=await x(l);else if(d.value){const e=u.value;if(e.province_id)try{const t=parseInt(e.province_id)||void 0,a=e.city_id&&parseInt(e.city_id)||void 0,l=e.district_id&&parseInt(e.district_id)||void 0;i=await V({province_id:t,city_id:a,district_id:l})}catch(a){}if(!i&&d.value)return o.value=null,void(c.value=!1)}else{const e=T.currentRegionIds;if(e.provinceId)i=await V({province_id:e.provinceId||void 0,city_id:e.cityId||void 0,district_id:e.districtId||void 0});else{const e=T.currentHeritagePlace;e&&(i=await x(e.id))}}if(!i)return d.value?(o.value=null,void(c.value=!1)):(uni.showToast({title:"当前地区未构建该功能",icon:"none",duration:2e3}),void setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},2e3));o.value={placeId:i.place_info.id,placeName:i.place_info.place_name,placeDesc:i.place_info.place_desc||"",headerBgImage:i.place_info.header_bg_image||"/static/images/no-image.svg",introduction:i.place_info.introduction||"",footerText:i.place_info.footer_text||"",timelineData:(i.timeline_data||[]).map(e=>({id:e.id,period:e.period,year:e.year,title:e.title,description:e.description,image:e.image||"/static/images/no-image.svg",heritages:e.heritage_tags||[],hasDetail:Boolean(e.has_detail&&(e.detail||e.detail_images&&e.detail_images.length>0)),isExpanded:!1,detail:e.detail,detailImages:e.detail_images||[],sortOrder:e.sort_order||0})).sort((e,t)=>e.sortOrder-t.sortOrder),heritageData:(i.heritage_data||[]).map(e=>({id:e.id,title:e.title,type:e.type,brief:e.brief,image:e.image||"/static/images/no-image.svg",sortOrder:e.sort_order||0})).sort((e,t)=>e.sortOrder-t.sortOrder),memoryData:(i.memory_data||[]).map(e=>({id:e.id,title:e.title,year:e.year,image:e.image||"/static/images/no-image.svg",sortOrder:e.sort_order||0})).sort((e,t)=>e.sortOrder-t.sortOrder)},c.value=!1,r.value=!1,setTimeout(()=>{(()=>{var e,t;!r.value&&(null==(t=null==(e=o.value)?void 0:e.timelineData)?void 0:t.length)&&(r.value=!0,s.value=[],d.value?o.value.timelineData.forEach((e,t)=>{s.value.push(t)}):o.value.timelineData.forEach((e,t)=>{setTimeout(()=>{s.value.push(t)},800*(t+1))}))})()},500)}catch(i){if(d.value)return o.value=null,void(c.value=!1);uni.showToast({title:"当前地区未构建该功能",icon:"none",duration:2e3}),setTimeout(()=>{uni.switchTab({url:"/pages/index/index"})},2e3),c.value=!1}},S=e.ref({}),M=()=>{const e=S.value||{};if("true"===e.manage_mode||!0===e.manage_mode||"1"===e.manage_mode||"true"===e.manage||!0===e.manage)d.value=!0,m.value={role:e.role||"",province_name:decodeURIComponent(e.province_name||""),province_id:e.province_id||"",city_name:decodeURIComponent(e.city_name||""),city_id:e.city_id||"",district_name:decodeURIComponent(e.district_name||""),district_id:e.district_id||"",user_id:e.user_id||""},e.province_id||e.province_name?u.value={province_name:m.value.province_name,province_id:m.value.province_id,city_name:m.value.city_name,city_id:m.value.city_id,district_name:m.value.district_name,district_id:m.value.district_id}:"SUPER_ADMIN"===m.value.role?(u.value={province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""},setTimeout(()=>{uni.showToast({title:"请选择要管理的区域",icon:"none",duration:2e3})},1e3)):"PROVINCE_ADMIN"===m.value.role?u.value={province_name:m.value.province_name,province_id:m.value.province_id,city_name:"",city_id:"",district_name:"",district_id:""}:"CITY_ADMIN"===m.value.role?u.value={province_name:m.value.province_name,province_id:m.value.province_id,city_name:m.value.city_name,city_id:m.value.city_id,district_name:"",district_id:""}:"DISTRICT_ADMIN"===m.value.role&&(u.value={province_name:m.value.province_name,province_id:m.value.province_id,city_name:m.value.city_name,city_id:m.value.city_id,district_name:m.value.district_name,district_id:m.value.district_id}),(e.manage_province_id||e.province_id)&&setTimeout(()=>{uni.showToast({title:`当前管理区域: ${K()}`,icon:"none",duration:2e3})},500);else if(e.role&&"guest"!==e.role){d.value=!0,m.value={role:e.role||"",province_name:decodeURIComponent(e.province_name||""),province_id:e.province_id||"",city_name:decodeURIComponent(e.city_name||""),city_id:e.city_id||"",district_name:decodeURIComponent(e.district_name||""),district_id:e.district_id||"",user_id:e.user_id||""};const t=m.value;"SUPER_ADMIN"===t.role?u.value={province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}:"PROVINCE_ADMIN"===t.role?u.value={province_name:t.province_name,province_id:t.province_id,city_name:"",city_id:"",district_name:"",district_id:""}:"CITY_ADMIN"===t.role?u.value={province_name:t.province_name,province_id:t.province_id,city_name:t.city_name,city_id:t.city_id,district_name:"",district_id:""}:"DISTRICT_ADMIN"===t.role&&(u.value={province_name:t.province_name,province_id:t.province_id,city_name:t.city_name,city_id:t.city_id,district_name:t.district_name,district_id:t.district_id})}if(!d.value){const e=uni.getStorageSync("__current_path__")||"";e&&e.includes("role=")&&(e.includes("PROVINCE_ADMIN")||e.includes("CITY_ADMIN")||e.includes("DISTRICT_ADMIN")||e.includes("SUPER_ADMIN"))&&(d.value=!0,m.value={role:"admin",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:"",user_id:""},e.includes("SUPER_ADMIN")?m.value.role="SUPER_ADMIN":e.includes("PROVINCE_ADMIN")?m.value.role="PROVINCE_ADMIN":e.includes("CITY_ADMIN")?m.value.role="CITY_ADMIN":e.includes("DISTRICT_ADMIN")&&(m.value.role="DISTRICT_ADMIN"))}},A=()=>{if(t("log","at pages/culture/heritage.vue:1015","🔍 editPlace 被调用"),t("log","at pages/culture/heritage.vue:1016","🔍 isManageMode:",d.value),t("log","at pages/culture/heritage.vue:1017","🔍 userInfo:",m.value),t("log","at pages/culture/heritage.vue:1018","🔍 pageData:",o.value),o.value)try{const e=m.value,a=u.value,i=o.value,l={role:e.role||"",province_name:e.province_name||"",province_id:e.province_id||"",city_name:e.city_name||"",city_id:e.city_id||"",district_name:e.district_name||"",district_id:e.district_id||"",manage_province_name:a.province_name||"",manage_province_id:a.province_id||"",manage_city_name:a.city_name||"",manage_city_id:a.city_id||"",manage_district_name:a.district_name||"",manage_district_id:a.district_id||"",place_id:(i.placeId||1).toString(),place_name:i.placeName||"",place_desc:i.placeDesc||"",header_bg_image:i.headerBgImage||"",introduction:i.introduction||"",footer_text:i.footerText||"",manage_mode:"true",timestamp:Date.now()};t("log","at pages/culture/heritage.vue:1063","地点编辑跳转数据:",l);const n=getApp(),c=n.globalData||(n.globalData={});c.placeEditData=l,t("log","at pages/culture/heritage.vue:1070","地点编辑全局数据已设置:",c.placeEditData),uni.navigateTo({url:`/pages/culture/place-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1076","地点编辑页面跳转成功")},fail:e=>{t("error","at pages/culture/heritage.vue:1079","跳转失败",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})}catch(e){t("error","at pages/culture/heritage.vue:1087","编辑处理失败",e),uni.showToast({title:"操作失败，请重试",icon:"none"})}else uni.showToast({title:"没有可编辑的地点信息",icon:"none"})},P=()=>{var e;if(!(null==(e=o.value)?void 0:e.placeId))return void uni.showToast({title:"缺少地点信息",icon:"none"});const t=m.value,a=o.value,i=Math.max(0,...(o.value.timelineData||[]).map(e=>e.sortOrder||0))+1;(getApp().globalData||(getApp().globalData={})).timelineEditData={role:t.role||"",province_name:t.province_name||"",province_id:t.province_id||"",city_name:t.city_name||"",city_id:t.city_id||"",district_name:t.district_name||"",district_id:t.district_id||"",place_id:(a.placeId||0).toString(),place_name:a.placeName||"",manage_mode:"true",sort_order:i,timestamp:Date.now()},uni.navigateTo({url:`/pages/culture/timeline-edit?from=heritage&t=${Date.now()}`})},F=e=>{var a;if(!(null==(a=o.value)?void 0:a.timelineData)||!o.value.timelineData[e])return void uni.showToast({title:"时间轴项目不存在",icon:"none"});const i=o.value.timelineData[e];i.id?uni.showModal({title:"确认删除",content:`确定要删除时间轴项目"${i.title}"吗？删除后无法恢复。`,success:async e=>{var a,l;if(e.confirm)try{await async function(e){try{return await k({url:`/heritage/timeline/${e}`,method:"DELETE"}),!0}catch(a){throw t("error","at api/heritage.ts:504","删除时间轴项目失败:",a),a}}(i.id)&&(uni.showToast({title:"删除成功",icon:"success"}),await D())}catch(n){t("error","at pages/culture/heritage.vue:1273","删除时间轴项目失败:",n);let e="删除失败，请重试";(null==(l=null==(a=n.response)?void 0:a.data)?void 0:l.detail)?e=n.response.data.detail:n.message&&(e=n.message),uni.showToast({title:e,icon:"none"})}}}):uni.showToast({title:"时间轴项目ID缺失",icon:"none"})},L=()=>{var e;if(!d.value||!(null==(e=o.value)?void 0:e.placeId))return void uni.showToast({title:"当前不是管理模式或缺少地点信息",icon:"none"});const a=m.value,i=o.value,l=Math.max(0,...(o.value.heritageData||[]).map(e=>e.sortOrder||0))+1,n=getApp().globalData||(getApp().globalData={});n.heritageEditData={role:a.role||"",province_name:a.province_name||"",province_id:a.province_id||"",city_name:a.city_name||"",city_id:a.city_id||"",district_name:a.district_name||"",district_id:a.district_id||"",place_id:(i.placeId||0).toString(),place_name:i.placeName||"",manage_mode:"true",sort_order:l,timestamp:Date.now()},t("log","at pages/culture/heritage.vue:1335","文化传承编辑跳转，全局数据:",n.heritageEditData),uni.navigateTo({url:`/pages/culture/heritage-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1341","文化传承编辑页面跳转成功")},fail:e=>{t("error","at pages/culture/heritage.vue:1344","文化传承编辑页面跳转失败:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})},$=e=>{var a;if(!(null==(a=o.value)?void 0:a.heritageData)||!o.value.heritageData[e])return void uni.showToast({title:"文化遗产项目不存在",icon:"none"});const i=o.value.heritageData[e];i.id?uni.showModal({title:"确认删除",content:`确定要删除文化遗产项目"${i.title}"吗？删除后无法恢复。`,success:async e=>{var a,l;if(e.confirm)try{await async function(e){try{return await k({url:`/heritage/heritage/${e}`,method:"DELETE"}),!0}catch(a){throw t("error","at api/heritage.ts:600","删除文化遗产项目失败:",a),a}}(i.id)&&(uni.showToast({title:"删除成功",icon:"success"}),await D())}catch(n){t("error","at pages/culture/heritage.vue:1465","删除文化遗产项目失败:",n);let e="删除失败，请重试";(null==(l=null==(a=n.response)?void 0:a.data)?void 0:l.detail)?e=n.response.data.detail:n.message&&(e=n.message),uni.showToast({title:e,icon:"none"})}}}):uni.showToast({title:"文化遗产项目ID缺失",icon:"none"})},U=()=>{var e;if(!d.value||!(null==(e=o.value)?void 0:e.placeId))return void uni.showToast({title:"当前不是管理模式或缺少地点信息",icon:"none"});const a=m.value,i=o.value,l=Math.max(0,...(o.value.memoryData||[]).map(e=>e.sortOrder||0))+1,n=getApp().globalData||(getApp().globalData={});n.memoryEditData={role:a.role||"",province_name:a.province_name||"",province_id:a.province_id||"",city_name:a.city_name||"",city_id:a.city_id||"",district_name:a.district_name||"",district_id:a.district_id||"",place_id:(i.placeId||0).toString(),place_name:i.placeName||"",manage_mode:"true",sort_order:l,timestamp:Date.now()},t("log","at pages/culture/heritage.vue:1527","当代记忆编辑跳转，全局数据:",n.memoryEditData),uni.navigateTo({url:`/pages/culture/memory-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1533","当代记忆编辑页面跳转成功")},fail:e=>{t("error","at pages/culture/heritage.vue:1536","当代记忆编辑页面跳转失败:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})},R=e=>{var a;if(!(null==(a=o.value)?void 0:a.memoryData)||!o.value.memoryData[e])return void uni.showToast({title:"城市记忆项目不存在",icon:"none"});const i=o.value.memoryData[e];i.id?uni.showModal({title:"确认删除",content:`确定要删除城市记忆项目"${i.title}"吗？删除后无法恢复。`,success:async e=>{var a,l;if(e.confirm)try{await async function(e){try{return await k({url:`/heritage/memory/${e}`,method:"DELETE"}),!0}catch(a){return t("error","at api/heritage.ts:724","删除城市记忆项目失败:",a),!1}}(i.id)&&(uni.showToast({title:"删除成功",icon:"success"}),await D())}catch(n){t("error","at pages/culture/heritage.vue:1656","删除城市记忆项目失败:",n);let e="删除失败，请重试";(null==(l=null==(a=n.response)?void 0:a.data)?void 0:l.detail)?e=n.response.data.detail:n.message&&(e=n.message),uni.showToast({title:e,icon:"none"})}}}):uni.showToast({title:"城市记忆项目ID缺失",icon:"none"})},q=async()=>{if("DISTRICT_ADMIN"!==m.value.role)try{v.value=!1,await O(),j(),setTimeout(()=>{v.value=!0},100),v.value||(v.value=!0)}catch(e){uni.showToast({title:"获取区域数据失败，请稍后重试",icon:"none"})}else uni.showToast({title:"区县管理员无法切换管理区域",icon:"none"})},j=()=>{const e=u.value;if(m.value,p.value={provinceIndex:-1,cityIndex:-1,districtIndex:-1},e.province_id&&g.value.length>0){const t=g.value.findIndex(t=>t.province_id.toString()===e.province_id.toString());t>=0&&(p.value.provinceIndex=t)}if(e.city_id&&h.value.length>0){const t=h.value.findIndex(t=>t.city_id.toString()===e.city_id.toString());t>=0&&(p.value.cityIndex=t)}if(e.district_id&&y.value.length>0){const t=y.value.findIndex(t=>t.district_id.toString()===e.district_id.toString());t>=0&&(p.value.districtIndex=t)}},O=async()=>{const e=m.value;g.value=[],h.value=[],y.value=[];try{"SUPER_ADMIN"===e.role?(g.value=await E(),0===g.value.length&&(g.value=[{province_id:51,name:"四川省"},{province_id:11,name:"北京市"},{province_id:31,name:"上海市"}])):"PROVINCE_ADMIN"===e.role?e.province_id&&(h.value=await w(parseInt(e.province_id)),0===h.value.length&&(h.value=[{city_id:4,name:"攀枝花市"},{city_id:1,name:"成都市"}])):"CITY_ADMIN"===e.role&&e.province_id&&e.city_id&&(y.value=await N(parseInt(e.province_id),parseInt(e.city_id)),0===y.value.length&&(y.value=[{district_id:21,name:"米易县"},{district_id:22,name:"盐边县"}]))}catch(t){"SUPER_ADMIN"===e.role&&0===g.value.length&&(g.value=[{province_id:51,name:"四川省"},{province_id:11,name:"北京市"},{province_id:31,name:"上海市"}])}},z=()=>{v.value=!1,p.value={provinceIndex:-1,cityIndex:-1,districtIndex:-1}},G=e=>{try{if("province"===e){const e=g.value.map(e=>e.name);if(0===e.length)return;uni.showActionSheet({itemList:e,success:e=>{p.value.provinceIndex=e.tapIndex,(async e=>{try{if(p.value.provinceIndex=parseInt(e.detail.value),p.value.cityIndex=-1,p.value.districtIndex=-1,y.value=[],p.value.provinceIndex>=0&&g.value.length>0){const e=g.value[p.value.provinceIndex];try{h.value=await w(e.province_id),0===h.value.length&&(h.value=[{city_id:1,name:"成都市"},{city_id:4,name:"攀枝花市"}])}catch(t){h.value=[{city_id:1,name:"成都市"},{city_id:4,name:"攀枝花市"}]}}else h.value=[];h.value=[...h.value]}catch(t){}})({detail:{value:e.tapIndex.toString()}})}})}else if("city"===e){const e=h.value.map(e=>e.name);if(0===e.length)return;uni.showActionSheet({itemList:e,success:e=>{p.value.cityIndex=e.tapIndex,(async e=>{try{if(p.value.cityIndex=parseInt(e.detail.value),p.value.districtIndex=-1,p.value.cityIndex>=0&&h.value.length>0){if(p.value.provinceIndex<0||!g.value.length)return;const e=g.value[p.value.provinceIndex],a=h.value[p.value.cityIndex];try{y.value=await N(e.province_id,a.city_id),0===y.value.length&&(y.value=[{district_id:21,name:"米易县"},{district_id:22,name:"盐边县"}])}catch(t){y.value=[{district_id:21,name:"米易县"},{district_id:22,name:"盐边县"}]}}else y.value=[];y.value=[...y.value]}catch(t){}})({detail:{value:e.tapIndex.toString()}})}})}else if("district"===e){const e=y.value.map(e=>e.name);if(0===e.length)return;uni.showActionSheet({itemList:e,success:e=>{p.value.districtIndex=e.tapIndex,(e=>{try{p.value.districtIndex=parseInt(e.detail.value)}catch(t){}})({detail:{value:e.tapIndex.toString()}})}})}}catch(t){}},H=()=>{let e="";return p.value.provinceIndex>=0&&g.value.length>0&&(e+=g.value[p.value.provinceIndex].name,p.value.cityIndex>=0&&h.value.length>0&&(e+=" "+h.value[p.value.cityIndex].name,p.value.districtIndex>=0&&y.value.length>0&&(e+=" "+y.value[p.value.districtIndex].name))),e||"未选择"},Y=()=>{const e=u.value,a=m.value;e.district_id?u.value={province_name:e.province_name,province_id:e.province_id,city_name:e.city_name,city_id:e.city_id,district_name:"",district_id:""}:e.city_id?["SUPER_ADMIN","PROVINCE_ADMIN"].includes(a.role)&&(u.value={province_name:e.province_name,province_id:e.province_id,city_name:"",city_id:"",district_name:"",district_id:""}):e.province_id&&"SUPER_ADMIN"===a.role&&(u.value={province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),z(),D().then(()=>{uni.showToast({title:"已返回上级区域",icon:"success"})}).catch(e=>{t("error","at pages/culture/heritage.vue:2055","重新加载数据失败:",e),uni.showToast({title:"数据加载失败",icon:"none"})})},W=()=>{const e=u.value,t=m.value;if(e.district_id)return e.city_name||"市级";if(e.city_id){if(["SUPER_ADMIN","PROVINCE_ADMIN"].includes(t.role))return e.province_name||"省级"}else if(e.province_id&&"SUPER_ADMIN"===t.role)return"全国";return"上级"},J=()=>{const e=m.value,t={...u.value};try{if("SUPER_ADMIN"===e.role){if(p.value.provinceIndex>=0){const e=g.value[p.value.provinceIndex];if(p.value.cityIndex>=0){const t=h.value[p.value.cityIndex];if(p.value.districtIndex>=0){const a=y.value[p.value.districtIndex];u.value={province_name:e.name,province_id:e.province_id.toString(),city_name:t.name,city_id:t.city_id.toString(),district_name:a.name,district_id:a.district_id.toString()}}else u.value={province_name:e.name,province_id:e.province_id.toString(),city_name:t.name,city_id:t.city_id.toString(),district_name:"",district_id:""}}else u.value={province_name:e.name,province_id:e.province_id.toString(),city_name:"",city_id:"",district_name:"",district_id:""}}}else if("PROVINCE_ADMIN"===e.role)if(p.value.cityIndex>=0){const t=h.value[p.value.cityIndex];if(p.value.districtIndex>=0){const a=y.value[p.value.districtIndex];u.value={province_name:e.province_name,province_id:e.province_id,city_name:t.name,city_id:t.city_id.toString(),district_name:a.name,district_id:a.district_id.toString()}}else u.value={province_name:e.province_name,province_id:e.province_id,city_name:t.name,city_id:t.city_id.toString(),district_name:"",district_id:""}}else u.value={province_name:e.province_name,province_id:e.province_id,city_name:"",city_id:"",district_name:"",district_id:""};else if("CITY_ADMIN"===e.role)if(p.value.districtIndex>=0){const t=y.value[p.value.districtIndex];u.value={province_name:e.province_name,province_id:e.province_id,city_name:e.city_name,city_id:e.city_id,district_name:t.name,district_id:t.district_id.toString()}}else u.value={province_name:e.province_name,province_id:e.province_id,city_name:e.city_name,city_id:e.city_id,district_name:"",district_id:""}}catch(a){uni.showToast({title:"区域选择处理失败",icon:"none"})}z();t.province_id!==u.value.province_id||t.city_id!==u.value.city_id||t.district_id!==u.value.district_id?D().then(()=>{uni.showToast({title:`已切换到${K()}`,icon:"success"})}).catch(e=>{uni.showToast({title:"数据加载失败",icon:"none"})}):uni.showToast({title:`当前区域: ${K()}`,icon:"none"})},K=()=>{const e=u.value;return e.province_id||e.city_id||e.district_id?e.district_name?`${e.province_name} ${e.city_name} ${e.district_name}`:e.city_name?`${e.province_name} ${e.city_name}`:e.province_name?e.province_name:"未选择区域":"未选择区域"},te=()=>{const e=m.value,t=u.value;(getApp().globalData||(getApp().globalData={})).placeEditData={role:e.role||"",province_name:e.province_name||"",province_id:e.province_id||"",city_name:e.city_name||"",city_id:e.city_id||"",district_name:e.district_name||"",district_id:e.district_id||"",manage_province_name:t.province_name||"",manage_province_id:t.province_id||"",manage_city_name:t.city_name||"",manage_city_id:t.city_id||"",manage_district_name:t.district_name||"",manage_district_id:t.district_id||"",manage_mode:"true",timestamp:Date.now()},uni.navigateTo({url:`/pages/culture/place-edit?from=heritage&t=${Date.now()}`})},ae=e.ref(!1);return e.onMounted(()=>{try{const t=uni.getLaunchOptionsSync();try{const e=getCurrentPages();if(e&&e.length>0){const t=e[e.length-1];t&&t.options&&t.options.role&&!S.value.role&&(S.value={...t.options})}}catch(e){}t&&t.query&&t.query.role&&(S.value={...t.query})}catch(e){}M(),D(),ae.value=!0,setTimeout(()=>{const e=K();d.value&&"未选择区域"!==e&&uni.showToast({title:`当前管理区域: ${e}`,icon:"none",duration:2e3})},1e3)}),e.onUnmounted(()=>{}),(null==(i=e.getCurrentInstance())?void 0:i.proxy).$options.onShow=()=>{ae.value&&d.value&&(t("log","at pages/culture/heritage.vue:2353","从编辑页面返回，刷新数据"),D())},(null==(l=e.getCurrentInstance())?void 0:l.proxy).$options.onLoad=function(e){S.value=e||{};try{const t=[];for(const i in e)void 0!==e[i]&&null!==e[i]&&t.push(`${i}=${encodeURIComponent(e[i])}`);const a=t.join("&");if(a){const e=`/pages/culture/heritage?${a}`;uni.setStorageSync("__current_path__",e)}}catch(t){}e&&e.role&&!e.manage_mode&&(S.value.manage_mode="true"),M(),ae.value&&D()},(null==(n=e.getCurrentInstance())?void 0:n.proxy).$options.onShow=function(){ae.value&&(d.value?D():S.value.role&&!S.value.manage_mode&&(S.value.manage_mode="true",M(),d.value&&D()))},(a,i)=>{var l,n,r,u,E,w,N,k;return e.openBlock(),e.createElementBlock("view",{class:"container"},[d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"region-selector"},[e.createElementVNode("view",{class:"region-info"},[e.createElementVNode("text",{class:"current-region"},"当前管理区域："+e.toDisplayString(K()),1)]),e.createElementVNode("view",{class:"region-actions"},[e.createElementVNode("text",{class:"region-btn",onClick:q},"切换区域")])])):e.createCommentVNode("",!0),v.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"popup-overlay",onClick:z},[e.createElementVNode("view",{class:"region-select-popup",onClick:i[3]||(i[3]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-header"},[e.createElementVNode("text",{class:"popup-title"},"选择管理区域"),e.createElementVNode("text",{onClick:z,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"region-form"},[_.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"省份"),e.createElementVNode("view",{class:"picker-wrapper"},[e.createElementVNode("view",{class:"form-input picker-input",onClick:i[0]||(i[0]=e=>G("province"))},[e.createTextVNode(e.toDisplayString(p.value.provinceIndex>=0&&g.value.length>0?null==(l=g.value[p.value.provinceIndex])?void 0:l.name:"请选择省份")+" ",1),e.createElementVNode("text",{class:"picker-arrow"},"▼")])])])):e.createCommentVNode("",!0),f.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"城市"),e.createElementVNode("view",{class:"picker-wrapper"},[e.createElementVNode("view",{class:"form-input picker-input",onClick:i[1]||(i[1]=e=>G("city"))},[e.createTextVNode(e.toDisplayString(p.value.cityIndex>=0&&h.value.length>0?null==(n=h.value[p.value.cityIndex])?void 0:n.name:"请选择城市")+" ",1),e.createElementVNode("text",{class:"picker-arrow"},"▼")])])])):e.createCommentVNode("",!0),C.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"区县"),e.createElementVNode("view",{class:"picker-wrapper"},[e.createElementVNode("view",{class:"form-input picker-input",onClick:i[2]||(i[2]=e=>G("district"))},[e.createTextVNode(e.toDisplayString(p.value.districtIndex>=0&&y.value.length>0?null==(r=y.value[p.value.districtIndex])?void 0:r.name:"请选择区县")+" ",1),e.createElementVNode("text",{class:"picker-arrow"},"▼")])])])):e.createCommentVNode("",!0),B.value?(e.openBlock(),e.createElementBlock("view",{key:3,class:"form-item"},[e.createElementVNode("button",{onClick:Y,class:"return-btn"},"返回"+e.toDisplayString(W()),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"current-selection"},[e.createElementVNode("text",{class:"selection-text"},"当前选择："+e.toDisplayString(H()),1)])]),e.createElementVNode("view",{class:"popup-actions"},[e.createElementVNode("button",{onClick:z,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:J,class:"confirm-btn"},"确定")])])])):e.createCommentVNode("",!0),d.value&&!o.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"no-data-tip"},[e.createElementVNode("view",{class:"tip-content"},[e.createElementVNode("text",{class:"tip-icon"},"📝"),e.createElementVNode("text",{class:"tip-title"},"当前区域暂无文源纪数据"),e.createElementVNode("text",{class:"tip-desc"},"您可以为当前区域创建文源纪内容"),e.createElementVNode("view",{class:"tip-actions"},[e.createElementVNode("text",{class:"create-btn",onClick:te},"创建地点信息")])])])):e.createCommentVNode("",!0),o.value?(e.openBlock(),e.createElementBlock("view",{key:3,class:"header-section",style:e.normalizeStyle({marginTop:d.value?"72rpx":"0"})},[e.createElementVNode("image",{class:"header-bg",src:o.value.headerBgImage,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"place-name"},e.toDisplayString(o.value.placeName),1),e.createElementVNode("text",{class:"place-desc"},e.toDisplayString(o.value.placeDesc),1)]),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"manage-overlay",onClick:A},[e.createElementVNode("text",{class:"edit-icon"},"✏️"),e.createElementVNode("text",{class:"edit-text"},"编辑地点信息")])):e.createCommentVNode("",!0)],4)):e.createCommentVNode("",!0),o.value?(e.openBlock(),e.createElementBlock("view",{key:4,class:"intro-section"},[e.createElementVNode("text",{class:"intro-text"},e.toDisplayString((null==(u=o.value)?void 0:u.introduction)||""),1)])):e.createCommentVNode("",!0),o.value||d.value?(e.openBlock(),e.createElementBlock("view",{key:5,class:"timeline-section"},[e.createElementVNode("view",{class:"timeline-title"},[e.createElementVNode("text",null,"历史文脉溯源"),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"icon-add-btn",onClick:P},[e.createElementVNode("image",{src:Q,class:"add-btn-icon",mode:"aspectFit"})])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"timeline"},[o.value&&o.value.timelineData&&0!==o.value.timelineData.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},e.toDisplayString(d.value?'暂无时间轴数据，点击右上角"+"按钮创建':"暂无时间轴数据"),1)])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((null==(E=o.value)?void 0:E.timelineData)||[],(a,i)=>{var l,n;return e.withDirectives((e.openBlock(),e.createElementBlock("view",{class:"timeline-item",key:i},[e.createElementVNode("view",{class:"time-marker"},[e.createElementVNode("view",{class:"time-dot"}),i!==((null==(n=null==(l=o.value)?void 0:l.timelineData)?void 0:n.length)||0)-1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"time-line"})):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"time-card"},[e.createElementVNode("view",{class:"time-period"},[e.createElementVNode("text",{class:"period-name"},e.toDisplayString(a.period),1),e.createElementVNode("text",{class:"period-year"},e.toDisplayString(a.year),1),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"manage-actions timeline-actions"},[e.createElementVNode("view",{class:"icon-btn edit-btn",onClick:e.withModifiers(e=>(e=>{var a;if(t("log","at pages/culture/heritage.vue:1146","🔍 editTimelineItem 被调用, index:",e),!(null==(a=o.value)?void 0:a.timelineData)||!o.value.timelineData[e])return void uni.showToast({title:"时间轴项目不存在",icon:"none"});const i=o.value.timelineData[e];if(i.id)try{const e=m.value,a=o.value,l={role:e.role||"",province_name:e.province_name||"",province_id:e.province_id||"",city_name:e.city_name||"",city_id:e.city_id||"",district_name:e.district_name||"",district_id:e.district_id||"",place_id:(a.placeId||0).toString(),place_name:a.placeName||"",timeline_id:i.id.toString(),period:i.period||"",year:i.year||"",title:i.title||"",description:i.description||"",image:i.image||"",has_detail:i.hasDetail||!1,detail:i.detail||"",detail_images:i.detailImages||[],heritage_tags:i.heritages||[],is_active:!0,sort_order:i.sortOrder||0,manage_mode:"true",timestamp:Date.now()};t("log","at pages/culture/heritage.vue:1202","历史文脉编辑跳转数据:",l);const n=getApp(),c=n.globalData||(n.globalData={});c.timelineEditData=l,t("log","at pages/culture/heritage.vue:1209","历史文脉编辑全局数据已设置:",c.timelineEditData),uni.navigateTo({url:`/pages/culture/timeline-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1215","历史文脉编辑页面跳转成功")},fail:e=>{t("error","at pages/culture/heritage.vue:1218","跳转失败",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})}catch(l){t("error","at pages/culture/heritage.vue:1226","编辑处理失败",l),uni.showToast({title:"操作失败，请重试",icon:"none"})}else uni.showToast({title:"时间轴项目ID缺失，无法编辑",icon:"none"})})(i),["stop"])},[e.createElementVNode("image",{src:Z,class:"btn-icon",mode:"aspectFit"})],8,["onClick"]),e.createElementVNode("view",{class:"icon-btn delete-btn",onClick:e.withModifiers(e=>F(i),["stop"])},[e.createElementVNode("image",{src:ee,class:"btn-icon",mode:"aspectFit"})],8,["onClick"])])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("image",{src:b(a.image),mode:"aspectFill",class:"card-image",onError:e=>I(e)},null,40,["src","onError"]),e.createElementVNode("view",{class:"card-text"},[e.createElementVNode("text",{class:"card-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"card-desc"},e.toDisplayString(a.description),1)])]),a.heritages&&a.heritages.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"heritage-tags"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.heritages,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"tag",key:a},e.toDisplayString(t),1))),128))])):e.createCommentVNode("",!0),a.hasDetail?(e.openBlock(),e.createElementBlock("view",{key:1,class:"expand-section",onClick:e.withModifiers(e=>(e=>{if(o.value&&o.value.timelineData&&o.value.timelineData[e]){o.value.timelineData[e],s.value.includes(e)||s.value.push(e);const t=o.value.timelineData[e].isExpanded;o.value.timelineData[e].isExpanded=!t,o.value={...o.value}}})(i),["stop"])},[e.createElementVNode("text",{class:"expand-text"},e.toDisplayString(a.isExpanded?"收起":"展开更多"),1),e.createElementVNode("text",{class:"expand-icon"},e.toDisplayString(a.isExpanded?"▲":"▼"),1)],8,["onClick"])):e.createCommentVNode("",!0),a.isExpanded&&(a.detail||a.detailImages&&a.detailImages.length>0)?(e.openBlock(),e.createElementBlock("view",{key:2,class:"detail-content"},[a.detail?(e.openBlock(),e.createElementBlock("rich-text",{key:0,nodes:a.detail},null,8,["nodes"])):e.createCommentVNode("",!0),a.detailImages&&a.detailImages.length>0?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.detailImages,(t,i)=>(e.openBlock(),e.createElementBlock("image",{key:i,src:t,mode:"aspectFill",class:"detail-image",onClick:e=>{return t=a.detailImages,l=i,void uni.previewImage({urls:t,current:t[l]});var t,l}},null,8,["src","onClick"]))),128))])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])])),[[e.vShow,s.value.includes(i)]])}),128))])])):e.createCommentVNode("",!0),o.value||d.value?(e.openBlock(),e.createElementBlock("view",{key:6,class:"modern-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"当代文化传承")]),e.createElementVNode("view",{class:"heritage-list"},[o.value&&o.value.heritageData&&0!==o.value.heritageData.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},e.toDisplayString(d.value?'暂无文化传承数据，点击下方"+"新建文化传承':"暂无文化传承数据"),1)])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((null==(w=o.value)?void 0:w.heritageData)||[],(a,i)=>(e.openBlock(),e.createElementBlock("view",{class:"heritage-item",key:i,onClick:e=>{var t;(t=a.id)?uni.navigateTo({url:`/pages/culture/heritage-detail?heritage_id=${t}`}):uni.showToast({title:"文化遗产ID缺失",icon:"none"})}},[e.createElementVNode("image",{src:b(a.image),mode:"aspectFill",class:"heritage-image",onError:e=>I(e)},null,40,["src","onError"]),e.createElementVNode("view",{class:"heritage-info"},[e.createElementVNode("text",{class:"heritage-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"heritage-type"},e.toDisplayString(a.type),1),e.createElementVNode("text",{class:"heritage-brief"},e.toDisplayString(a.brief),1)]),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"manage-actions card-actions"},[e.createElementVNode("view",{class:"icon-btn edit-btn",onClick:e.withModifiers(e=>(e=>{var a;if(!(null==(a=o.value)?void 0:a.heritageData)||!o.value.heritageData[e])return void uni.showToast({title:"文化遗产项目不存在",icon:"none"});const i=o.value.heritageData[e];if(!i.id)return void uni.showToast({title:"文化遗产项目ID缺失，无法编辑",icon:"none"});const l=m.value,n=o.value,c=getApp().globalData||(getApp().globalData={});c.heritageEditData={role:l.role||"",province_name:l.province_name||"",province_id:l.province_id||"",city_name:l.city_name||"",city_id:l.city_id||"",district_name:l.district_name||"",district_id:l.district_id||"",place_id:(n.placeId||0).toString(),place_name:n.placeName||"",heritage_id:i.id.toString(),title:i.title||"",brief:i.brief||"",image:i.image||"",type:i.type||"",is_active:!0,sort_order:i.sortOrder||0,manage_mode:"true",timestamp:Date.now()},t("log","at pages/culture/heritage.vue:1405","文化传承编辑跳转（编辑模式），全局数据:",c.heritageEditData),uni.navigateTo({url:`/pages/culture/heritage-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1414","文化传承编辑页面跳转成功（编辑模式）")},fail:e=>{t("error","at pages/culture/heritage.vue:1417","文化传承编辑页面跳转失败（编辑模式）:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})})(i),["stop"])},[e.createElementVNode("image",{src:Z,class:"btn-icon",mode:"aspectFit"})],8,["onClick"]),e.createElementVNode("view",{class:"icon-btn delete-btn",onClick:e.withModifiers(e=>$(i),["stop"])},[e.createElementVNode("image",{src:ee,class:"btn-icon",mode:"aspectFit"})],8,["onClick"])])):e.createCommentVNode("",!0)],8,["onClick"]))),128)),d.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"add-heritage-card",onClick:L},[e.createElementVNode("view",{class:"add-content"},[e.createElementVNode("image",{src:Q,class:"add-icon-svg",mode:"aspectFit"}),e.createElementVNode("text",{class:"add-card-text"},"新增文化传承")])])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),o.value||d.value?(e.openBlock(),e.createElementBlock("view",{key:7,class:"memory-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"当代城市记忆"),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"icon-add-btn",onClick:U},[e.createElementVNode("image",{src:Q,class:"add-btn-icon",mode:"aspectFit"})])):e.createCommentVNode("",!0)]),o.value&&o.value.memoryData&&0!==o.value.memoryData.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},e.toDisplayString(d.value?'暂无城市记忆数据，点击右上角"+"按钮创建':"暂无城市记忆数据"),1)])),o.value&&o.value.memoryData&&o.value.memoryData.length>0?(e.openBlock(),e.createElementBlock("scroll-view",{key:1,"scroll-x":"",class:"memory-scroll"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((null==(N=o.value)?void 0:N.memoryData)||[],(a,i)=>(e.openBlock(),e.createElementBlock("view",{class:"memory-item",key:i,onClick:e=>{return t=a.id,void uni.navigateTo({url:`/pages/culture/memory-detail?memory_id=${t}`});var t}},[e.createElementVNode("image",{src:b(a.image),mode:"aspectFill",class:"memory-image",onError:e=>I(e)},null,40,["src","onError"]),e.createElementVNode("view",{class:"memory-title"},e.toDisplayString(a.title),1),e.createElementVNode("view",{class:"memory-year"},e.toDisplayString(a.year),1),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"manage-actions overlay"},[e.createElementVNode("view",{class:"icon-btn edit-btn",onClick:e.withModifiers(e=>(e=>{var a;if(!(null==(a=o.value)?void 0:a.memoryData)||!o.value.memoryData[e])return void uni.showToast({title:"城市记忆项目不存在",icon:"none"});const i=o.value.memoryData[e];if(!i.id)return void uni.showToast({title:"城市记忆项目ID缺失，无法编辑",icon:"none"});const l=m.value,n=o.value,c=getApp().globalData||(getApp().globalData={});c.memoryEditData={role:l.role||"",province_name:l.province_name||"",province_id:l.province_id||"",city_name:l.city_name||"",city_id:l.city_id||"",district_name:l.district_name||"",district_id:l.district_id||"",place_id:(n.placeId||0).toString(),place_name:n.placeName||"",memory_id:i.id.toString(),title:i.title||"",year:i.year||"",image:i.image||"",is_active:!0,sort_order:i.sortOrder||0,manage_mode:"true",timestamp:Date.now()},t("log","at pages/culture/heritage.vue:1596","当代记忆编辑跳转（编辑模式），全局数据:",c.memoryEditData),uni.navigateTo({url:`/pages/culture/memory-edit?from=heritage&t=${Date.now()}`,success:()=>{t("log","at pages/culture/heritage.vue:1605","当代记忆编辑页面跳转成功（编辑模式）")},fail:e=>{t("error","at pages/culture/heritage.vue:1608","当代记忆编辑页面跳转失败（编辑模式）:",e),uni.showToast({title:"页面跳转失败",icon:"none"})}})})(i),["stop"])},[e.createElementVNode("image",{src:Z,class:"btn-icon",mode:"aspectFit"})],8,["onClick"]),e.createElementVNode("view",{class:"icon-btn delete-btn",onClick:e.withModifiers(e=>R(i),["stop"])},[e.createElementVNode("image",{src:ee,class:"btn-icon",mode:"aspectFit"})],8,["onClick"])])):e.createCommentVNode("",!0)],8,["onClick"]))),128))])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),o.value?(e.openBlock(),e.createElementBlock("view",{key:8,class:"footer"},[e.createElementVNode("text",{class:"footer-text"},e.toDisplayString((null==(k=o.value)?void 0:k.footerText)||""),1)])):e.createCommentVNode("",!0),c.value?(e.openBlock(),e.createElementBlock("view",{key:9,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0)])}}});async function ae(e,a="images",i){return new Promise(async i=>{const l=uni.getStorageSync("access_token");if(!l)return void i({success:!1,message:"请先登录"});if(!(await async function(){return!0}()))return void i({success:!1,message:"文件大小超过限制"});const n=function(e){return`${y()}/upload/${(e=>{switch(e){case"ancient-books":return"ancient-books";case"heritage":return"heritage";case"memory":return"memory";case"timeline":return"timeline";case"place":return"place";case"archive":return"archive";default:return"images"}})(e)}/image`}(a);uni.showLoading({title:"上传中...",mask:!0}),uni.uploadFile({url:n,filePath:e,name:"file",header:{Authorization:`Bearer ${l}`},timeout:6e4,success:e=>{try{if(200===e.statusCode){const t=JSON.parse(e.data);t.success?i({success:!0,url:t.data.url}):i({success:!1,message:t.message||"上传失败"})}else 401===e.statusCode?(uni.removeStorageSync("access_token"),uni.showModal({title:"登录已过期",content:"请重新登录后再上传",showCancel:!1,success:()=>{uni.navigateTo({url:"/pages/user/index"})}}),i({success:!1,message:"登录已过期"})):i({success:!1,message:`上传失败，状态码: ${e.statusCode}`})}catch(a){t("error","at utils/upload.ts:180","解析上传结果失败:",a),i({success:!1,message:"上传结果解析失败"})}},fail:e=>{t("error","at utils/upload.ts:188","上传失败:",e);let a="上传失败";e.errMsg&&(e.errMsg.includes("timeout")?a="上传超时，请检查网络":e.errMsg.includes("fail")&&(a="网络连接失败")),i({success:!1,message:a})},complete:()=>{uni.hideLoading()}})})}const ie=e.defineComponent({__name:"place-edit",setup(a,{expose:i}){const l=e.ref(!1),n=e.ref(!1),o=e.ref("加载中..."),c=e.ref(null),r=e.ref(!1),s=e.ref({role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),d=e.ref({province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),m=e.ref({place_name:"",place_desc:"",header_bg_image:"",introduction:"",footer_text:"",province_id:0,city_id:0,district_id:0,is_active:!0,sort_order:0});i({onLoad:e=>{t("log","at pages/culture/place-edit.vue:273","🔍 place-edit onLoad 接收到的参数:",e),uni.setNavigationBarTitle({title:"地点信息编辑"})}});const u=()=>{const e=d.value;return e.district_name?`${e.province_name} ${e.city_name} ${e.district_name}`:e.city_name?`${e.province_name} ${e.city_name}`:e.province_name?e.province_name:"未选择区域"},v=e=>{t("error","at pages/culture/place-edit.vue:407","头部背景图上传失败:",e),uni.showToast({title:e||"图片上传失败，请重试",icon:"none",duration:2e3})},p=async()=>{try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"]});e.tempFilePaths&&e.tempFilePaths.length>0&&await y(e.tempFilePaths[0])}catch(e){t("error","at pages/culture/place-edit.vue:428","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},g=()=>{p()},h=()=>{m.value.header_bg_image="",uni.showToast({title:"图片已删除",icon:"success",duration:1e3})},y=async e=>{r.value=!0;try{const a=await ae(e,"place");if(a.success&&a.url){const e=Y(a.url);m.value.header_bg_image=e,(e=>{t("log","at pages/culture/place-edit.vue:396","头部背景图上传成功:",e),m.value.header_bg_image=e,uni.showToast({title:"图片上传成功",icon:"success",duration:1500})})(e)}else v(a.message||"上传失败")}catch(a){t("error","at pages/culture/place-edit.vue:469","上传图片失败:",a),v("上传失败，请重试")}finally{r.value=!1}},_=e=>{m.value.is_active=e.detail.value},E=async()=>{var e,a;if(m.value.place_name.trim())if(l.value||m.value.province_id){n.value=!0,o.value=l.value?"保存中...":"创建中...";try{if(l.value&&c.value){const e={place_name:m.value.place_name.trim(),place_desc:m.value.place_desc.trim()||void 0,header_bg_image:m.value.header_bg_image.trim()||null,introduction:m.value.introduction.trim()||void 0,footer_text:m.value.footer_text.trim()||void 0,is_active:m.value.is_active,sort_order:m.value.sort_order||0};t("log","at pages/culture/place-edit.vue:516","保存时的表单数据:",m.value),t("log","at pages/culture/place-edit.vue:517","发送给后端的数据:",e),await async function(e,a){try{return await k({url:`/heritage/places/${e}`,method:"PUT",data:a})}catch(i){throw t("error","at api/heritage.ts:385","更新文化遗产地点失败:",i),i}}(c.value,e),uni.showToast({title:"保存成功",icon:"success"})}else{const e={place_name:m.value.place_name.trim(),place_desc:m.value.place_desc.trim()||void 0,header_bg_image:m.value.header_bg_image.trim()||void 0,introduction:m.value.introduction.trim()||void 0,footer_text:m.value.footer_text.trim()||void 0,province_id:m.value.province_id,city_id:m.value.city_id||void 0,district_id:m.value.district_id||void 0,is_active:m.value.is_active,sort_order:m.value.sort_order||0};await async function(e){try{return await k({url:"/heritage/places",method:"POST",data:e})}catch(a){throw t("error","at api/heritage.ts:367","创建文化遗产地点失败:",a),a}}(e),uni.showToast({title:"创建成功",icon:"success"})}setTimeout(()=>{uni.navigateBack({delta:1})},1500)}catch(i){t("error","at pages/culture/place-edit.vue:555","保存地点失败:",i);let l="保存失败，请重试";(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.detail)?l=i.response.data.detail:i.message&&(l=i.message),uni.showToast({title:l,icon:"none"})}finally{n.value=!1}}else uni.showToast({title:"请选择管理区域",icon:"none"});else uni.showToast({title:"请输入地点名称",icon:"none"})};return e.onMounted(()=>{t("log","at pages/culture/place-edit.vue:575","🔍 place-edit onMounted 开始初始化"),(()=>{const a=getApp();if(a.globalData&&a.globalData.placeEditData){const i=a.globalData.placeEditData;if(t("log","at pages/culture/place-edit.vue:198","🔍 place-edit 接收到的全局数据:",i),s.value={role:i.role||"",province_name:i.province_name||"",province_id:i.province_id||"",city_name:i.city_name||"",city_id:i.city_id||"",district_name:i.district_name||"",district_id:i.district_id||""},d.value={province_name:i.manage_province_name||"",province_id:i.manage_province_id||"",city_name:i.manage_city_name||"",city_id:i.manage_city_id||"",district_name:i.manage_district_name||"",district_id:i.manage_district_id||""},i.place_id)l.value=!0,c.value=parseInt(i.place_id),t("log","at pages/culture/place-edit.vue:227","🔍 使用全局数据填充表单"),m.value.place_name=i.place_name||"",m.value.place_desc=i.place_desc||"",m.value.header_bg_image=i.header_bg_image||"",m.value.introduction=i.introduction||"",m.value.footer_text=i.footer_text||"",m.value.province_id=parseInt(i.manage_province_id)||0,m.value.city_id=parseInt(i.manage_city_id)||0,m.value.district_id=parseInt(i.manage_district_id)||0,m.value.is_active=!0,m.value.sort_order=0,t("log","at pages/culture/place-edit.vue:241","🔍 表单数据已填充:",m.value),e.nextTick(()=>{t("log","at pages/culture/place-edit.vue:245","🔍 nextTick后的表单数据:",m.value)});else{const e=d.value;m.value.province_id=parseInt(e.province_id)||0,m.value.city_id=parseInt(e.city_id)||0,m.value.district_id=parseInt(e.district_id)||0}}else t("error","at pages/culture/place-edit.vue:259","未找到全局参数数据"),uni.showToast({title:"参数加载失败",icon:"none"}),setTimeout(()=>{uni.navigateBack()},1500)})(),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:"#C8161E"}),uni.setNavigationBarTitle({title:l.value?"编辑地点信息":"创建地点信息"})}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"基本信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"地点名称 * (当前值: "+e.toDisplayString(m.value.place_name)+")",1),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[0]||(a[0]=e=>m.value.place_name=e),placeholder:"请输入地点名称",maxlength:"100"},null,512),[[e.vModelText,m.value.place_name]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"地点描述 (当前值: "+e.toDisplayString(m.value.place_desc)+")",1),e.withDirectives(e.createElementVNode("textarea",{class:"form-textarea short","onUpdate:modelValue":a[1]||(a[1]=e=>m.value.place_desc=e),placeholder:"请输入地点描述",maxlength:"255","auto-height":!0,"min-height":80},null,512),[[e.vModelText,m.value.place_desc]])]),l.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"管理区域"),e.createElementVNode("view",{class:"region-display"},[e.createElementVNode("text",{class:"region-text"},e.toDisplayString(u()),1)])]))]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"页面内容"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"头部背景图"),e.createElementVNode("view",{class:"upload-tip"},[e.createElementVNode("text",{class:"tip-text"},"建议尺寸：1200x600像素，支持JPG、PNG格式")]),e.createElementVNode("view",{class:"simple-image-upload"},[m.value.header_bg_image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"image-preview"},[e.createElementVNode("image",{src:m.value.header_bg_image,class:"preview-img",mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"image-actions"},[e.createElementVNode("text",{class:"action-btn",onClick:g},"更换"),e.createElementVNode("text",{class:"action-btn delete",onClick:h},"删除")])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"upload-btn",onClick:p},[e.createElementVNode("text",{class:"upload-text"},"+ 选择图片")])),r.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"upload-progress"},[e.createElementVNode("text",null,"上传中...")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"简介"),e.withDirectives(e.createElementVNode("textarea",{class:"form-textarea","onUpdate:modelValue":a[2]||(a[2]=e=>m.value.introduction=e),placeholder:"请输入地点简介，将显示在页面顶部",maxlength:"2000","show-count":!0,"auto-height":!0,"min-height":120},null,512),[[e.vModelText,m.value.introduction]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"底部文本"),e.withDirectives(e.createElementVNode("textarea",{class:"form-textarea short","onUpdate:modelValue":a[3]||(a[3]=e=>m.value.footer_text=e),placeholder:"请输入底部文本，如座右铭、标语等",maxlength:"500","auto-height":!0,"min-height":80},null,512),[[e.vModelText,m.value.footer_text]])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"其他设置"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"启用状态"),e.createElementVNode("switch",{checked:m.value.is_active,onChange:_,color:"#007aff"},null,40,["checked"]),e.createElementVNode("text",{class:"form-desc"},"关闭后，用户将无法访问该地点页面")]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"排序"),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[4]||(a[4]=e=>m.value.sort_order=e),placeholder:"数字越小排序越靠前",type:"number"},null,512),[[e.vModelText,m.value.sort_order,void 0,{number:!0}]])])]),e.createElementVNode("view",{class:"save-section"},[e.createElementVNode("button",{class:"save-button",onClick:E,disabled:n.value},e.toDisplayString(n.value?l.value?"保存中...":"创建中...":l.value?"保存":"创建"),9,["disabled"])])]),n.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(o.value),1)])):e.createCommentVNode("",!0)]))}}),le=[{name:"基础段落模板",description:"适用于一般文字描述",preview:"<p>这是一个段落...</p>",category:"基础",tags:["段落","文字","基础"],content:"<p>在这里输入您的内容...</p>\n<p>您可以添加多个段落来组织内容。</p>"},{name:"要点列表模板",description:"适用于列举要点",preview:"• 要点一 • 要点二",category:"列表",tags:["要点","列表","归纳"],content:"<p>主要特点：</p>\n<p>• 第一个要点</p>\n<p>• 第二个要点</p>\n<p>• 第三个要点</p>"},{name:"时间发展模板",description:"适用于描述历史发展过程",preview:"年份 - 事件描述",category:"历史",tags:["时间","发展","历史"],content:"<p>这一时期的重要发展：</p>\n<p>• 初期：发展背景和起源</p>\n<p>• 中期：重要变化和发展</p>\n<p>• 后期：成果和影响</p>\n<p>这些发展为后来的历史奠定了重要基础。</p>"},{name:"文化特色模板",description:"适用于介绍文化特色",preview:"文化特色介绍...",category:"文化",tags:["文化","特色","介绍"],content:"<p>这一时期的文化特色：</p>\n<p>• 建筑风格：描述建筑特点</p>\n<p>• 生活方式：描述生活特点</p>\n<p>• 艺术表现：描述艺术特点</p>\n<p>• 社会制度：描述社会特点</p>\n<p>这些文化特色体现了当时的时代特征。</p>"},{name:"影响意义模板",description:"适用于总结历史影响和意义",preview:"历史影响和意义...",category:"分析",tags:["影响","意义","总结"],content:"<p>这一时期的历史意义：</p>\n<p>• 政治影响：对政治制度的影响</p>\n<p>• 经济影响：对经济发展的影响</p>\n<p>• 文化影响：对文化传承的影响</p>\n<p>• 社会影响：对社会发展的影响</p>\n<p>这些影响对后世产生了深远的意义。</p>"},{name:"强调重点模板",description:"用于突出重要内容",preview:"【重要】突出显示的内容",category:"强调",tags:["重要","强调","突出"],content:'<p style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; border-radius: 4px;">\n<strong>【重要提示】</strong>这里是需要特别强调的重要内容，会以醒目的样式显示。\n</p>\n<p>普通内容可以继续在这里描述...</p>'},{name:"引用说明模板",description:"用于引用文献或说明出处",preview:"引用内容和出处说明",category:"引用",tags:["引用","文献","出处"],content:'<p style="border-left: 3px solid #007aff; padding-left: 15px; margin: 15px 0; font-style: italic; color: #666;">\n"这里是引用的原文内容，可以是古籍记载、史料描述等重要文献资料。"\n</p>\n<p style="text-align: right; font-size: 14px; color: #999; margin-top: 5px;">\n—— 出处：《文献名称》或相关史料\n</p>\n<p>基于以上史料记载，我们可以了解到...</p>'},{name:"时间轴详细模板",description:"适用于详细的时间线描述",preview:"时间节点 → 事件详情",category:"时间轴",tags:["时间轴","时间线","详细"],content:'<div style="border-left: 2px solid #007aff; padding-left: 20px;">\n<p style="margin-bottom: 15px;"><strong style="color: #007aff;">公元XXX年</strong></p>\n<p style="margin-left: 15px;">• 重要事件一：事件详细描述</p>\n<p style="margin-left: 15px;">• 重要事件二：事件详细描述</p>\n<p style="margin-left: 15px;">• 重要事件三：事件详细描述</p>\n</div>\n<div style="border-left: 2px solid #28a745; padding-left: 20px; margin-top: 20px;">\n<p style="margin-bottom: 15px;"><strong style="color: #28a745;">公元XXX年</strong></p>\n<p style="margin-left: 15px;">• 重要事件一：事件详细描述</p>\n<p style="margin-left: 15px;">• 重要事件二：事件详细描述</p>\n</div>'},{name:"对比分析模板",description:"用于对比不同时期或方面",preview:"对比项目A vs 对比项目B",category:"分析",tags:["对比","分析","比较"],content:'<div style="display: flex; gap: 20px; margin: 15px 0;">\n<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007aff;">\n<h4 style="color: #007aff; margin-bottom: 10px;">前期特征</h4>\n<p>• 特征描述一</p>\n<p>• 特征描述二</p>\n<p>• 特征描述三</p>\n</div>\n<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">\n<h4 style="color: #28a745; margin-bottom: 10px;">后期特征</h4>\n<p>• 特征描述一</p>\n<p>• 特征描述二</p>\n<p>• 特征描述三</p>\n</div>\n</div>\n<p>通过对比可以看出...</p>'},{name:"数据统计模板",description:"用于展示数据和统计信息",preview:"数据项：具体数值",category:"数据",tags:["数据","统计","展示"],content:'<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;">\n<h4 style="text-align: center; color: #333; margin-bottom: 15px;">重要数据统计</h4>\n<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">\n<div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px;">\n<div style="font-size: 24px; font-weight: bold; color: #007aff;">XXX</div>\n<div style="font-size: 14px; color: #666;">数据项目一</div>\n</div>\n<div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px;">\n<div style="font-size: 24px; font-weight: bold; color: #28a745;">XXX</div>\n<div style="font-size: 14px; color: #666;">数据项目二</div>\n</div>\n</div>\n</div>\n<p>数据说明：以上统计数据反映了...</p>'},{name:"成就展示模板",description:"用于展示重要成就和贡献",preview:"成就标题 + 详细说明",category:"展示",tags:["成就","展示","贡献"],content:'<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin: 15px 0;">\n<h3 style="margin-bottom: 15px; text-align: center;">重要成就</h3>\n<div style="background-color: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">\n<p><strong>📍 成就一：</strong>具体成就描述</p>\n<p><strong>🏆 成就二：</strong>具体成就描述</p>\n<p><strong>⭐ 成就三：</strong>具体成就描述</p>\n</div>\n</div>\n<p>这些成就的历史意义在于...</p>'},{name:"人物介绍模板",description:"用于介绍重要历史人物",preview:"人物姓名 + 生平简介",category:"人物",tags:["人物","介绍","历史"],content:'<div style="border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; margin: 15px 0; background-color: #fafafa;">\n<div style="display: flex; align-items: center; margin-bottom: 15px;">\n<div style="width: 4px; height: 40px; background-color: #007aff; margin-right: 15px;"></div>\n<h3 style="color: #333; margin: 0;">人物姓名（生卒年份）</h3>\n</div>\n<p><strong>身份职位：</strong>具体身份描述</p>\n<p><strong>主要贡献：</strong>重要贡献和成就</p>\n<p><strong>历史评价：</strong>后世对其的评价和影响</p>\n</div>\n<p>该人物在这一时期的作用...</p>'},{name:"事件记录模板",description:"用于详细记录重要历史事件",preview:"事件名称 + 过程描述",category:"事件",tags:["事件","记录","历史"],content:'<div style="background-color: #fff; border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; margin: 15px 0;">\n<div style="background-color: #007aff; color: white; padding: 15px;">\n<h3 style="margin: 0;">重要历史事件</h3>\n</div>\n<div style="padding: 20px;">\n<p><strong>🕐 发生时间：</strong>具体时间</p>\n<p><strong>📍 发生地点：</strong>具体地点</p>\n<p><strong>👥 参与人物：</strong>相关人物</p>\n<p><strong>📋 事件经过：</strong></p>\n<div style="margin-left: 20px; border-left: 3px solid #007aff; padding-left: 15px;">\n<p>1. 事件起因和背景</p>\n<p>2. 事件发展过程</p>\n<p>3. 事件结果和影响</p>\n</div>\n</div>\n</div>'},{name:"总结回顾模板",description:"用于总结和回顾历史时期",preview:"时期总结 + 历史地位",category:"总结",tags:["总结","回顾","历史"],content:'<div style="background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%); padding: 2px; border-radius: 12px; margin: 20px 0;">\n<div style="background-color: white; padding: 20px; border-radius: 10px;">\n<h3 style="text-align: center; color: #333; margin-bottom: 20px;">历史时期总结</h3>\n<div style="border-top: 2px solid #f5576c; padding-top: 15px;">\n<p><strong>🔸 时代特征：</strong>这一时期的主要特征和特点</p>\n<p><strong>🔸 重要成就：</strong>取得的主要成就和进步</p>\n<p><strong>🔸 历史地位：</strong>在历史长河中的重要地位</p>\n<p><strong>🔸 后世影响：</strong>对后续历史发展的深远影响</p>\n</div>\n</div>\n</div>\n<p style="text-align: center; font-style: italic; color: #666;">\n这一时期在历史发展中承前启后，具有重要的历史意义。\n</p>'},{name:"表格数据模板",description:"用于展示结构化数据",preview:"表格形式的数据展示",category:"数据",tags:["表格","数据","结构化"],content:'<div style="overflow-x: auto; margin: 15px 0;">\n<table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">\n<thead style="background-color: #f8f9fa;">\n<tr>\n<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">项目</th>\n<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">内容</th>\n<th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">备注</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style="border: 1px solid #dee2e6; padding: 12px;">数据项目一</td>\n<td style="border: 1px solid #dee2e6; padding: 12px;">具体内容</td>\n<td style="border: 1px solid #dee2e6; padding: 12px;">相关说明</td>\n</tr>\n<tr style="background-color: #f8f9fa;">\n<td style="border: 1px solid #dee2e6; padding: 12px;">数据项目二</td>\n<td style="border: 1px solid #dee2e6; padding: 12px;">具体内容</td>\n<td style="border: 1px solid #dee2e6; padding: 12px;">相关说明</td>\n</tr>\n</tbody>\n</table>\n</div>\n<p>表格说明：以上数据展示了...</p>'},{name:"步骤流程模板",description:"用于描述发展过程或操作步骤",preview:"步骤1 → 步骤2 → 步骤3",category:"流程",tags:["步骤","流程","过程"],content:'<div style="margin: 20px 0;">\n<div style="display: flex; align-items: center; margin-bottom: 15px;">\n<div style="width: 30px; height: 30px; background-color: #007aff; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">1</div>\n<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">\n<strong>第一步：</strong>步骤描述和具体内容\n</div>\n</div>\n<div style="display: flex; align-items: center; margin-bottom: 15px;">\n<div style="width: 30px; height: 30px; background-color: #28a745; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">2</div>\n<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">\n<strong>第二步：</strong>步骤描述和具体内容\n</div>\n</div>\n<div style="display: flex; align-items: center; margin-bottom: 15px;">\n<div style="width: 30px; height: 30px; background-color: #ffc107; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 15px;">3</div>\n<div style="flex: 1; background-color: #f8f9fa; padding: 15px; border-radius: 8px;">\n<strong>第三步：</strong>步骤描述和具体内容\n</div>\n</div>\n</div>\n<p>通过以上步骤，最终形成了...</p>'},{name:"注意提醒模板",description:"用于重要提醒和注意事项",preview:"⚠️ 注意事项提醒",category:"提醒",tags:["注意","提醒","警告"],content:'<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0;">\n<div style="display: flex; align-items: center; margin-bottom: 10px;">\n<span style="font-size: 20px; margin-right: 10px;">⚠️</span>\n<strong style="color: #856404;">注意事项</strong>\n</div>\n<p style="margin: 0; color: #856404;">这里是需要特别注意的重要信息，请仔细阅读和理解。</p>\n</div>\n<p>除了上述注意事项外...</p>'},{name:"小贴士模板",description:"用于提供有用的补充信息",preview:"💡 实用小贴士",category:"提示",tags:["贴士","提示","补充"],content:'<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin: 15px 0;">\n<div style="display: flex; align-items: center; margin-bottom: 10px;">\n<span style="font-size: 20px; margin-right: 10px;">💡</span>\n<strong style="color: #0c5460;">小贴士</strong>\n</div>\n<p style="margin: 0; color: #0c5460;">这里提供一些有用的补充信息和背景知识，帮助更好地理解相关内容。</p>\n</div>\n<p>了解这些背景信息后...</p>'}];function ne(e,t=le){if(!e.trim())return t;const a=e.toLowerCase().trim();return t.filter(e=>{var t,i;return e.name.toLowerCase().includes(a)||e.description.toLowerCase().includes(a)||e.preview.toLowerCase().includes(a)||(null==(t=e.category)?void 0:t.toLowerCase().includes(a))||(null==(i=e.tags)?void 0:i.some(e=>e.toLowerCase().includes(a)))})}const oe=e.defineComponent({__name:"timeline-edit",setup(a,{expose:i}){const l=e.ref(!1),n=e.ref(!1),o=e.ref("加载中..."),c=e.ref(null),r=e.ref(null),s=e.ref(!1),d=e.ref(""),m=e.ref(!1),u=e.ref(""),v=e.ref([]),p=e.ref({role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),g=e.ref({place_id:0,place_name:""}),h=e.ref({period:"",year:"",title:"",description:"",image:"",has_detail:!1,detail:"",detail_images:[],heritage_tags:[],is_active:!0,sort_order:0}),y=e.ref(["国家级非物质文化遗产","省级非物质文化遗产","市级非物质文化遗产","世界文化遗产","国家重点文物保护单位","省级文物保护单位","历史文化名城","传统手工艺","民族文化","古建筑","文化景观","民俗活动","传统技艺","口传文学","传统音乐","传统舞蹈","传统戏剧","传统体育","传统美术","传统医药"]),_=async()=>{var e,a;if(c.value){n.value=!0,o.value="加载时间轴信息...";try{const e=await async function(e){try{return await k({url:`/heritage/timeline/${e}`,method:"GET"})}catch(a){throw t("error","at api/heritage.ts:487","获取时间轴项目失败:",a),a}}(c.value);e?(h.value={period:e.period||"",year:e.year||"",title:e.title||"",description:e.description||"",image:e.image||"",has_detail:e.has_detail||!1,detail:e.detail||"",detail_images:e.detail_images||[],heritage_tags:e.heritage_tags||[],is_active:void 0===e.is_active||e.is_active,sort_order:e.sort_order||0},t("log","at pages/culture/timeline-edit.vue:517","时间轴数据加载成功:",e)):(uni.showToast({title:"未找到时间轴数据",icon:"none"}),setTimeout(()=>{uni.navigateBack()},2e3))}catch(i){t("error","at pages/culture/timeline-edit.vue:529","加载时间轴信息失败:",i);let l="加载失败，请重试";(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.detail)?l=i.response.data.detail:i.message&&(l=i.message),uni.showToast({title:l,icon:"none"}),setTimeout(()=>{uni.navigateBack()},2e3)}finally{n.value=!1}}},E=async(e,a)=>{s.value=!0;try{const t=await ae(e,"timeline");if(!t.success||!t.url)throw new Error(t.message||"上传失败");{const e=Y(t.url);if("main"!==a)return e;h.value.image=e,uni.showToast({title:"主图片上传成功",icon:"success",duration:1500})}}catch(i){throw t("error","at pages/culture/timeline-edit.vue:582","上传图片失败:",i),uni.showToast({title:"上传失败，请重试",icon:"none"}),i}finally{s.value=!1}},w=async()=>{if(!s.value)try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"]});e.tempFilePaths&&e.tempFilePaths.length>0&&await E(e.tempFilePaths[0],"main")}catch(e){t("error","at pages/culture/timeline-edit.vue:608","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},N=()=>{w()},f=()=>{h.value.image="",uni.showToast({title:"图片已删除",icon:"success",duration:1e3})},V=e=>{h.value.has_detail=e.detail.value,h.value.has_detail||(h.value.detail="",h.value.detail_images=[])},x=async()=>{if(!s.value)try{const a=await uni.chooseImage({count:9-h.value.detail_images.length,sizeType:["compressed"],sourceType:["album","camera"]});if(a.tempFilePaths&&a.tempFilePaths.length>0){uni.showLoading({title:"上传中..."});try{for(const e of a.tempFilePaths){const t=await E(e,"detail");t&&h.value.detail_images.push(t)}uni.hideLoading(),uni.showToast({title:`成功上传${a.tempFilePaths.length}张图片`,icon:"success",duration:1500})}catch(e){uni.hideLoading(),t("error","at pages/culture/timeline-edit.vue:676","上传详细图片失败:",e)}}}catch(e){t("error","at pages/culture/timeline-edit.vue:680","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},C=()=>{const e=d.value.trim();e&&!h.value.heritage_tags.includes(e)?(h.value.heritage_tags.push(e),d.value="",uni.showToast({title:"标签已添加",icon:"success",duration:1500})):h.value.heritage_tags.includes(e)&&uni.showToast({title:"该标签已存在",icon:"none",duration:1500})},B=()=>{m.value=!0,u.value="",v.value=le;try{uni.hideKeyboard(),setTimeout(()=>{},100)}catch(e){t("log","at pages/culture/timeline-edit.vue:770","处理页面状态时出错:",e)}},b=()=>{m.value=!1,u.value=""},I=()=>{u.value.trim()?v.value=ne(u.value.trim()):v.value=le},D=()=>u.value.trim()?v.value:le,T=e=>{h.value.is_active=e.detail.value},S=async()=>{var e,a;if(h.value.period.trim())if(h.value.year.trim())if(h.value.title.trim())if(h.value.description.trim())if(r.value){n.value=!0,o.value=l.value?"保存中...":"创建中...";try{if(l.value&&c.value){const e={period:h.value.period.trim(),year:h.value.year.trim(),title:h.value.title.trim(),description:h.value.description.trim(),image:h.value.image.trim()||null,has_detail:h.value.has_detail,detail:h.value.detail.trim()||void 0,detail_images:h.value.detail_images.length>0?h.value.detail_images:null,heritage_tags:h.value.heritage_tags.length>0?h.value.heritage_tags:null,is_active:h.value.is_active,sort_order:h.value.sort_order||0};await async function(e,a){try{return await k({url:`/heritage/timeline/${e}`,method:"PUT",data:a})}catch(i){throw t("error","at api/heritage.ts:470","更新时间轴项目失败:",i),i}}(c.value,e),uni.showToast({title:"保存成功",icon:"success"})}else{const e={period:h.value.period.trim(),year:h.value.year.trim(),title:h.value.title.trim(),description:h.value.description.trim(),has_detail:h.value.has_detail,detail:h.value.detail.trim()||void 0,detail_images:h.value.detail_images.length>0?h.value.detail_images:void 0,heritage_tags:h.value.heritage_tags.length>0?h.value.heritage_tags:void 0,is_active:h.value.is_active,sort_order:h.value.sort_order||0};h.value.image&&h.value.image.trim()&&(e.image=h.value.image.trim()),await async function(e,a){try{return await k({url:`/heritage/places/${e}/timeline`,method:"POST",data:a})}catch(i){throw t("error","at api/heritage.ts:452","创建时间轴项目失败:",i),i}}(r.value,e),uni.showToast({title:"创建成功",icon:"success"})}setTimeout(()=>{uni.navigateBack({delta:1})},1500)}catch(i){t("error","at pages/culture/timeline-edit.vue:936","保存时间轴项目失败:",i);let l="保存失败，请重试";(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.detail)?l=i.response.data.detail:i.message&&(l=i.message),uni.showToast({title:l,icon:"none"})}finally{n.value=!1}}else uni.showToast({title:"缺少地点信息",icon:"none"});else uni.showToast({title:"请输入描述",icon:"none"});else uni.showToast({title:"请输入标题",icon:"none"});else uni.showToast({title:"请输入年份范围",icon:"none"});else uni.showToast({title:"请输入时期名称",icon:"none"})};return i({onLoad:e=>{t("log","at pages/culture/timeline-edit.vue:956","🔍 timeline-edit onLoad 接收到的参数:",e),uni.setNavigationBarTitle({title:"历史时间轴编辑"})}}),e.onMounted(()=>{t("log","at pages/culture/timeline-edit.vue:971","🔍 timeline-edit onMounted 开始初始化"),(()=>{const e=getApp();if(e.globalData&&e.globalData.timelineEditData){const a=e.globalData.timelineEditData;t("log","at pages/culture/timeline-edit.vue:399","🔍 timeline-edit 接收到的全局数据:",a),p.value={role:a.role||"",province_name:a.province_name||"",province_id:a.province_id||"",city_name:a.city_name||"",city_id:a.city_id||"",district_name:a.district_name||"",district_id:a.district_id||""},g.value={place_id:parseInt(a.place_id)||0,place_name:a.place_name||""},r.value=g.value.place_id,a.timeline_id&&(l.value=!0,c.value=parseInt(a.timeline_id),t("log","at pages/culture/timeline-edit.vue:425","🔍 使用全局数据填充时间轴表单"),h.value.period=a.period||"",h.value.year=a.year||"",h.value.title=a.title||"",h.value.description=a.description||"",h.value.image=a.image||"",h.value.has_detail=a.has_detail||!1,h.value.detail=a.detail||"",h.value.detail_images=a.detail_images||[],h.value.heritage_tags=a.heritage_tags||[],h.value.is_active=void 0===a.is_active||a.is_active,h.value.sort_order=a.sort_order||0,t("log","at pages/culture/timeline-edit.vue:441","🔍 时间轴表单数据已填充:",h.value))}else{const e=getCurrentPages(),a=e[e.length-1],i=(null==a?void 0:a.options)||{};i.place_id?(p.value={role:i.role||"",province_name:decodeURIComponent(i.province_name||""),province_id:i.province_id||"",city_name:decodeURIComponent(i.city_name||""),city_id:i.city_id||"",district_name:decodeURIComponent(i.district_name||""),district_id:i.district_id||""},g.value={place_id:parseInt(i.place_id)||0,place_name:decodeURIComponent(i.place_name||"")},r.value=g.value.place_id,i.timeline_id&&(l.value=!0,c.value=parseInt(i.timeline_id),_())):(t("error","at pages/culture/timeline-edit.vue:477","未找到参数数据"),uni.showToast({title:"参数加载失败",icon:"none"}),setTimeout(()=>{uni.navigateBack()},1500))}})(),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:"#C8161E"}),uni.setNavigationBarTitle({title:l.value?"编辑历史时间轴":"添加历史时间轴"})}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:e.normalizeClass(["form-container",{"content-hidden":m.value}])},[e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"基本信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},[e.createTextVNode("时期名称 "),e.createElementVNode("text",{class:"required"},"*")]),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[0]||(a[0]=e=>h.value.period=e),placeholder:"请输入时期名称，如：现代、民国、清朝等",maxlength:"100"},null,512),[[e.vModelText,h.value.period]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},[e.createTextVNode("年份范围 "),e.createElementVNode("text",{class:"required"},"*")]),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[1]||(a[1]=e=>h.value.year=e),placeholder:"请输入年份范围，如：1949至今、1912-1949等",maxlength:"100"},null,512),[[e.vModelText,h.value.year]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},[e.createTextVNode("标题 "),e.createElementVNode("text",{class:"required"},"*"),e.createTextVNode(" (当前值: "+e.toDisplayString(h.value.title)+")",1)]),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[2]||(a[2]=e=>h.value.title=e),placeholder:"请输入时间轴项目标题",maxlength:"200"},null,512),[[e.vModelText,h.value.title]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},[e.createTextVNode("描述 "),e.createElementVNode("text",{class:"required"},"*")]),e.withDirectives(e.createElementVNode("textarea",{class:"form-textarea","onUpdate:modelValue":a[3]||(a[3]=e=>h.value.description=e),placeholder:"请输入项目描述，将显示在时间轴卡片上",maxlength:"1000","auto-height":!0,"min-height":120,"show-count":!0},null,512),[[e.vModelText,h.value.description]])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"图片和内容"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"主图片"),e.createElementVNode("view",{class:"image-upload"},[h.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"image-preview"},[e.createElementVNode("image",{src:h.value.image,mode:"aspectFill",class:"preview-image"},null,8,["src"]),e.createElementVNode("view",{class:"image-actions"},[e.createElementVNode("text",{class:"action-btn",onClick:N},"更换"),e.createElementVNode("text",{class:"action-btn delete",onClick:f},"删除")])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(["upload-btn",{disabled:s.value}]),onClick:w},[e.createElementVNode("text",{class:"upload-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"upload-text"},e.toDisplayString(s.value?"上传中":"上传主图片"),1)],2))])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"详细内容"),e.createElementVNode("switch",{checked:h.value.has_detail,onChange:V,color:"#007aff"},null,40,["checked"]),e.createElementVNode("text",{class:"form-desc"},"开启后可添加详细内容和更多图片")]),h.value.has_detail?(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("view",{class:"form-label-with-button"},[e.createElementVNode("text",{class:"form-label"},"详细内容（HTML格式）"),e.createElementVNode("button",{class:"template-btn",onClick:B},"选择模板")]),e.withDirectives(e.createElementVNode("textarea",{class:"form-textarea","onUpdate:modelValue":a[4]||(a[4]=e=>h.value.detail=e),placeholder:"请输入详细内容，支持HTML格式，或点击上方选择模板按钮快速插入模板",maxlength:"10000","auto-height":!0,"min-height":150,"show-count":!0},null,512),[[e.vModelText,h.value.detail]])])):e.createCommentVNode("",!0),h.value.has_detail?(e.openBlock(),e.createElementBlock("view",{key:1,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"详细图片"),e.createElementVNode("view",{class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(h.value.detail_images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"detail-image-item",key:a},[e.createElementVNode("image",{src:t,mode:"aspectFill",class:"detail-image"},null,8,["src"]),e.createElementVNode("view",{class:"detail-image-actions"},[e.createElementVNode("text",{class:"action-btn delete",onClick:e=>(e=>{h.value.detail_images.splice(e,1),uni.showToast({title:"图片已删除",icon:"success",duration:1e3})})(a)},"删除",8,["onClick"])])]))),128)),h.value.detail_images.length<9?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["add-detail-image",{disabled:s.value}]),onClick:x},[e.createElementVNode("text",{class:"add-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"add-text"},e.toDisplayString(s.value?"上传中":"添加图片"),1)],2)):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"文化遗产标签"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"标签选择"),e.createElementVNode("view",{class:"tags-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,t=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["tag-item",{active:h.value.heritage_tags.includes(t)}]),key:t,onClick:e=>(e=>{const t=h.value.heritage_tags.indexOf(e);t>-1?h.value.heritage_tags.splice(t,1):h.value.heritage_tags.push(e)})(t)},e.toDisplayString(t),11,["onClick"]))),128))]),e.createElementVNode("text",{class:"form-desc"},"点击选择相关的文化遗产标签")]),h.value.heritage_tags.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"已选择的标签"),e.createElementVNode("view",{class:"selected-tags-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(h.value.heritage_tags,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"selected-tag-item",key:a},[e.createElementVNode("text",{class:"tag-text"},e.toDisplayString(t),1),e.createElementVNode("text",{class:"remove-tag-btn",onClick:e=>(e=>{if(e>=0&&e<h.value.heritage_tags.length){const t=h.value.heritage_tags[e];h.value.heritage_tags.splice(e,1),uni.showToast({title:`已移除标签: ${t}`,icon:"success",duration:1500})}})(a)},"×",8,["onClick"])]))),128))]),e.createElementVNode("text",{class:"form-desc"},"点击标签右侧的 × 可以移除标签")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"自定义标签"),e.createElementVNode("view",{class:"custom-tag-input"},[e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[5]||(a[5]=e=>d.value=e),placeholder:"输入自定义标签后点击添加",maxlength:"50",onConfirm:C},null,544),[[e.vModelText,d.value]]),e.createElementVNode("button",{class:"add-tag-btn",onClick:C,disabled:!d.value.trim()},"添加",8,["disabled"])]),e.createElementVNode("text",{class:"form-desc"},'添加后的自定义标签会显示在上方"已选择的标签"区域')])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"其他设置"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"启用状态"),e.createElementVNode("switch",{checked:h.value.is_active,onChange:T,color:"#007aff"},null,40,["checked"]),e.createElementVNode("text",{class:"form-desc"},"关闭后，该时间轴项目将不会显示在前端")]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"排序"),e.withDirectives(e.createElementVNode("input",{class:"form-input","onUpdate:modelValue":a[6]||(a[6]=e=>h.value.sort_order=e),placeholder:"数字越小排序越靠前",type:"number"},null,512),[[e.vModelText,h.value.sort_order,void 0,{number:!0}]])])]),e.createElementVNode("view",{class:"save-section"},[e.createElementVNode("button",{class:"save-button",onClick:S,disabled:n.value},e.toDisplayString(n.value?l.value?"保存中...":"创建中...":l.value?"保存":"创建"),9,["disabled"])])],2),m.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"fullscreen-modal"},[e.createElementVNode("view",{class:"template-modal"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},[e.createTextVNode(" 选择HTML模板 "),e.createElementVNode("text",{class:"template-count"},"("+e.toDisplayString(D().length)+"个)",1)]),e.createElementVNode("text",{onClick:b,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"search-section"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>u.value=e),placeholder:"搜索模板...",class:"search-input",onInput:I},null,544),[[e.vModelText,u.value]])]),e.createElementVNode("scroll-view",{class:"template-list","scroll-y":"","enable-back-to-top":"",enhanced:"","show-scrollbar":!0,"scroll-with-animation":!0,"enable-passive":!1},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(D(),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"template-item",key:a,onClick:e=>(e=>{const t=h.value.detail.trim();h.value.detail=t?t+"\n\n"+e.content:e.content,uni.showToast({title:"模板已插入",icon:"success",duration:1e3}),b()})(t)},[e.createElementVNode("text",{class:"template-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"template-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"template-preview"},e.toDisplayString(t.preview),1)],8,["onClick"]))),128)),u.value.trim()&&0===D().length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-results"},[e.createElementVNode("text",{class:"no-results-text"},"未找到相关模板"),e.createElementVNode("text",{class:"no-results-desc"},"请尝试其他关键词")])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"modal-actions"},[e.createElementVNode("button",{onClick:b,class:"cancel-btn"},"取消")])])])):e.createCommentVNode("",!0),n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(o.value),1)])):e.createCommentVNode("",!0)]))}}),ce=e.defineComponent({__name:"heritage-edit",setup(a,{expose:i}){const l=e.ref(!1),n=e.ref(!1),o=e.ref("加载中..."),c=e.ref(null),r=e.ref(null),s=e.ref(!1),d=e.ref(!1),m=e.ref(""),u=e.ref([]),v=e.ref(-1),p=e.ref(["国家级非物质文化遗产","省级非物质文化遗产","市级非物质文化遗产","区县级非物质文化遗产","世界文化遗产","国家重点文物保护单位","省级文物保护单位","市县级文物保护单位","历史文化名城","历史文化街区","传统手工艺","民族文化","古建筑群","文化景观","民俗活动","传统技艺","口传文学","传统音乐","传统舞蹈","传统戏剧","传统体育","传统美术","传统医药","其他"]),g=e.ref({role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),h=e.ref({place_id:"",place_name:""}),y=e.ref({title:"",type:"",brief:"",image:"",detail_content:"",detail_images:[],is_active:!0,sort_order:0});e.onMounted(async()=>{const e=getApp();if(e.globalData&&e.globalData.heritageEditData){const a=e.globalData.heritageEditData;t("log","at pages/culture/heritage-edit.vue:339","🔍 heritage-edit 接收到的全局数据:",a),g.value={role:a.role||"",province_name:a.province_name||"",province_id:a.province_id||"",city_name:a.city_name||"",city_id:a.city_id||"",district_name:a.district_name||"",district_id:a.district_id||""},h.value={place_id:a.place_id||"",place_name:a.place_name||""},r.value=parseInt(h.value.place_id)||null,a.heritage_id&&(l.value=!0,c.value=parseInt(a.heritage_id),t("log","at pages/culture/heritage-edit.vue:366","🔍 使用全局数据填充文化传承表单"),y.value.title=a.title||"",y.value.type=a.type||"",y.value.brief=a.brief||"",y.value.image=a.image||"",y.value.detail_content=a.detail_content||"",y.value.detail_images=a.detail_images||[],y.value.is_active=void 0===a.is_active||a.is_active,y.value.sort_order=a.sort_order||0,t("log","at pages/culture/heritage-edit.vue:379","🔍 文化传承表单数据已填充:",y.value))}else{const e=getCurrentPages(),a=e[e.length-1];null==a||a.options,t("log","at pages/culture/heritage-edit.vue:387","🔍 使用URL参数初始化heritage-edit")}uni.setNavigationBarTitle({title:l.value?"编辑文化遗产":"新增文化遗产"}),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:"#C8161E"})});const _=e=>{v.value=parseInt(e.detail.value),y.value.type=p.value[v.value]},E=e=>{y.value.is_active=e.detail.value},w=async(e,a)=>{s.value=!0;try{const t=await ae(a,"heritage");if(!t.success||!t.url)throw new Error(t.message||"上传失败");{const a=Y(t.url);if("main"!==e)return a;y.value.image=a,uni.showToast({title:"封面图片上传成功",icon:"success",duration:1500})}}catch(i){throw t("error","at pages/culture/heritage-edit.vue:482","上传图片失败:",i),uni.showToast({title:"上传失败，请重试",icon:"none"}),i}finally{s.value=!1}},N=async()=>{if(!s.value)try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"]});e.tempFilePaths&&e.tempFilePaths.length>0&&await w("main",e.tempFilePaths[0])}catch(e){t("error","at pages/culture/heritage-edit.vue:508","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},f=()=>{N()},V=()=>{y.value.image="",uni.showToast({title:"图片已删除",icon:"success",duration:1e3})},x=async()=>{if(!s.value)try{const a=await uni.chooseImage({count:9-y.value.detail_images.length,sizeType:["compressed"],sourceType:["album","camera"]});if(a.tempFilePaths&&a.tempFilePaths.length>0){uni.showLoading({title:"上传中..."});try{for(const e of a.tempFilePaths){const t=await w("detail",e);t&&y.value.detail_images.push(t)}uni.hideLoading(),uni.showToast({title:`成功上传${a.tempFilePaths.length}张图片`,icon:"success",duration:1500})}catch(e){uni.hideLoading(),t("error","at pages/culture/heritage-edit.vue:567","上传详细图片失败:",e)}}}catch(e){t("error","at pages/culture/heritage-edit.vue:571","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},C=async()=>{var e,a;if(y.value.title.trim()?y.value.type.trim()?y.value.brief.trim()||(uni.showToast({title:"请输入简介",icon:"none"}),0):(uni.showToast({title:"请选择遗产类型",icon:"none"}),0):(uni.showToast({title:"请输入遗产名称",icon:"none"}),0))if(r.value)try{n.value=!0,o.value=l.value?"更新中...":"创建中...";const e=y.value.detail_content.trim(),a=""===e?void 0:e,i={title:y.value.title.trim(),type:y.value.type,brief:y.value.brief.trim(),image:y.value.image&&y.value.image.trim()?y.value.image.trim():void 0,detail_content:a,detail_images:y.value.detail_images.length>0?y.value.detail_images:void 0,is_active:y.value.is_active,sort_order:y.value.sort_order};l.value&&c.value?(await async function(e,a){try{return await k({url:`/heritage/heritage/${e}`,method:"PUT",data:a})}catch(i){throw t("error","at api/heritage.ts:566","更新文化遗产项目失败:",i),i}}(c.value,i),uni.showToast({title:"更新成功",icon:"success"})):(await async function(e,a){try{return await k({url:`/heritage/places/${e}/heritage`,method:"POST",data:a})}catch(i){throw t("error","at api/heritage.ts:548","创建文化遗产项目失败:",i),i}}(r.value,i),uni.showToast({title:"创建成功",icon:"success"})),setTimeout(()=>{uni.navigateBack()},1500)}catch(i){t("error","at pages/culture/heritage-edit.vue:679","保存文化遗产失败:",i);let l="保存失败，请重试";(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.detail)?l=i.response.data.detail:i.message&&(l=i.message),uni.showToast({title:l,icon:"none"})}finally{n.value=!1}else uni.showToast({title:"缺少地点信息",icon:"none"})},B=()=>{d.value=!0,m.value="",u.value=le;try{uni.hideKeyboard(),setTimeout(()=>{},100)}catch(e){t("log","at pages/culture/heritage-edit.vue:724","处理页面状态时出错:",e)}},b=()=>{d.value=!1,m.value=""},I=()=>{m.value.trim()?u.value=ne(m.value.trim()):u.value=le},D=()=>m.value.trim()?u.value:le,T=()=>{uni.showModal({title:"确认清空",content:"确定要清空详细内容吗？此操作不可恢复。",success:e=>{e.confirm&&(y.value.detail_content="",uni.showToast({title:"内容已清空",icon:"success",duration:1e3}))}})};return i({onLoad:e=>{t("log","at pages/culture/heritage-edit.vue:324","🔍 heritage-edit onLoad 接收到的参数:",e),uni.setNavigationBarTitle({title:"文化传承编辑"})}}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-wrapper"},[e.createElementVNode("view",{class:"container"},[e.createElementVNode("view",{class:e.normalizeClass(["form-container",{"content-hidden":d.value}])},[e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"基本信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"遗产名称 (当前值: "+e.toDisplayString(y.value.title)+")",1),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>y.value.title=e),class:"form-input",placeholder:"请输入遗产名称",maxlength:"200"},null,512),[[e.vModelText,y.value.title]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"遗产类型"),e.createElementVNode("picker",{value:v.value,range:p.value,onChange:_},[e.createElementVNode("view",{class:"form-input"},e.toDisplayString(y.value.type||"请选择遗产类型"),1)],40,["value","range"])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"简介"),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":a[1]||(a[1]=e=>y.value.brief=e),class:"form-textarea",placeholder:"请输入简介",maxlength:"1000","auto-height":!0,"min-height":120,"show-count":!0},null,512),[[e.vModelText,y.value.brief]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"封面图片"),e.createElementVNode("view",{class:"image-upload"},[y.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"image-preview"},[e.createElementVNode("image",{src:y.value.image,mode:"aspectFill",class:"preview-image"},null,8,["src"]),e.createElementVNode("view",{class:"image-actions"},[e.createElementVNode("text",{class:"action-btn",onClick:f},"更换"),e.createElementVNode("text",{class:"action-btn delete",onClick:V},"删除")])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(["upload-btn",{disabled:s.value}]),onClick:N},[e.createElementVNode("text",{class:"upload-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"upload-text"},e.toDisplayString(s.value?"上传中":"上传封面图片"),1)],2))])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"详细信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("view",{class:"form-label-with-button"},[e.createElementVNode("text",{class:"form-label"},"详细内容（HTML格式）"),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("button",{class:"template-btn",onClick:B},"选择模板"),y.value.detail_content.trim()?(e.openBlock(),e.createElementBlock("button",{key:0,class:"clear-btn",onClick:T},"清空")):e.createCommentVNode("",!0)])]),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":a[2]||(a[2]=e=>y.value.detail_content=e),class:"form-textarea",placeholder:"请输入详细内容，支持HTML格式，或点击上方选择模板按钮快速插入模板",maxlength:"10000","auto-height":!0,"min-height":150,"show-count":!0},null,512),[[e.vModelText,y.value.detail_content]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"详细图片"),e.createElementVNode("view",{class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value.detail_images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"detail-image-item",key:a},[e.createElementVNode("image",{src:t,mode:"aspectFill",class:"detail-image"},null,8,["src"]),e.createElementVNode("view",{class:"detail-image-actions"},[e.createElementVNode("text",{class:"action-btn delete",onClick:e=>(e=>{y.value.detail_images.splice(e,1),uni.showToast({title:"图片已删除",icon:"success",duration:1e3})})(a)},"删除",8,["onClick"])])]))),128)),y.value.detail_images.length<9?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["add-detail-image",{disabled:s.value}]),onClick:x},[e.createElementVNode("text",{class:"add-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"add-text"},e.toDisplayString(s.value?"上传中":"添加图片"),1)],2)):e.createCommentVNode("",!0)])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"设置"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("view",{class:"switch-item"},[e.createElementVNode("text",{class:"switch-label"},"启用状态"),e.createElementVNode("switch",{checked:y.value.is_active,onChange:E},null,40,["checked"])])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"排序"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>y.value.sort_order=e),class:"form-input",type:"number",placeholder:"数值越大排序越靠前"},null,512),[[e.vModelText,y.value.sort_order,void 0,{number:!0}]])])])],2),e.createElementVNode("view",{class:"save-section"},[e.createElementVNode("button",{class:"save-button",onClick:C,disabled:n.value},e.toDisplayString(n.value?l.value?"保存中...":"创建中...":l.value?"保存":"创建"),9,["disabled"])])]),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"fullscreen-modal"},[e.createElementVNode("view",{class:"template-modal"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},[e.createTextVNode(" 选择HTML模板 "),e.createElementVNode("text",{class:"template-count"},"("+e.toDisplayString(D().length)+"个)",1)]),e.createElementVNode("text",{onClick:b,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"search-section"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[4]||(a[4]=e=>m.value=e),placeholder:"搜索模板...",class:"search-input",onInput:I},null,544),[[e.vModelText,m.value]])]),e.createElementVNode("scroll-view",{class:"template-list","scroll-y":"","enable-back-to-top":"",enhanced:"","show-scrollbar":!0,"scroll-with-animation":!0,"enable-passive":!1},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(D(),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"template-item",key:a,onClick:e=>(e=>{const t=y.value.detail_content.trim();y.value.detail_content=t?t+"\n\n"+e.content:e.content,uni.showToast({title:"模板已插入",icon:"success",duration:1e3}),b()})(t)},[e.createElementVNode("text",{class:"template-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"template-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"template-preview"},e.toDisplayString(t.preview),1)],8,["onClick"]))),128)),m.value.trim()&&0===D().length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-results"},[e.createElementVNode("text",{class:"no-results-text"},"未找到相关模板"),e.createElementVNode("text",{class:"no-results-desc"},"请尝试其他关键词")])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"modal-actions"},[e.createElementVNode("button",{onClick:b,class:"cancel-btn"},"取消")])])])):e.createCommentVNode("",!0),n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(o.value),1)])):e.createCommentVNode("",!0)]))}}),re=S(e.defineComponent({__name:"heritage-detail",setup(a){const i=e.ref(!0),l=e.ref("加载中..."),n=e.ref(null),o=e.ref(null);e.onMounted(async()=>{var e;const t=getCurrentPages(),a=t[t.length-1],l=(null==a?void 0:a.options)||{};l.heritage_id?(o.value=parseInt(l.heritage_id),await c()):(i.value=!1,uni.showToast({title:"缺少遗产ID",icon:"none"})),uni.setNavigationBarTitle({title:(null==(e=n.value)?void 0:e.title)||"文化遗产详情"})});const c=async()=>{var e,a;if(o.value)try{i.value=!0,l.value="加载中...";const e=await async function(e){try{return await k({url:`/heritage/heritage/${e}`,method:"GET"})}catch(a){throw t("error","at api/heritage.ts:583","获取文化遗产项目失败:",a),a}}(o.value);e?(n.value=e,uni.setNavigationBarTitle({title:e.title})):uni.showToast({title:"获取数据失败",icon:"none"})}catch(c){t("error","at pages/culture/heritage-detail.vue:145","加载文化遗产详情失败:",c);let i="加载失败，请重试";(null==(a=null==(e=c.response)?void 0:e.data)?void 0:a.detail)?i=c.response.data.detail:c.message&&(i=c.message),uni.showToast({title:i,icon:"none"})}finally{i.value=!1}else uni.showToast({title:"缺少遗产ID",icon:"none"})};return(t,a)=>{return e.openBlock(),e.createElementBlock("view",{class:"heritage-detail-container"},[i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(l.value),1)])):n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-content"},[n.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"header-image-container"},[e.createElementVNode("image",{src:n.value.image,mode:"aspectFill",class:"header-image"},null,8,["src"]),e.createElementVNode("view",{class:"image-overlay"},[e.createElementVNode("text",{class:"heritage-title"},e.toDisplayString(n.value.title),1),e.createElementVNode("text",{class:"heritage-type"},e.toDisplayString(n.value.type),1)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"info-section"},[n.value.image?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"section-title"},[e.createElementVNode("text",{class:"title-text"},e.toDisplayString(n.value.title),1),e.createElementVNode("text",{class:"type-text"},e.toDisplayString(n.value.type),1)])),e.createElementVNode("view",{class:"brief-content"},[e.createElementVNode("text",{class:"brief-text"},e.toDisplayString(n.value.brief),1)])]),n.value.detail_content?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title-text"},"详细介绍")]),e.createElementVNode("view",{class:"detail-text-content"},[e.createElementVNode("rich-text",{nodes:n.value.detail_content},null,8,["nodes"])])])):e.createCommentVNode("",!0),n.value.detail_images&&n.value.detail_images.length>0?(e.openBlock(),e.createElementBlock("view",{key:2,class:"images-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title-text"},"相关图片")]),e.createElementVNode("view",{class:"images-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.detail_images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"image-item",key:a,onClick:e=>{return t=n.value.detail_images,i=a,void uni.previewImage({urls:t,current:i});var t,i}},[e.createElementVNode("image",{src:t,mode:"aspectFill",class:"grid-image"},null,8,["src"])],8,["onClick"]))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"footer-info"},[e.createElementVNode("text",{class:"update-time"},"最后更新："+e.toDisplayString((o=n.value.updated_at,new Date(o).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}))),1)])])):(e.openBlock(),e.createElementBlock("view",{key:2,class:"error-container"},[e.createElementVNode("text",{class:"error-text"},"内容加载失败"),e.createElementVNode("button",{class:"retry-btn",onClick:c},"重试")]))]);var o}}}),[["__scopeId","data-v-87468cc8"]]),se=e.defineComponent({__name:"heritage-content",setup(a){const i=e.ref(!0),l=e.ref([]),n=e.ref(""),o=e.ref(""),c=e.ref(`${y().replace("/api/","")}/static/image/chuancheng.png`),r=e.ref(""),s=e.ref("");e.onMounted(async()=>{await d()});const d=async()=>{var e,a,d,u,v;i.value=!0;try{const t=T.currentRegionIds;if(t.provinceId){const i=await V({province_id:t.provinceId,city_id:t.cityId,district_id:t.districtId});i&&(n.value=(null==(e=i.place_info)?void 0:e.place_name)||T.fullRegionName||"文化传承",o.value=(null==(a=i.place_info)?void 0:a.place_desc)||"探索传统文化之美",c.value=(null==(d=i.place_info)?void 0:d.header_bg_image)?Y(i.place_info.header_bg_image):`${y().replace("/api/","")}/static/image/chuancheng.png`,r.value=(null==(u=i.place_info)?void 0:u.introduction)||"",s.value=(null==(v=i.place_info)?void 0:v.footer_text)||"",i.heritage_data&&i.heritage_data.length>0&&(l.value=i.heritage_data.sort((e,t)=>(e.sort_order||0)-(t.sort_order||0))))}}catch(p){t("error","at pages/culture/heritage-content.vue:144","获取区域文化遗产数据失败:",p)}finally{i.value=!1,setTimeout(()=>{m()},500)}},m=()=>{t("log","at pages/culture/heritage-content.vue:158","水墨动画已启动")},u=e=>{const t=e.target||e.currentTarget;t&&(t.src="/static/images/no-image.svg")};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"heritage-content-page"},[e.createElementVNode("view",{class:"cover-section"},[e.createElementVNode("image",{class:"cover-image",src:c.value,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"cover-overlay"}),e.createElementVNode("view",{class:"cover-content"},[e.createElementVNode("text",{class:"place-name"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"place-desc"},e.toDisplayString(o.value),1)])]),r.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"intro-section"},[e.createElementVNode("text",{class:"intro-text"},e.toDisplayString(r.value),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modern-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"当代文化传承")]),e.createElementVNode("view",{class:"heritage-list"},[l.value&&0!==l.value.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},"暂无文化传承数据")])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,(t,a)=>{return e.openBlock(),e.createElementBlock("view",{class:"heritage-item",key:a,onClick:e=>{return a=t.id,void uni.navigateTo({url:`/pages/culture/heritage-detail?heritage_id=${a}`});var a},style:e.normalizeStyle({animationDelay:.2*a+"s"})},[e.createElementVNode("image",{src:(i=t.image,X(i)),mode:"aspectFill",class:"heritage-image",onError:u},null,40,["src"]),e.createElementVNode("view",{class:"heritage-info"},[e.createElementVNode("text",{class:"heritage-title"},e.toDisplayString(t.title),1),e.createElementVNode("text",{class:"heritage-type"},e.toDisplayString(t.type),1),e.createElementVNode("text",{class:"heritage-brief"},e.toDisplayString(t.brief),1)]),e.createElementVNode("view",{class:"ink-animation"},[e.createElementVNode("view",{class:"ink-drop"})])],12,["onClick"]);var i}),128))])]),s.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"footer"},[e.createElementVNode("text",{class:"footer-text"},e.toDisplayString(s.value),1)])):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"loading-container"},[e.createElementVNode("view",{class:"ink-loading"},[e.createElementVNode("view",{class:"ink-circle"}),e.createElementVNode("view",{class:"ink-splash"})]),e.createElementVNode("text",{class:"loading-text"},"加载中...")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"animated-bg"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{class:"ink-wash",key:t})),64))])]))}}),de=e.defineComponent({__name:"memory-edit",setup(a,{expose:i}){const l=e.ref(!1),n=e.ref(!1),o=e.ref("加载中..."),c=e.ref(null),r=e.ref(null),s=e.ref(!1),d=e.ref(!1),m=e.ref(""),u=e.ref([]),v=e.ref({role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:""}),p=e.ref({place_id:"",place_name:""}),g=e.ref({title:"",year:"",image:"",description:"",detail_content:"",detail_images:[],is_active:!0,sort_order:0});e.onMounted(async()=>{const e=getApp();if(e.globalData&&e.globalData.memoryEditData){const a=e.globalData.memoryEditData;t("log","at pages/culture/memory-edit.vue:307","🔍 memory-edit 接收到的全局数据:",a),v.value={role:a.role||"",province_name:a.province_name||"",province_id:a.province_id||"",city_name:a.city_name||"",city_id:a.city_id||"",district_name:a.district_name||"",district_id:a.district_id||""},p.value={place_id:a.place_id||"",place_name:a.place_name||""},r.value=parseInt(p.value.place_id)||null,a.memory_id&&(l.value=!0,c.value=parseInt(a.memory_id),t("log","at pages/culture/memory-edit.vue:334","🔍 使用全局数据填充当代记忆表单"),g.value.title=a.title||"",g.value.year=a.year||"",g.value.image=a.image||"",g.value.description=a.description||"",g.value.detail_content=a.detail_content||"",g.value.detail_images=a.detail_images||[],g.value.is_active=void 0===a.is_active||a.is_active,g.value.sort_order=a.sort_order||0,t("log","at pages/culture/memory-edit.vue:347","🔍 当代记忆表单数据已填充:",g.value))}else{const e=getCurrentPages(),a=e[e.length-1];null==a||a.options,t("log","at pages/culture/memory-edit.vue:355","🔍 使用URL参数初始化memory-edit")}uni.setNavigationBarTitle({title:l.value?"编辑城市记忆":"新增城市记忆"}),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:"#C8161E"})});const h=e=>{g.value.is_active=e.detail.value},y=async(e,a)=>{s.value=!0;try{const t=await ae(e,"memory");if(!t.success||!t.url)throw new Error(t.message||"上传失败");{const e=Y(t.url);if("main"!==a)return e;g.value.image=e,uni.showToast({title:"封面图片上传成功",icon:"success",duration:1500})}}catch(i){throw t("error","at pages/culture/memory-edit.vue:440","上传图片失败:",i),uni.showToast({title:"上传失败，请重试",icon:"none"}),i}finally{s.value=!1}},_=async()=>{if(!s.value)try{const e=await uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"]});e.tempFilePaths&&e.tempFilePaths.length>0&&await y(e.tempFilePaths[0],"main")}catch(e){t("error","at pages/culture/memory-edit.vue:466","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},E=()=>{_()},w=()=>{g.value.image="",uni.showToast({title:"图片已删除",icon:"success",duration:1e3})},N=async()=>{if(!s.value)try{const a=await uni.chooseImage({count:9-g.value.detail_images.length,sizeType:["compressed"],sourceType:["album","camera"]});if(a.tempFilePaths&&a.tempFilePaths.length>0){uni.showLoading({title:"上传中..."});try{for(const e of a.tempFilePaths){const t=await y(e,"detail");t&&g.value.detail_images.push(t)}uni.hideLoading(),uni.showToast({title:`成功上传${a.tempFilePaths.length}张图片`,icon:"success",duration:1500})}catch(e){uni.hideLoading(),t("error","at pages/culture/memory-edit.vue:525","上传详细图片失败:",e)}}}catch(e){t("error","at pages/culture/memory-edit.vue:529","选择图片失败:",e),uni.showToast({title:"选择图片失败",icon:"none"})}},f=()=>{d.value=!0,m.value="",u.value=le;try{uni.hideKeyboard(),setTimeout(()=>{},100)}catch(e){t("log","at pages/culture/memory-edit.vue:597","处理页面状态时出错:",e)}},V=()=>{d.value=!1,m.value=""},x=()=>{m.value.trim()?u.value=ne(m.value.trim()):u.value=le},C=()=>m.value.trim()?u.value:le,B=()=>{uni.showModal({title:"确认清空",content:"确定要清空详细内容吗？此操作不可恢复。",success:e=>{e.confirm&&(g.value.detail_content="",uni.showToast({title:"内容已清空",icon:"success",duration:1e3}))}})},b=async()=>{var e,a;if(g.value.title.trim()?g.value.year.trim()||(uni.showToast({title:"请输入年份",icon:"none"}),0):(uni.showToast({title:"请输入记忆标题",icon:"none"}),0))if(r.value)try{n.value=!0,o.value=l.value?"更新中...":"创建中...";const e={title:g.value.title.trim(),year:g.value.year.trim(),image:g.value.image&&g.value.image.trim()?g.value.image.trim():void 0,description:g.value.description&&g.value.description.trim()?g.value.description.trim():void 0,detail_content:g.value.detail_content&&g.value.detail_content.trim()?g.value.detail_content.trim():void 0,detail_images:g.value.detail_images.length>0?g.value.detail_images:void 0,is_active:g.value.is_active,sort_order:g.value.sort_order};l.value&&c.value?(await async function(e,a){try{return await k({url:`/heritage/memory/${e}`,method:"PUT",data:a})}catch(i){return t("error","at api/heritage.ts:692","更新城市记忆项目失败:",i),null}}(c.value,e),uni.showToast({title:"更新成功",icon:"success"})):(await async function(e,a){try{return await k({url:`/heritage/places/${e}/memory`,method:"POST",data:a})}catch(i){return t("error","at api/heritage.ts:675","创建城市记忆项目失败:",i),null}}(r.value,e),uni.showToast({title:"创建成功",icon:"success"})),setTimeout(()=>{uni.navigateBack()},1500)}catch(i){t("error","at pages/culture/memory-edit.vue:718","保存城市记忆失败:",i);let l="保存失败，请重试";(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.detail)?l=i.response.data.detail:i.message&&(l=i.message),uni.showToast({title:l,icon:"none"})}finally{n.value=!1}else uni.showToast({title:"缺少地点信息",icon:"none"})};return i({onLoad:e=>{t("log","at pages/culture/memory-edit.vue:292","🔍 memory-edit onLoad 接收到的参数:",e),uni.setNavigationBarTitle({title:"当代记忆编辑"})}}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"page-wrapper"},[e.createElementVNode("view",{class:"container"},[e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"基本信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"记忆标题 (当前值: "+e.toDisplayString(g.value.title)+")",1),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>g.value.title=e),class:"form-input",placeholder:"请输入记忆标题",maxlength:"200"},null,512),[[e.vModelText,g.value.title]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"年份"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>g.value.year=e),class:"form-input",placeholder:"请输入年份，如：2020、2010年代",maxlength:"50"},null,512),[[e.vModelText,g.value.year]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"简要描述"),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":a[2]||(a[2]=e=>g.value.description=e),class:"form-textarea",placeholder:"请输入简要描述",maxlength:"1000","auto-height":!0,"min-height":120,"show-count":!0},null,512),[[e.vModelText,g.value.description]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"封面图片"),e.createElementVNode("view",{class:"image-upload"},[g.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"image-preview"},[e.createElementVNode("image",{src:g.value.image,mode:"aspectFill",class:"preview-image"},null,8,["src"]),e.createElementVNode("view",{class:"image-actions"},[e.createElementVNode("text",{class:"action-btn",onClick:E},"更换"),e.createElementVNode("text",{class:"action-btn delete",onClick:w},"删除")])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(["upload-btn",{disabled:s.value}]),onClick:_},[e.createElementVNode("text",{class:"upload-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"upload-text"},e.toDisplayString(s.value?"上传中":"上传封面图片"),1)],2))])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"详细信息"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("view",{class:"form-label-with-button"},[e.createElementVNode("text",{class:"form-label"},"详细内容（HTML格式）"),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("button",{class:"template-btn",onClick:f},"选择模板"),g.value.detail_content&&g.value.detail_content.trim()?(e.openBlock(),e.createElementBlock("button",{key:0,class:"clear-btn",onClick:B},"清空")):e.createCommentVNode("",!0)])]),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":a[3]||(a[3]=e=>g.value.detail_content=e),class:"form-textarea",placeholder:"请输入详细内容，支持HTML格式，或点击上方选择模板按钮快速插入模板",maxlength:"10000","auto-height":!0,"min-height":150,"show-count":!0},null,512),[[e.vModelText,g.value.detail_content]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"详细图片"),e.createElementVNode("view",{class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value.detail_images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"detail-image-item",key:a},[e.createElementVNode("image",{src:t,mode:"aspectFill",class:"detail-image"},null,8,["src"]),e.createElementVNode("view",{class:"detail-image-actions"},[e.createElementVNode("text",{class:"action-btn delete",onClick:e=>(e=>{g.value.detail_images.splice(e,1),uni.showToast({title:"图片已删除",icon:"success",duration:1e3})})(a)},"删除",8,["onClick"])])]))),128)),g.value.detail_images.length<9?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["add-detail-image",{disabled:s.value}]),onClick:N},[e.createElementVNode("text",{class:"add-icon"},e.toDisplayString(s.value?"...":"+"),1),e.createElementVNode("text",{class:"add-text"},e.toDisplayString(s.value?"上传中":"添加图片"),1)],2)):e.createCommentVNode("",!0)])])]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"section-title"},"设置"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("view",{class:"switch-item"},[e.createElementVNode("text",{class:"switch-label"},"启用状态"),e.createElementVNode("switch",{checked:g.value.is_active,onChange:h},null,40,["checked"])])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"排序"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[4]||(a[4]=e=>g.value.sort_order=e),class:"form-input",type:"number",placeholder:"数值越大排序越靠前"},null,512),[[e.vModelText,g.value.sort_order,void 0,{number:!0}]])])])]),e.createElementVNode("view",{class:"save-section"},[e.createElementVNode("button",{class:"save-button",onClick:b,disabled:n.value},e.toDisplayString(n.value?l.value?"保存中...":"创建中...":l.value?"保存":"创建"),9,["disabled"])])]),d.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"fullscreen-modal"},[e.createElementVNode("view",{class:"template-modal"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},[e.createTextVNode(" 选择HTML模板 "),e.createElementVNode("text",{class:"template-count"},"("+e.toDisplayString(C().length)+"个)",1)]),e.createElementVNode("text",{onClick:V,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"search-section"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>m.value=e),placeholder:"搜索模板...",class:"search-input",onInput:x},null,544),[[e.vModelText,m.value]])]),e.createElementVNode("scroll-view",{class:"template-list","scroll-y":"","enable-back-to-top":"",enhanced:"","show-scrollbar":!0,"scroll-with-animation":!0,"enable-passive":!1},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(C(),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"template-item",key:a,onClick:e=>(e=>{const t=g.value.detail_content.trim();g.value.detail_content=t?t+"\n\n"+e.content:e.content,uni.showToast({title:"模板已插入",icon:"success",duration:1e3}),V()})(t)},[e.createElementVNode("text",{class:"template-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"template-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"template-preview"},e.toDisplayString(t.preview),1)],8,["onClick"]))),128)),m.value.trim()&&0===C().length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-results"},[e.createElementVNode("text",{class:"no-results-text"},"未找到相关模板"),e.createElementVNode("text",{class:"no-results-desc"},"请尝试其他关键词")])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"modal-actions"},[e.createElementVNode("button",{onClick:V,class:"cancel-btn"},"取消")])])])):e.createCommentVNode("",!0),n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(o.value),1)])):e.createCommentVNode("",!0)]))}}),me=S(e.defineComponent({__name:"memory-detail",setup(a){const i=e.ref(!0),l=e.ref("加载中..."),n=e.ref(null),o=e.ref(null);e.onMounted(async()=>{var e;const t=getCurrentPages(),a=t[t.length-1],l=(null==a?void 0:a.options)||{};l.memory_id?(o.value=parseInt(l.memory_id),await c()):(i.value=!1,uni.showToast({title:"缺少记忆ID",icon:"none"})),uni.setNavigationBarTitle({title:(null==(e=n.value)?void 0:e.title)||"城市记忆详情"})});const c=async()=>{var e,a;if(o.value)try{i.value=!0,l.value="加载中...";const e=await async function(e){try{return await k({url:`/heritage/memory/${e}`,method:"GET"})}catch(a){return t("error","at api/heritage.ts:708","获取城市记忆项目详情失败:",a),null}}(o.value);e?(n.value=e,uni.setNavigationBarTitle({title:e.title})):uni.showToast({title:"获取数据失败",icon:"none"})}catch(c){t("error","at pages/culture/memory-detail.vue:146","加载城市记忆详情失败:",c);let i="加载失败，请重试";(null==(a=null==(e=c.response)?void 0:e.data)?void 0:a.detail)?i=c.response.data.detail:c.message&&(i=c.message),uni.showToast({title:i,icon:"none"})}finally{i.value=!1}else uni.showToast({title:"缺少记忆ID",icon:"none"})};return(t,a)=>{return e.openBlock(),e.createElementBlock("view",{class:"memory-detail-container"},[i.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(l.value),1)])):n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-content"},[n.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"header-image-container"},[e.createElementVNode("image",{src:n.value.image,mode:"aspectFill",class:"header-image"},null,8,["src"]),e.createElementVNode("view",{class:"image-overlay"},[e.createElementVNode("text",{class:"memory-title"},e.toDisplayString(n.value.title),1),e.createElementVNode("text",{class:"memory-year"},e.toDisplayString(n.value.year),1)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"info-section"},[n.value.image?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"section-title"},[e.createElementVNode("text",{class:"title-text"},e.toDisplayString(n.value.title),1),e.createElementVNode("text",{class:"year-text"},e.toDisplayString(n.value.year),1)])),n.value.description?(e.openBlock(),e.createElementBlock("view",{key:1,class:"description-content"},[e.createElementVNode("text",{class:"description-text"},e.toDisplayString(n.value.description),1)])):e.createCommentVNode("",!0)]),n.value.detail_content?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-content-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title-text"},"详细内容")]),e.createElementVNode("view",{class:"detail-content-container"},[e.createElementVNode("rich-text",{nodes:n.value.detail_content},null,8,["nodes"])])])):e.createCommentVNode("",!0),n.value.detail_images&&n.value.detail_images.length>0?(e.openBlock(),e.createElementBlock("view",{key:2,class:"images-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title-text"},"相关图片")]),e.createElementVNode("view",{class:"images-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.detail_images,(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"image-item",key:a,onClick:e=>{return t=n.value.detail_images,i=a,void uni.previewImage({urls:t,current:i});var t,i}},[e.createElementVNode("image",{src:t,mode:"aspectFill",class:"grid-image"},null,8,["src"])],8,["onClick"]))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"footer-info"},[e.createElementVNode("text",{class:"update-time"},"最后更新："+e.toDisplayString((o=n.value.updated_at,new Date(o).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}))),1)])])):(e.openBlock(),e.createElementBlock("view",{key:2,class:"error-container"},[e.createElementVNode("text",{class:"error-text"},"内容加载失败"),e.createElementVNode("button",{class:"retry-btn",onClick:c},"重试")]))]);var o}}}),[["__scopeId","data-v-3c04d2ca"]]),ue=e.defineComponent({__name:"memory-content",setup(a){const i=e.ref(!0),l=e.ref([]),n=e.ref(""),o=e.ref(""),c=e.ref(`${y().replace("/api/","")}/static/image/memory.jpg`),r=e.ref(""),s=e.ref("");e.onMounted(async()=>{await d()});const d=async()=>{var e,a,d,u,v;i.value=!0;try{const t=T.currentRegionIds;if(t.provinceId){const i=await V({province_id:t.provinceId,city_id:t.cityId,district_id:t.districtId});i&&(n.value=(null==(e=i.place_info)?void 0:e.place_name)||T.fullRegionName||"城市记忆",o.value=(null==(a=i.place_info)?void 0:a.place_desc)||"城市变迁的影像档案",c.value=(null==(d=i.place_info)?void 0:d.header_bg_image)?Y(i.place_info.header_bg_image):`${y().replace("/api","")}/static/image/memory.jpg`,r.value=(null==(u=i.place_info)?void 0:u.introduction)||"城市记忆是城市的灵魂，记录着这座城市的变迁、成长和故事。通过图像、文字和影音，我们共同守护这座城市的集体回忆。",s.value=(null==(v=i.place_info)?void 0:v.footer_text)||"",i.memory_data&&i.memory_data.length>0&&(l.value=i.memory_data.sort((e,t)=>(e.sort_order||0)-(t.sort_order||0))))}}catch(p){t("error","at pages/culture/memory-content.vue:164","获取区域城市记忆数据失败:",p)}finally{i.value=!1,setTimeout(()=>{m()},500)}},m=()=>{t("log","at pages/culture/memory-content.vue:178","记忆动画已启动")},u=e=>{const t=e.target||e.currentTarget;t&&(t.src="/static/images/no-image.svg")};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"memory-content-page"},[e.createElementVNode("view",{class:"cover-section"},[e.createElementVNode("image",{class:"cover-image",src:c.value,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"cover-overlay"}),e.createElementVNode("view",{class:"cover-content"},[e.createElementVNode("text",{class:"place-name"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"place-desc"},e.toDisplayString(o.value),1)])]),r.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"intro-section"},[e.createElementVNode("text",{class:"intro-text"},e.toDisplayString(r.value),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"timeline-decoration"},[e.createElementVNode("view",{class:"timeline-line"}),e.createElementVNode("view",{class:"time-node"})]),e.createElementVNode("view",{class:"memory-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"当代城市记忆")]),e.createElementVNode("view",{class:"memory-timeline"},[l.value&&0!==l.value.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createElementVNode("text",{class:"empty-text"},"暂无城市记忆数据")])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,(t,a)=>{return e.openBlock(),e.createElementBlock("view",{key:a,class:"memory-item",style:e.normalizeStyle({animationDelay:.2*a+"s"}),onClick:e=>{return a=t.id,void uni.navigateTo({url:`/pages/culture/memory-detail?memory_id=${a}`});var a}},[e.createElementVNode("view",{class:"time-marker"},[e.createElementVNode("view",{class:"year-dot"}),a!==l.value.length-1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"year-line"})):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"memory-card"},[e.createElementVNode("view",{class:"memory-year"},e.toDisplayString(t.year),1),e.createElementVNode("view",{class:"memory-content"},[e.createElementVNode("image",{src:(i=t.image,i?Y(i):"/static/images/no-image.svg"),mode:"aspectFill",class:"memory-image",onError:u},null,40,["src"]),e.createElementVNode("view",{class:"memory-info"},[e.createElementVNode("text",{class:"memory-title"},e.toDisplayString(t.title),1),e.createElementVNode("text",{class:"memory-desc"},e.toDisplayString(t.description||"点击查看详情"),1)])]),e.createElementVNode("view",{class:"ink-effect"})])],12,["onClick"]);var i}),128))])]),s.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"footer"},[e.createElementVNode("text",{class:"footer-text"},e.toDisplayString(s.value),1)])):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"loading-container"},[e.createElementVNode("view",{class:"photo-loading"},[e.createElementVNode("view",{class:"photo-frame"}),e.createElementVNode("view",{class:"photo-develop"})]),e.createElementVNode("text",{class:"loading-text"},"记忆加载中...")])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"animated-elements"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(3,t=>e.createElementVNode("view",{class:"floating-photo",key:t})),64)),(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,t=>e.createElementVNode("view",{class:"ink-wash",key:`ink-${t}`})),64))])]))}}),ve=S(e.defineComponent({__name:"index",setup(t){const a="super_admin",i="province_admin",l="city_admin",n="district_admin",o=e.ref({id:"",role:"",province_name:"",province_id:"",city_name:"",city_id:"",district_name:"",district_id:"",user_id:"",module_permissions:{ancient_books:!1,paintings:!1,archives:!1,videos:!1}}),c=e.ref("用户"),s=e.ref(0),d=e=>{switch(e){case a:return"超级管理员";case i:return"省级管理员";case l:return"市级管理员";case n:return"区县管理员";default:return"用户"}},m={get currentUser(){return o.value},get userRoleDisplayName(){return c.value}};r(e=>{e&&e.role?(o.value={id:e.user_id||"",role:e.role||"",province_name:decodeURIComponent(e.province_name||""),province_id:e.province_id||"",city_name:decodeURIComponent(e.city_name||""),city_id:e.city_id||"",district_name:decodeURIComponent(e.district_name||""),district_id:e.district_id||"",user_id:e.user_id||"",module_permissions:{ancient_books:"super_admin"===e.role||"true"===e.ancient_books_permission,paintings:"super_admin"===e.role||"true"===e.paintings_permission,archives:"super_admin"===e.role||"true"===e.archives_permission,videos:"super_admin"===e.role||"true"===e.videos_permission}},c.value=d(o.value.role)):(o.value={id:"test_user",role:l,province_name:"河南省",province_id:"410000",city_name:"郑州市",city_id:"410100",district_name:"",district_id:"",user_id:"test_user",module_permissions:{ancient_books:!0,paintings:!1,archives:!1,videos:!1}},c.value=d(o.value.role))}),e.onMounted(()=>{const e=uni.getSystemInfoSync();s.value=e.statusBarHeight||0});const u=()=>{const e=m.currentUser;if(!e)return"用户";const t=m.userRoleDisplayName,o=(()=>{const e=m.currentUser;return e?e.role===a?"全国":e.role===i?e.province_name||"省级":e.role===l?`${e.province_name||""} ${e.city_name||""}`.trim()||"市级":e.role===n?`${e.province_name||""} ${e.city_name||""} ${e.district_name||""}`.trim()||"区县级":"":""})();return e.role===a||o?`${t}（${o}）`:t},v=()=>{uni.showToast({title:"功能开发中",icon:"none"})},p=()=>{const e=o.value,t={manage_mode:"true",role:e.role,province_name:e.province_name||"",province_id:e.province_id||"",city_name:e.city_name||"",city_id:e.city_id||"",district_name:e.district_name||"",district_id:e.district_id||"",user_id:e.id||""},a=Object.keys(t).map(e=>`${e}=${encodeURIComponent(t[e])}`).join("&");uni.navigateTo({url:`/pages/culture/heritage?${a}`})},g=()=>{var e,t,a,i;const l=o.value;if(!l)return void uni.showToast({title:"请先登录",icon:"error"});if(!("super_admin"===l.role||l.module_permissions&&!0===l.module_permissions.ancient_books))return void uni.showToast({title:"您没有古籍管理权限",icon:"error"});const n={manage_mode:"true",role:l.role,province_name:l.province_name||"",province_id:l.province_id||"",city_name:l.city_name||"",city_id:l.city_id||"",district_name:l.district_name||"",district_id:l.district_id||"",user_id:l.user_id||l.id||"",ancient_books_permission:(null==(e=l.module_permissions)?void 0:e.ancient_books)?"true":"false",paintings_permission:(null==(t=l.module_permissions)?void 0:t.paintings)?"true":"false",archives_permission:(null==(a=l.module_permissions)?void 0:a.archives)?"true":"false",videos_permission:(null==(i=l.module_permissions)?void 0:i.videos)?"true":"false"},c=Object.keys(n).map(e=>`${e}=${encodeURIComponent(n[e])}`).join("&");uni.navigateTo({url:`/pages/culture/ancient-books-management?${c}`})},h=()=>{uni.showToast({title:"功能开发中",icon:"none"})},y=()=>{uni.showToast({title:"功能开发中",icon:"none"})},_=()=>{uni.showToast({title:"功能开发中",icon:"none"})},E=()=>{uni.showToast({title:"功能开发中",icon:"none"})},w=()=>{uni.showToast({title:"功能开发中",icon:"none"})},N=()=>{uni.showToast({title:"功能开发中",icon:"none"})},f=()=>{uni.navigateTo({url:"/pages/rail/cq-line2"})};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"culture-management"},[e.createElementVNode("view",{class:"page-header"},[e.createElementVNode("text",{class:"page-title"},"文化管理"),e.createElementVNode("text",{class:"page-subtitle"},e.toDisplayString(u()),1)]),e.createElementVNode("view",{class:"grid-container"},[e.createElementVNode("view",{class:"grid-item",onClick:v},[e.createElementVNode("view",{class:"grid-icon"},"🏠"),e.createElementVNode("text",{class:"grid-title"},"首页管理"),e.createElementVNode("text",{class:"grid-desc"},"管理首页内容")]),e.createElementVNode("view",{class:"grid-item",onClick:p},[e.createElementVNode("view",{class:"grid-icon"},"📜"),e.createElementVNode("text",{class:"grid-title"},"文源纪管理"),e.createElementVNode("text",{class:"grid-desc"},"管理文源纪展示内容")]),e.createElementVNode("view",{class:"grid-item",onClick:g},[e.createElementVNode("view",{class:"grid-icon"},"📚"),e.createElementVNode("text",{class:"grid-title"},"古籍管理"),e.createElementVNode("text",{class:"grid-desc"},"管理古籍典藏内容")]),e.createElementVNode("view",{class:"grid-item",onClick:h},[e.createElementVNode("view",{class:"grid-icon"},"🎬"),e.createElementVNode("text",{class:"grid-title"},"声像文藏"),e.createElementVNode("text",{class:"grid-desc"},"视听内容管理")]),e.createElementVNode("view",{class:"grid-item",onClick:y},[e.createElementVNode("view",{class:"grid-icon"},"🎭"),e.createElementVNode("text",{class:"grid-title"},"活动管理"),e.createElementVNode("text",{class:"grid-desc"},"文化活动管理")]),e.createElementVNode("view",{class:"grid-item",onClick:_},[e.createElementVNode("view",{class:"grid-icon"},"🗺️"),e.createElementVNode("text",{class:"grid-title"},"旅游管理"),e.createElementVNode("text",{class:"grid-desc"},"文化旅游管理")]),e.createElementVNode("view",{class:"grid-item",onClick:E},[e.createElementVNode("view",{class:"grid-icon"},"📰"),e.createElementVNode("text",{class:"grid-title"},"资讯管理"),e.createElementVNode("text",{class:"grid-desc"},"文化资讯管理")]),e.createElementVNode("view",{class:"grid-item",onClick:w},[e.createElementVNode("view",{class:"grid-icon"},"📚"),e.createElementVNode("text",{class:"grid-title"},"教育管理"),e.createElementVNode("text",{class:"grid-desc"},"文化教育管理")]),e.createElementVNode("view",{class:"grid-item",onClick:N},[e.createElementVNode("view",{class:"grid-icon"},"📈"),e.createElementVNode("text",{class:"grid-title"},"统计报表"),e.createElementVNode("text",{class:"grid-desc"},"数据统计分析")]),e.createElementVNode("view",{class:"grid-item",onClick:f},[e.createElementVNode("view",{class:"grid-icon"},"🚇"),e.createElementVNode("text",{class:"grid-title"},"轨道交通"),e.createElementVNode("text",{class:"grid-desc"},"重庆轨道2号线")])])]))}}),[["__scopeId","data-v-78a08210"]]),pe=[{id:"jiaochangkou",name:"较场口",order:1,isTransfer:!0,isFeatured:!0,description:"重庆母城的历史起点，古城门遗址所在地",detailedDescription:"较场口是重庆最具历史底蕴的地区之一，这里曾是古重庆城的南门，承载着山城千年的历史记忆。",highlight:"历史文化",features:["历史古迹","文化遗址","换乘枢纽"],attractions:[{name:"凯旋路电梯",distance:"352m",image:"/static/images/cq_2/kxldt.jpg",description:"重庆最长的户外电梯，连接上下半城",details:"凯旋路电梯全长112米，是重庆最长的户外电梯之一，连接了较场口的上下半城，是体验重庆立体交通的绝佳地点。",images:["/static/images/cq_2/kxldt.jpg"]},{name:"湖广会馆",distance:"2000m",image:"/static/images/cq_2/hghg.png",description:"明清古建筑群，重庆历史文化名片",details:"湖广会馆是重庆现存最大的古建筑群，始建于清朝，展现了重庆移民文化的历史变迁。"},{name:"东水门大桥",distance:"2000m",image:"/static/images/cq_2/dsmcjdq.png",description:"横跨长江的现代化大桥",details:"东水门大桥是重庆的标志性建筑之一，夜景尤为壮观，是拍摄重庆夜景的最佳地点之一。"},{name:"较场口夜市",distance:"300m",image:"",description:"重庆传统夜市，品尝地道小吃",details:"较场口夜市汇聚了重庆各种传统小吃，是体验重庆夜生活和美食文化的绝佳场所。"}],history:"较场口得名于明清时期的军事操练场，是重庆古城的重要组成部分。",culture:"这里保存着重庆传统的巴渝文化，是了解山城历史的重要窗口。",tips:"建议傍晚时分前往，可以欣赏到美丽的江景和夜景。"},{id:"linjiangmen",name:"临江门",order:2,isTransfer:!1,isFeatured:!0,description:"解放碑商圈核心，重庆最繁华的商业中心",detailedDescription:"临江门站位于解放碑商圈核心区域，是重庆最繁华的商业和金融中心，汇聚了众多购物中心和美食。",highlight:"商业中心",features:["购物天堂","美食聚集","金融中心"],attractions:[{name:"解放碑",distance:"800m",image:"/static/images/cq_2/ljm-jfb.jpg",description:"重庆地标建筑，抗战胜利纪功碑",details:'解放碑全称"重庆人民解放纪念碑"，原名"抗战胜利纪功碑"，是全中国唯一的一座纪念中华民族抗日战争胜利的国家纪念碑。碑高27.5米，有旋梯可达顶端。解放碑位于重庆主城渝中区商业区中心部位，是重庆的标志建筑物。以解放碑为中心的十字路口，包括民权路、民族路和邹容路，是重庆最繁华的商业圈。周边汇聚了重庆百货、新世纪百货、太平洋百货等大型购物中心，是购物、美食、娱乐的天堂。',images:["/static/images/cq_2/ljm-jfb.jpg"]},{name:"洪崖洞",distance:"1.2km",image:"/static/images/cq_2/ljm-hyd.jpg",description:"巴渝传统建筑特色的吊脚楼群",details:'洪崖洞是重庆历史文化的见证和重庆城市精神的象征，以巴渝传统建筑特色的"吊脚楼"风貌为主体，依山就势，沿崖而建。洪崖洞由纸盐河酒吧街、天成巷巴渝风情街、盛宴美食街及异域风情城市阳台四部分组成。建筑面积4.6万平方米，主要景点由吊脚楼、仿古商业街等景观组成。夜晚灯火辉煌，金碧辉煌，被誉为"现实版千与千寻"，是重庆夜景的代表之一。这里汇聚了重庆各种特色小吃和手工艺品，是体验巴渝文化的绝佳场所。',images:["/static/images/cq_2/ljm-hyd.jpg","/static/images/cq_2/hyd2.jpg"]},{name:"朝天门广场",distance:"2km",image:"/static/images/cq_2/ctm1.jpg",description:"两江汇流的壮观景象",details:"朝天门位于重庆渝中区渝中半岛的嘉陵江与长江交汇处，是重庆以前的十七门之一。南宋时期宋朝定都临安，即今天的杭州，那时有圣旨传来是经长江到达朝天门，所以才有了朝天门这个名字。朝天门是两江枢纽，也是重庆最大的水路客运码头。站在朝天门广场上，可以看到两江汇流的壮观景象，嘉陵江水呈绿色，长江水呈黄色，两江汇合后的长江水呈混黄色。朝天门广场是观看重庆夜景的最佳地点之一，也是重庆标志性的旅游景点。",images:["/static/images/cq_2/ctm1.jpg","/static/images/cq_2/ctm2.jpg"]},{name:"云端之眼",distance:"2km",image:"/static/images/cq_2/ljm-ydzy.jpg",description:"重庆最高观景台，360度俯瞰山城全貌",details:"云端之眼位于重庆来福士广场顶层，是重庆最高的观景台之一，海拔约300米。这里提供360度无死角的重庆全景视野，可以俯瞰两江四岸的壮丽景色。观景台采用全透明玻璃设计，给游客带来悬空的刺激体验。白天可以清晰看到重庆的山水城市格局，夜晚则能欣赏到璀璨的重庆夜景。这里是拍摄重庆城市大片的绝佳位置，也是情侣约会和游客打卡的热门地点。",images:["/static/images/cq_2/ljm-ydzy.jpg"]}],history:"临江门是重庆开埠后最早的商业区，见证了山城的现代化发展历程。",culture:"这里融合了传统巴渝文化和现代都市文明，是重庆文化的缩影。",tips:"周末人流较多，建议工作日前往购物和用餐。"},{id:"huanghuayuan",name:"黄花园",order:3,isTransfer:!1,isFeatured:!0,description:"嘉陵江畔的宁静站点，连接两江四岸",detailedDescription:"黄花园站紧邻嘉陵江，是观赏重庆山水城市风貌的绝佳位置，这里有着独特的江景和桥梁景观。",highlight:"江景观光",features:["江景观光","桥梁景观","休闲漫步"],attractions:[{name:"水上列车",distance:"500m",image:"/static/images/cq_2/hhy-sslc.jpg",description:"重庆轻轨2号线跨江段，体验水上行驶的奇妙感觉",details:'重庆轻轨2号线在黄花园至大溪沟段跨越嘉陵江，被称为"水上列车"。这段轨道建在嘉陵江上，全长约1.2公里，是重庆独特的城市景观之一。乘坐轻轨经过这段时，可以近距离欣赏嘉陵江的美丽风光，感受在江面上行驶的奇妙体验。两岸的山城景色尽收眼底，是体验重庆"山水之城"魅力的绝佳方式。这里也是摄影爱好者的天堂，可以拍摄到轻轨与江景完美融合的画面。',images:["/static/images/cq_2/hhy-sslc.jpg"]},{name:"黄花园大桥",distance:"300m",image:"",description:"横跨嘉陵江的现代化大桥",details:"黄花园大桥是连接重庆江北区和渝中区的重要桥梁，全长1022米，主跨长度为250米。大桥采用双塔双索面斜拉桥结构，造型优美，是重庆的标志性建筑之一。桥上可以欣赏到嘉陵江两岸的美丽风光，是拍摄重庆城市景观的绝佳位置。夜晚时分，大桥灯光璀璨，与江面倒影相映成趣，构成了重庆夜景的重要组成部分。"},{name:"嘉陵江滨江路",distance:"500m",image:"",description:"江边休闲步道，亲水观景胜地",details:"嘉陵江滨江路是重庆重要的滨江景观带，沿江而建，全长数公里。这里绿树成荫，环境优美，是市民休闲健身的好去处。滨江路上设有多个观景平台，可以近距离欣赏嘉陵江的美丽风光。路边还有咖啡厅、茶楼等休闲场所，是约会聊天的理想地点。清晨和傍晚时分，这里聚集了众多晨练和散步的市民，充满了生活气息。"},{name:"重庆科技馆",distance:"2.5km",image:"",description:"现代化科普教育基地",details:"重庆科技馆是重庆市重要的科普教育基地，建筑面积4.5万平方米，展示面积3万平方米。馆内设有生活科技、防灾科技、交通科技、国防科技等多个主题展厅，通过互动体验的方式普及科学知识。科技馆采用现代化的展示手段，集科学性、知识性、趣味性于一体，是青少年科普教育的重要场所。馆内还定期举办各种科普活动和临时展览，是家庭亲子游的热门选择。"}],history:"黄花园因历史上此地遍植黄花而得名，是重庆江北区的重要节点。",culture:'这里体现了重庆"山水之城"的独特魅力，是摄影爱好者的天堂。',tips:"最佳观景时间是日落时分，可以拍摄到美丽的江景和桥梁剪影。"},{id:"daxigou",name:"大溪沟",order:4,isTransfer:!1,isFeatured:!0,description:"文化艺术聚集地，重庆的文艺心脏",detailedDescription:"大溪沟是重庆重要的文化艺术区域，这里聚集了众多文化场馆和艺术机构，是重庆文化生活的重要组成部分。",highlight:"文化艺术",features:["文化场馆","艺术展览","创意产业"],attractions:[{name:"S湾",distance:"500m",image:"/static/images/cq_2/dxg.jpg",description:"嘉陵江S型弯道，重庆最美江湾景观",details:'S湾是嘉陵江在大溪沟段形成的天然S型弯道，是重庆最具特色的江湾景观之一。从高处俯瞰，嘉陵江在这里画出了一个完美的S型曲线，两岸绿树成荫，江水清澈。这里是重庆"山水之城"的完美体现，也是摄影师们钟爱的拍摄地点。沿江步道设施完善，是市民晨练和休闲的好去处。夜晚时分，两岸灯火倒映在江面上，形成美丽的光影效果。',images:["/static/images/cq_2/dxg.jpg"]},{name:"重庆美术馆",distance:"600m",image:"",description:"重庆市重要的艺术展览场所",details:"重庆美术馆是重庆市重要的艺术展览和文化交流场所，建筑面积约1.5万平方米。馆内设有多个展厅，常年举办各类艺术展览，包括绘画、雕塑、摄影、书法等多种艺术形式。美术馆不仅展示本土艺术家的作品，也引进国内外优秀的艺术展览。这里是重庆文化艺术的重要窗口，也是市民接受艺术熏陶的重要场所。建筑设计现代简约，内部空间宽敞明亮，为艺术品展示提供了良好的环境。"},{name:"重庆图书馆",distance:"800m",image:"",description:"重庆市最大的综合性图书馆",details:"重庆图书馆是重庆市最大的综合性图书馆，建筑面积8.3万平方米，藏书量超过400万册。图书馆设有阅览室、电子阅览室、少儿阅览室、古籍阅览室等多个功能区域。这里不仅是市民阅读学习的重要场所，也是重庆重要的文献信息中心。图书馆建筑设计现代化，内部环境优雅安静，配备了先进的数字化设备。定期举办各种文化讲座和读书活动，是重庆文化生活的重要组成部分。"},{name:"文化创意园",distance:"700m",image:"",description:"重庆文化创意产业聚集地",details:"大溪沟文化创意园是重庆重要的文化创意产业聚集地，汇聚了众多设计公司、艺术工作室、文化机构等。园区内有画廊、咖啡厅、书店、手工艺品店等，营造了浓厚的文化艺术氛围。这里经常举办艺术展览、文化沙龙、创意市集等活动，是年轻人和文艺爱好者的聚集地。园区建筑多为改造的老厂房，保留了工业遗产的特色，与现代文化创意完美融合。"}],history:"大溪沟历史上是重庆的文教区，培养了众多文化名人。",culture:"这里是重庆现代文化的发源地，承载着城市的文化记忆。",tips:"周末经常有文化活动和艺术展览，建议提前查看活动安排。"},{id:"zengjiayan",name:"曾家岩",order:5,isTransfer:!1,isFeatured:!0,description:"红色文化地标，抗战历史见证地",detailedDescription:"曾家岩是重庆重要的红色文化地标，这里保存着丰富的抗战历史遗迹，是了解重庆抗战文化的重要场所。",highlight:"红色文化",features:["红色旅游","历史教育","爱国主义"],attractions:[{name:"重庆人民大礼堂",distance:"500m",image:"/static/images/cq_2/zjy-dlt.jpg",description:"重庆标志性建筑，仿古宫殿式建筑群",details:"重庆人民大礼堂是重庆市的标志性建筑之一，建于1951-1954年，是一座仿古民族建筑群。大礼堂采用明清宫殿建筑风格，主体建筑为四层，顶部为重檐攒尖式屋顶，气势恢宏。大礼堂可容纳4200人，是重庆重要的政治文化活动场所。建筑群包括大礼堂主体、南楼、北楼，整体布局对称，体现了中国传统建筑的美学特色。这里经常举办重要会议、文艺演出等活动，是重庆政治文化生活的重要场所。",images:["/static/images/cq_2/zjy-dlt.jpg"]},{name:"中山四路",distance:"500m",image:"/static/images/cq_2/zjy-zssl.jpg",description:"重庆最美街道，民国建筑风情街",details:"中山四路被誉为重庆最美的街道之一，全长约800米，两旁梧桐成荫，保存着大量民国时期的建筑。这条路曾是国民政府时期的政治中心，沿街有桂园、周公馆、戴公馆等历史建筑。街道两旁的法国梧桐形成绿色隧道，四季景色各异，春夏绿意盎然，秋季金黄满树。这里是重庆历史文化的重要载体，也是市民休闲散步的好去处。许多影视作品都在这里取景，是重庆文艺青年的打卡圣地。",images:["/static/images/cq_2/zjy-zssl.jpg"]},{name:"重庆中国三峡博物馆",distance:"1.5km",image:"/static/images/cq_2/zjy-sxbwg.jpg",description:"展示三峡文化和重庆历史的国家级博物馆",details:'重庆中国三峡博物馆是国家一级博物馆，建筑面积4.25万平方米，是展示和研究三峡文化的重要场所。博物馆设有《壮丽三峡》、《远古巴渝》、《重庆·城市之路》等基本陈列，全面展示了三峡地区的自然风光、历史文化和重庆的城市发展历程。馆藏文物17万余件套，其中珍贵文物4万余件套。博物馆建筑设计现代化，与重庆人民大礼堂形成"古今对话"的建筑景观。这里是了解重庆历史文化和三峡文明的重要窗口。',images:["/static/images/cq_2/zjy-sxbwg.jpg"]}],history:"曾家岩在抗战时期是重要的政治活动中心，见证了重庆的抗战历史。",culture:"这里承载着深厚的红色文化底蕴，是进行爱国主义教育的重要基地。",tips:"建议安排半天时间深度游览，可以更好地了解抗战历史。"},{id:"niujiaotuo",name:"牛角沱",order:6,isTransfer:!0,isFeatured:!0,description:"重要交通枢纽，连接江北与渝中",detailedDescription:"牛角沱是重庆重要的交通枢纽站，连接着江北区和渝中区，是重庆轨道交通网络的重要节点。",highlight:"交通枢纽",features:["换乘枢纽","交通便利","商业配套"],attractions:[{name:"纱帽石",distance:"1.5km",image:"/static/images/cq_2/sms.png",description:"嘉陵江中的天然石岛，重庆独特地质景观",details:"纱帽石是位于嘉陵江中的一座天然石岛，因形似古代官员的纱帽而得名。这座石岛面积约2000平方米，高出江面约10米，是重庆独特的地质景观。石岛上绿树成荫，建有亭台楼阁，是观赏嘉陵江风光的绝佳位置。从岛上可以360度欣赏两岸的山城景色，特别是夜晚时分，两岸灯火辉煌，倒映在江面上，景色格外迷人。纱帽石也是重庆历史文化的见证，历代文人墨客都曾在此留下诗词佳作。",images:["/static/images/cq_2/sms.png"]},{name:"怡园",distance:"1.5km",image:"/static/images/cq_2/njt-sms-yy.jpg",description:"江边休闲公园，市民健身娱乐好去处",details:"怡园是位于嘉陵江边的一座综合性公园，占地面积约15公顷，是市民休闲健身的重要场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区等多个功能区域。园内还有观江亭台，可以欣赏嘉陵江的美丽风光。公园的设计充分利用了江边的自然地形，形成了高低错落的景观层次。这里是附近居民晨练、散步、休闲的好去处，也是家庭亲子活动的理想场所。",images:["/static/images/cq_2/njt-sms-yy.jpg"]}],history:"牛角沱因地形似牛角而得名，是重庆传统的渡口和交通要道。",culture:"这里体现了重庆作为交通枢纽城市的重要地位。",tips:"换乘时注意指示标识，高峰期人流较大。"},{id:"liziba",name:"李子坝",order:7,isTransfer:!1,isFeatured:!0,description:"网红打卡地，轻轨穿楼奇观",detailedDescription:"李子坝站因轻轨穿越居民楼而闻名全国，成为重庆最具特色的网红打卡地，展现了山城独特的立体交通魅力。",highlight:"网红景点",features:["网红打卡","建筑奇观","摄影圣地"],attractions:[{name:"轻轨穿楼",distance:"50m",image:"/static/images/cq_2/lzb-qgcl.jpg",description:"世界唯一的轻轨穿楼奇观",details:"李子坝轻轨穿楼是重庆独有的城市景观，重庆轨道交通2号线从李子坝站穿过一栋19层的居民楼，这在全世界都是独一无二的。这栋楼建于2004年，轻轨在6-8层穿过，由于采用了减震降噪技术，对居民生活影响很小。这里已成为重庆最著名的网红打卡地，每天都有大量游客前来拍照留念。最佳拍摄时间是上午10点到下午4点，可以拍到轻轨穿楼的完整画面。",images:["/static/images/cq_2/lzb-qgcl.jpg"]},{name:"李子坝抗战遗址公园",distance:"800m",image:"",description:"抗战时期重要历史遗址",details:"李子坝抗战遗址公园是重庆抗战文化的重要载体，园内保存了抗战时期的重要历史建筑和文物。公园内有抗战时期的防空洞、指挥所等历史遗迹，还有详细的抗战历史展览。这里曾是国民政府军事委员会政治部第三厅所在地，郭沫若、老舍、冰心等文化名人都曾在此工作过。公园环境优美，既是历史教育基地，也是市民休闲的好去处。",images:[]},{name:"鹅岭公园",distance:"1.8km",image:"",description:"重庆主城最高点，俯瞰全城",details:"鹅岭公园位于重庆市渝中区，海拔345米，是重庆主城区的最高点，也是观赏重庆夜景的最佳地点之一。公园内有瞰胜楼，登楼可360度俯瞰重庆全景，长江、嘉陵江尽收眼底。公园始建于1909年，原名鹅项岭，因其形似鹅颈项而得名。园内古树参天，环境清幽，是重庆市民休闲健身的好去处。夜晚时分，这里是观赏重庆璀璨夜景的绝佳位置。",images:[]}],history:'李子坝站的设计体现了重庆"立体城市"的建设理念，是城市规划的创新典范。',culture:"这里代表了重庆人敢于创新、因地制宜的城市精神。",tips:"最佳拍摄时间是上午10点到下午4点，光线较好。观景台位于站台对面。"},{id:"fotuguan",name:"佛图关",order:8,isTransfer:!1,isFeatured:!0,description:"古关隘遗址，山城制高点",detailedDescription:"佛图关是重庆古代的重要关隘，位于山城制高点，这里有着深厚的历史文化底蕴和绝佳的城市景观。",highlight:"历史古迹",features:["古关隘","城市观景","历史文化"],attractions:[{name:"开往春天的地铁",distance:"200m",image:"/static/images/cq_2/ftg-kwctddt.jpg",description:"重庆网红打卡地，轻轨穿越花海的浪漫景象",details:'"开往春天的地铁"是重庆轻轨2号线佛图关段的网红景观，每年春季，轻轨沿线开满了粉色的樱花和黄色的油菜花，形成了"轻轨穿花海"的浪漫景象。这里成为了重庆最受欢迎的拍照打卡地之一，吸引了无数游客和摄影爱好者前来。最佳观赏时间是每年3-4月，此时花开正盛，轻轨从花海中穿过，如同开往春天的列车。这里不仅是拍照圣地，也体现了重庆"山水之城、美丽之地"的城市魅力。',images:["/static/images/cq_2/ftg-kwctddt.jpg"]},{name:"佛图关公园",distance:"200m",image:"",description:"重庆主城制高点之一，俯瞰城市全景",details:"佛图关公园位于重庆主城的制高点之一，海拔约400米，是观赏重庆全景的绝佳位置。公园内绿树成荫，环境清幽，设有多个观景台，可以360度俯瞰重庆的山水城市风貌。从这里可以看到长江、嘉陵江的交汇，以及重庆主城的高楼大厦。公园内还保留着古代关隘的遗迹，体现了重庆深厚的历史文化底蕴。这里是市民登高望远、休闲健身的好去处，也是摄影爱好者拍摄重庆全景的理想地点。"},{name:"佛图关遗址",distance:"300m",image:"",description:"古代军事关隘遗址，重庆历史文化见证",details:"佛图关遗址是重庆古代重要的军事关隘遗址，始建于宋代，历经元、明、清各朝代的修建和使用。关隘地处重庆主城制高点，地势险要，是古代重庆城防体系的重要组成部分。遗址现存城墙、城门、炮台等建筑遗迹，虽经历史沧桑，但仍能看出当年的雄伟气势。这里是了解重庆古代军事文化和城市发展历史的重要场所，也是重庆市文物保护单位。遗址周围环境优美，是历史文化爱好者探访的好去处。"}],history:"佛图关始建于宋代，是重庆古代军事防御体系的重要组成部分。",culture:"这里承载着重庆深厚的历史文化，是了解山城古代文明的重要窗口。",tips:"公园内有观景台，可以俯瞰重庆全景，建议选择晴朗天气前往。"}],ge=[...pe,{id:"daping",name:"大坪",order:9,isTransfer:!0,isFeatured:!1,description:"重要的交通枢纽和商业区",detailedDescription:"大坪站是2号线的重要分叉点，连接主线和支线，周边商业发达，是九龙坡区的重要交通枢纽。",highlight:"交通枢纽",features:["交通枢纽","商业区","分叉点"],attractions:[{name:"大坪时代天街",distance:"400m",image:"",description:"重庆知名购物中心，时尚潮流聚集地",details:"大坪时代天街是重庆知名的大型购物中心，汇聚了众多国际国内知名品牌。商场内设有电影院、餐饮、娱乐等多种业态，是年轻人购物休闲的热门去处。建筑设计现代时尚，内部装修精美，购物环境舒适。这里经常举办各种时尚活动和品牌发布会，是重庆时尚潮流的风向标。",images:[]},{name:"重庆天地",distance:"1.2km",image:"",description:"高端商业综合体，国际化生活方式",details:"重庆天地是重庆高端商业综合体的代表，由国际知名开发商打造。项目融合了购物、餐饮、娱乐、办公、居住等多种功能，体现了国际化的生活方式。这里汇聚了众多国际奢侈品牌和高端餐饮，是重庆上流社会的聚集地。建筑风格现代简约，环境优雅，服务品质一流。",images:[]},{name:"大坪医院",distance:"600m",image:"",description:"重庆知名三甲医院，医疗服务中心",details:"重庆大坪医院是中国人民解放军陆军军医大学第三附属医院，是一所集医疗、教学、科研为一体的大型综合性三级甲等医院。医院技术力量雄厚，医疗设备先进，在心血管、神经外科、烧伤科等专业领域享有盛誉。医院环境优美，服务优质，是重庆市民信赖的医疗机构。",images:[]}],history:"大坪地区历史悠久，是重庆传统的商贸集散地。",culture:"现代商业与传统文化的完美融合。",tips:"这里是换乘的重要节点，建议预留充足的换乘时间。"},{id:"yuanjigang",name:"袁家岗",order:10,isTransfer:!1,isFeatured:!1,description:"医疗卫生中心区域",detailedDescription:"袁家岗站周边聚集了多家大型医院，是重庆重要的医疗卫生服务中心。",highlight:"医疗中心",features:["医疗中心","居住区"],attractions:[{name:"重医附一院",distance:"400m",image:"",description:"重庆医科大学附属第一医院，西南地区知名三甲医院",details:"重庆医科大学附属第一医院是西南地区知名的三级甲等综合医院，始建于1957年。医院技术力量雄厚，医疗设备先进，在心血管、神经外科、肿瘤、器官移植等专业领域享有盛誉。医院占地面积约200亩，建筑面积30余万平方米，开放床位3200余张。这里不仅是重要的医疗服务中心，也是医学教育和科研基地，为重庆及西南地区的医疗卫生事业做出了重要贡献。"},{name:"重庆医科大学",distance:"600m",image:"",description:"重庆市重点医科大学，培养医学人才的摇篮",details:'重庆医科大学是重庆市重点建设的医科大学，创建于1956年，是国家"中西部高校基础能力建设工程"重点建设高校。学校设有临床医学、基础医学、药学、护理学等多个学院，拥有完善的医学教育体系。校园环境优美，教学设施先进，图书馆藏书丰富。学校培养了大批优秀的医学人才，为重庆乃至全国的医疗卫生事业发展做出了重要贡献。'},{name:"袁家岗公园",distance:"300m",image:"",description:"社区休闲公园，市民健身娱乐场所",details:"袁家岗公园是一座综合性社区公园，占地面积约8公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区等多个功能区域。园内还有小型人工湖，湖水清澈，环境优美。公园的设计充分考虑了不同年龄段市民的需求，是附近居民晨练、散步、休闲的重要场所，也体现了重庆宜居城市的建设成果。"}],history:"袁家岗因清代袁姓大户聚居而得名。",culture:"医学教育和医疗服务的重要基地。",tips:"周边医院较多，交通相对繁忙，建议避开就诊高峰期。"},{id:"xiejiawan",name:"谢家湾",order:11,isTransfer:!1,isFeatured:!1,description:"居住区和教育区",detailedDescription:"谢家湾站位于成熟的居住区，周边有多所学校，是重要的教育和居住区域。",highlight:"教育区",features:["教育区","居住区"],attractions:[{name:"谢家湾小学",distance:"200m",image:"",description:"重庆知名小学，基础教育典范",details:'谢家湾小学是重庆市知名的小学，创建于1957年，是重庆市基础教育的典范学校。学校占地面积约3万平方米，建筑面积2万余平方米，环境优美，设施先进。学校以"红梅花儿开，朵朵放光彩"为办学理念，注重学生的全面发展。多年来，学校在教育教学、素质教育等方面取得了显著成绩，培养了大批优秀学生，在重庆教育界享有很高声誉。'},{name:"华润万象城",distance:"800m",image:"",description:"大型购物中心，时尚消费新地标",details:"华润万象城是重庆九龙坡区的大型购物中心，总建筑面积约15万平方米，是集购物、餐饮、娱乐、休闲于一体的现代化商业综合体。商场内汇聚了众多国际国内知名品牌，设有电影院、儿童乐园、美食广场等多种业态。建筑设计现代时尚，内部装修精美，购物环境舒适。这里是周边居民购物消费的重要场所，也是年轻人休闲娱乐的热门去处。"},{name:"彩云湖公园",distance:"1km",image:"",description:"重庆最大的人工湖公园，生态休闲胜地",details:'彩云湖公园是重庆最大的人工湖公园，占地面积约1100亩，其中湖面面积约600亩。公园以生态环保为主题，湖水清澈，环境优美，是重庆重要的生态休闲场所。园内设有环湖步道、观景台、儿童游乐区等多个功能区域，还有丰富的水生植物和鸟类。公园的建设体现了重庆"山水之城"的生态理念，是市民休闲健身、亲近自然的好去处，也是重庆生态文明建设的重要成果。'}],history:"谢家湾因谢姓人家聚居而得名，历史悠久。",culture:"重庆基础教育的重要区域。",tips:"周边学校较多，上下学时间人流量大。"},{id:"yangjiaping",name:"杨家坪",order:12,isTransfer:!1,isFeatured:!1,description:"九龙坡区政治经济中心",detailedDescription:"杨家坪是九龙坡区的政治、经济、文化中心，商业繁荣，交通便利。",highlight:"区政府",features:["区政府","商业中心","文化中心"],attractions:[{name:"杨家坪步行街",distance:"600m",image:"",description:"九龙坡区商业中心，购物休闲一条街",details:"杨家坪步行街是九龙坡区的商业中心，全长约800米，是重庆西部重要的商业步行街。街道两旁商铺林立，汇聚了服装、餐饮、娱乐等多种业态，是当地居民购物消费的主要场所。步行街环境整洁，设施完善，经常举办各种商业活动和文化表演。这里不仅是购物的好去处，也是体验重庆本土商业文化的重要场所，展现了重庆西部地区的商业活力。"},{name:"九龙坡区政府",distance:"800m",image:"",description:"九龙坡区行政中心，政务服务中心",details:"九龙坡区政府是九龙坡区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。周边配套设施齐全，交通便利。作为九龙坡区的政治中心，这里承担着重要的行政管理职能，是推动九龙坡区经济社会发展的重要机构。"},{name:"重庆动物园",distance:"1.2km",image:"",description:"西南地区重要动物园，家庭亲子游胜地",details:"重庆动物园建于1954年，是西南地区重要的动物园之一，占地面积约45公顷。园内饲养着大熊猫、金丝猴、华南虎等珍稀动物200多种、3000余只。动物园环境优美，设施完善，设有熊猫馆、猛兽区、鸟语林等多个展区。这里不仅是重要的动物保护和科研基地，也是市民休闲娱乐和青少年科普教育的重要场所，是重庆家庭亲子游的热门选择。"}],history:"杨家坪因杨姓人家聚居的平坝而得名。",culture:"九龙坡区的政治文化中心。",tips:"商业区人流量大，建议选择合适时间出行。"},{id:"dongwuyuan",name:"动物园",order:13,isTransfer:!1,isFeatured:!1,description:"重庆动物园所在地",detailedDescription:"动物园站因重庆动物园而得名，是市民休闲娱乐的重要场所。",highlight:"动物园",features:["动物园","休闲娱乐"],attractions:[{name:"重庆动物园",distance:"300m",image:"",description:"西南地区最大动物园，珍稀动物的家园",details:"重庆动物园是西南地区最大的动物园，建于1954年，占地面积约45公顷，是国家AAAA级旅游景区。园内饲养着来自世界各地的珍稀动物200多种、3000余只，其中包括大熊猫、金丝猴、华南虎、亚洲象等国家一级保护动物。动物园分为猛兽区、灵长类区、鸟语林、水禽湖等多个展区，每个区域都有其独特的特色。园内环境优美，绿树成荫，是集动物保护、科学研究、科普教育、休闲娱乐于一体的综合性动物园。"},{name:"杨家坪商圈",distance:"1.2km",image:"",description:"九龙坡区核心商业区，购物娱乐中心",details:"杨家坪商圈是九龙坡区的核心商业区，以杨家坪步行街为中心，辐射周边多个商业综合体。商圈内汇聚了百货商场、专卖店、餐饮店、娱乐场所等各类商业设施，是重庆西部重要的购物娱乐中心。这里交通便利，人流量大，商业氛围浓厚，不仅满足了周边居民的日常消费需求，也吸引了众多外地游客前来购物。商圈的发展体现了重庆西部地区的经济活力和商业繁荣。"}],history:"重庆动物园建于1954年，是西南地区重要的动物园。",culture:"重庆市民休闲娱乐的重要场所。",tips:"适合家庭出游，建议预留半天时间游览动物园。"},{id:"dayancun",name:"大堰村",order:14,isTransfer:!1,isFeatured:!1,description:"传统居住区",detailedDescription:"大堰村站位于传统的居住区域，保持着重庆老城区的特色。",highlight:"居住区",features:["居住区","传统社区"],attractions:[{name:"大堰村社区",distance:"100m",image:"",description:"重庆传统社区，体验本土生活文化",details:"大堰村社区是重庆典型的传统社区，保持着浓厚的重庆本土生活气息。社区内有传统的重庆民居建筑，街道狭窄但充满生活气息。这里有传统的小商铺、茶馆、面馆等，是体验重庆市井文化的好地方。社区居民热情好客，保持着重庆人特有的豪爽性格。虽然是老社区，但基础设施不断完善，体现了重庆城市更新和社区改造的成果。"},{name:"九龙坡公园",distance:"600m",image:"",description:"区域性综合公园，市民休闲健身场所",details:"九龙坡公园是九龙坡区重要的综合性公园，占地面积约20公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了重庆宜居城市的建设理念。"}],history:"大堰村因古代的大堰而得名，历史悠久。",culture:"保持着重庆传统社区的文化特色。",tips:"体验重庆传统社区生活的好地方。"},{id:"mawangchang",name:"马王场",order:15,isTransfer:!1,isFeatured:!1,description:"传统居住区和商业区",detailedDescription:"马王场站位于传统的居住和商业区域，是当地居民生活的重要交通节点。",highlight:"居住区",features:["居住区","商业区"],attractions:[{name:"马王场市场",distance:"200m",image:"",description:"传统农贸市场，体验重庆市井生活",details:"马王场市场是重庆传统的农贸市场，历史悠久，是当地居民购买生鲜食品的主要场所。市场内商品丰富，有新鲜蔬菜、水果、肉类、水产等各类农副产品。这里保持着重庆传统市场的特色，商贩热情，价格实惠，是体验重庆市井生活的好地方。市场虽然规模不大，但商品齐全，服务周边多个社区的居民，体现了重庆传统商贸文化的延续。"},{name:"九龙坡体育馆",distance:"800m",image:"",description:"区级体育场馆，全民健身中心",details:"九龙坡体育馆是九龙坡区重要的体育场馆，建筑面积约8000平方米，可容纳3000余人。体育馆设施完善，可举办篮球、排球、羽毛球等多种体育比赛和活动。这里不仅是重要的体育比赛场所，也是市民健身锻炼的重要场所，定期开放供市民使用。体育馆经常举办各种体育赛事和全民健身活动，是推动九龙坡区体育事业发展的重要载体。"}],history:"马王场因古代马市而得名，历史悠久。",culture:"重庆传统商贸文化的体现。",tips:"周边有传统市场，可以体验重庆本地生活。"},{id:"pingan",name:"平安",order:16,isTransfer:!1,isFeatured:!1,description:"居住区和工业区",detailedDescription:"平安站周边以居住区和轻工业为主，是重庆西部重要的居住区域。",highlight:"居住区",features:["居住区","工业区"],attractions:[{name:"平安公园",distance:"300m",image:"",description:"社区公园，居民休闲健身场所",details:"平安公园是一座社区性公园，占地面积约5公顷，为周边居民提供了良好的休闲健身场所。公园内绿化良好，设有健身步道、儿童游乐设施、老年活动区等多个功能区域。园内还有小型广场，是居民进行广场舞、太极拳等健身活动的场所。公园虽然规模不大，但设施齐全，环境优美，是附近居民日常休闲的重要场所，体现了重庆社区公园建设的成果。"},{name:"九龙工业园",distance:"1km",image:"",description:"现代化工业园区，重庆西部工业基地",details:"九龙工业园是重庆西部重要的工业园区，占地面积约10平方公里，是九龙坡区重要的经济增长点。园区内汇聚了机械制造、电子信息、新材料等多个产业，形成了完整的产业链条。园区基础设施完善，交通便利，为入驻企业提供了良好的发展环境。这里是重庆制造业发展的重要载体，也是推动九龙坡区经济转型升级的重要平台。"}],history:"平安地区寓意平安吉祥，是重庆西部发展的重要区域。",culture:"现代工业与居住的和谐发展。",tips:"适合了解重庆现代工业发展。"},{id:"dadukou",name:"大渡口",order:17,isTransfer:!1,isFeatured:!1,description:"大渡口区政府所在地",detailedDescription:"大渡口站是大渡口区的政治经济中心，历史上是重要的工业基地。",highlight:"区政府",features:["区政府","工业基地","历史文化"],attractions:[{name:"大渡口区政府",distance:"200m",image:"",description:"大渡口区行政中心，政务服务中心",details:"大渡口区政府是大渡口区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。作为大渡口区的政治中心，这里承担着重要的行政管理职能，是推动大渡口区经济社会发展的重要机构。周边配套设施齐全，交通便利，体现了现代化政务服务的理念。"},{name:"重钢博物馆",distance:"500m",image:"",description:"重庆钢铁工业历史博物馆，工业文化遗产",details:"重钢博物馆是展示重庆钢铁工业发展历史的专题博物馆，建在原重庆钢铁厂旧址上。博物馆保留了大量的工业设备和历史文物，全面展示了重庆钢铁工业从无到有、从小到大的发展历程。这里不仅是重庆工业文化的重要载体，也是进行爱国主义教育和工业文明教育的重要基地。博物馆的建设体现了对工业遗产的保护和利用，是重庆城市转型发展的重要见证。"},{name:"义渡公园",distance:"600m",image:"",description:"长江边综合性公园，历史文化主题公园",details:'义渡公园是位于长江边的综合性公园，占地面积约20公顷，以"义渡"历史文化为主题。公园内设有义渡文化广场、历史文化展示区、滨江步道等多个功能区域。园内绿化良好，环境优美，可以欣赏长江美景。公园不仅是市民休闲健身的好去处，也是了解大渡口历史文化的重要场所。这里承载着大渡口"义渡"文化的历史记忆，体现了重庆深厚的历史文化底蕴。'}],history:"大渡口因长江古渡口而得名，是重庆重要的工业基地。",culture:"重庆近现代工业文化的重要载体。",tips:"可以参观重钢博物馆，了解重庆工业发展历史。"},{id:"baijusi",name:"白居寺",order:18,isTransfer:!1,isFeatured:!1,description:"历史文化区域",detailedDescription:"白居寺站因古代白居寺而得名，承载着深厚的历史文化底蕴。",highlight:"历史文化",features:["历史文化","宗教文化"],attractions:[{name:"白居寺遗址",distance:"300m",image:"",description:"唐代古寺遗址，重庆佛教文化遗产",details:"白居寺遗址是重庆重要的佛教文化遗产，始建于唐代，历史悠久。虽然古寺建筑已不复存在，但遗址仍保留着重要的历史价值。这里曾是重庆地区重要的佛教活动场所，承载着深厚的宗教文化底蕴。遗址周围环境清幽，是了解重庆古代佛教文化的重要场所。现在这里建有纪念性建筑，供人们缅怀历史，感受佛教文化的博大精深。"},{name:"大渡口公园",distance:"400m",image:"",description:"区域性综合公园，市民休闲娱乐场所",details:"大渡口公园是大渡口区重要的综合性公园，占地面积约15公顷，为市民提供了良好的休闲娱乐场所。公园内绿化良好，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了大渡口区宜居城市的建设成果。"}],history:"白居寺始建于唐代，是重庆重要的历史文化遗址。",culture:"佛教文化与巴渝文化的融合。",tips:"适合了解重庆古代宗教文化。"},{id:"dajiang",name:"大江",order:19,isTransfer:!1,isFeatured:!1,description:"长江沿岸居住区",detailedDescription:"大江站位于长江沿岸，是重要的居住区域，享有优美的江景。",highlight:"江景",features:["江景","居住区"],attractions:[{name:"长江滨江路",distance:"200m",image:"",description:"长江沿岸景观大道，亲水休闲步道",details:'长江滨江路是沿长江而建的景观大道，全长数公里，是重庆重要的滨江景观带。这里绿树成荫，环境优美，设有多个观景平台和休息区，是市民休闲健身的好去处。滨江路上可以近距离欣赏长江的壮丽风光，感受"一江春水向东流"的磅礴气势。路边还有咖啡厅、茶楼等休闲场所，是约会聊天的理想地点。清晨和傍晚时分，这里聚集了众多晨练和散步的市民，充满了生活气息。'},{name:"大江公园",distance:"300m",image:"",description:"江边主题公园，长江文化展示地",details:"大江公园是以长江文化为主题的综合性公园，占地面积约10公顷，紧邻长江，地理位置优越。公园内设有长江文化展示区、滨江步道、观景台等多个功能区域。园内绿化良好，花草繁茂，可以欣赏到长江的美丽风光。公园不仅是市民休闲娱乐的场所，也是了解长江文化的重要窗口。这里经常举办各种文化活动，是传承和弘扬长江文化的重要载体。"}],history:"大江地区因临近长江而得名，历史悠久。",culture:"长江文化的重要体现。",tips:"可以沿江散步，欣赏长江美景。"},{id:"yudong",name:"鱼洞",order:20,isTransfer:!1,isFeatured:!1,description:"2号线南端终点站",detailedDescription:"鱼洞站是2号线的南端终点站，是巴南区的重要交通枢纽和商业中心。",highlight:"终点站",features:["终点站","商业中心","交通枢纽"],attractions:[{name:"鱼洞老街",distance:"800m",image:"",description:"巴南历史文化街区，传统巴渝建筑群",details:"鱼洞老街是巴南区重要的历史文化街区，保存着大量传统的巴渝建筑和历史遗迹。老街全长约500米，街道两旁是典型的川东民居建筑，青砖黛瓦，古朴典雅。这里曾是重要的商贸集散地，承载着丰富的历史文化内涵。老街内有传统的茶馆、小吃店、手工艺品店等，是体验巴南传统文化的重要场所。近年来经过保护性开发，既保持了历史风貌，又融入了现代元素。"},{name:"巴南区政府",distance:"1.5km",image:"",description:"巴南区行政中心，政务服务中心",details:"巴南区政府是巴南区的行政中心，负责全区的政务管理和公共服务。政府大楼建筑现代化，设施完善，设有多个政务服务窗口，为市民提供便民服务。作为巴南区的政治中心，这里承担着重要的行政管理职能，是推动巴南区经济社会发展的重要机构。周边配套设施齐全，交通便利，体现了现代化政务服务的理念。"},{name:"鱼洞长江大桥",distance:"2km",image:"",description:"跨越长江的现代化大桥，连接两岸交通",details:"鱼洞长江大桥是连接巴南区与主城区的重要桥梁，全长约1500米，是重庆南部地区重要的交通枢纽。大桥采用现代化的桥梁设计，造型优美，是重庆桥梁建设的重要成果。从桥上可以欣赏到长江两岸的美丽风光，是观赏长江景色的好地方。大桥的建成极大地改善了巴南区的交通条件，促进了区域经济的发展，是重庆城市建设的重要标志。"}],history:"鱼洞因古代此地多鱼洞而得名，是巴南区的政治经济中心。",culture:"巴南区政治经济文化中心。",tips:"作为终点站，这里是探索巴南区的起点。"},{id:"xinshancun",name:"新山村",order:21,isTransfer:!1,isFeatured:!1,description:"2号线支线终点站",detailedDescription:"新山村站是2号线支线的终点站，周边以居住区为主。",highlight:"支线终点",features:["支线终点","居住区"],attractions:[{name:"新山村社区",distance:"100m",image:"",description:"现代化居住社区，宜居生活典范",details:"新山村社区是重庆现代化居住社区的典型代表，规划合理，环境优美，配套设施完善。社区内绿化率高，有完善的健身设施、儿童游乐区、老年活动中心等公共设施。社区管理规范，服务贴心，为居民提供了良好的居住环境。这里体现了重庆现代社区建设的成果，是宜居重庆建设的重要体现。社区文化活动丰富，邻里关系和谐，展现了现代城市社区的文明风貌。"},{name:"九龙坡公园",distance:"600m",image:"",description:"区域性综合公园，市民休闲健身场所",details:"九龙坡公园是九龙坡区重要的综合性公园，占地面积约20公顷，为周边居民提供了良好的休闲健身场所。公园内绿树成荫，花草繁茂，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型人工湖和假山景观，环境优美宜人。公园是附近居民晨练、散步、休闲的重要场所，也是社区文化活动的重要载体，体现了重庆宜居城市的建设理念。"}],history:"新山村是重庆城市发展中的新兴居住区。",culture:"现代居住社区的典型代表。",tips:"体验重庆现代社区生活。"},{id:"tiantangbao",name:"天堂堡",order:22,isTransfer:!1,isFeatured:!1,description:"大渡口区居住区，传统社区与现代发展的结合",detailedDescription:"天堂堡站位于大渡口区，是连接新山村和建桥的重要站点，周边以居住区为主，体现了重庆传统社区向现代化发展的转变。",highlight:"居住社区",features:["居住区","社区服务","传统文化"],attractions:[{name:"天堂堡社区",distance:"200m",image:"",description:"传统居住社区，保持重庆本土生活特色",details:"天堂堡社区是大渡口区的传统居住社区，保持着浓厚的重庆本土生活气息。社区内有传统的重庆民居建筑，街道虽然不宽但充满生活气息。这里有传统的小商铺、茶馆、面馆等，是体验重庆市井文化的好地方。社区居民热情好客，保持着重庆人特有的豪爽性格。近年来社区基础设施不断完善，在保持传统特色的同时，也融入了现代化的便民设施。"},{name:"大渡口滨江公园",distance:"800m",image:"",description:"长江边休闲公园，观江景的好去处",details:'大渡口滨江公园沿长江而建，是当地居民休闲娱乐的重要场所。公园内设有观江平台、健身步道、儿童游乐区等设施。从这里可以欣赏到长江的壮丽景色，感受"一江春水向东流"的磅礴气势。公园绿化良好，环境优美，是市民晨练、散步的好去处。夜晚时分，江面波光粼粼，两岸灯火辉煌，景色格外迷人。'},{name:"天堂堡市场",distance:"300m",image:"",description:"传统农贸市场，体验本地生活",details:"天堂堡市场是当地重要的农贸市场，为周边居民提供新鲜的蔬菜、水果、肉类等生活必需品。市场虽然规模不大，但商品齐全，价格实惠，保持着重庆传统市场的特色。这里是了解当地生活文化、体验重庆市井风情的好地方。市场内商贩热情，充满了浓厚的生活气息。"}],history:"天堂堡因地势较高，寓意美好而得名，是大渡口区的传统居住区。",culture:"保持着重庆传统社区文化，体现了巴渝人民的生活智慧。",tips:"适合体验重庆传统社区生活，感受本土文化魅力。"},{id:"jianqiao",name:"建桥",order:23,isTransfer:!1,isFeatured:!1,description:"工业与居住并重的区域，重庆西南部重要节点",detailedDescription:"建桥站位于大渡口区南部，是连接天堂堡和金家湾的重要站点，周边既有工业区也有居住区，体现了重庆工业城市的特色。",highlight:"工业居住",features:["工业区","居住区","交通节点"],attractions:[{name:"建桥工业园",distance:"500m",image:"",description:"现代化工业园区，重庆制造业基地",details:"建桥工业园是大渡口区重要的工业园区，汇聚了机械制造、建材、化工等多个产业。园区基础设施完善，交通便利，为入驻企业提供了良好的发展环境。这里是重庆制造业发展的重要载体，也是推动大渡口区经济发展的重要引擎。园区注重环保和可持续发展，体现了现代工业园区的发展理念。"},{name:"建桥社区文化中心",distance:"300m",image:"",description:"社区文化活动中心，居民精神文化生活场所",details:"建桥社区文化中心是当地居民进行文化活动的重要场所，设有图书阅览室、多功能活动厅、健身房等设施。中心定期举办各种文化活动、技能培训、健康讲座等，丰富了居民的精神文化生活。这里也是社区居民交流互动的重要平台，增进了邻里关系，体现了和谐社区的建设成果。"},{name:"建桥长江大桥观景点",distance:"1km",image:"",description:"观赏长江大桥的最佳位置",details:"建桥长江大桥观景点是观赏附近长江大桥的最佳位置，可以近距离欣赏大桥的雄伟壮观。这里视野开阔，可以看到长江两岸的美丽风光。观景点设有休息设施，是市民休闲观光的好去处。特别是在夕阳西下时，大桥与江水相映成趣，构成了一幅美丽的画卷。"}],history:"建桥因附近有重要桥梁建设而得名，见证了重庆交通建设的发展。",culture:"工业文化与居住文化的融合，体现了重庆城市发展的特色。",tips:"可以了解重庆工业发展历程，感受工业与生活的和谐共存。"},{id:"jinjiawan",name:"金家湾",order:24,isTransfer:!1,isFeatured:!1,description:"巴南区北部重要站点，连接大渡口与巴南的桥梁",detailedDescription:"金家湾站位于巴南区北部，是连接大渡口区和巴南区的重要交通节点，周边以居住区和商业区为主，是巴南区重要的发展区域。",highlight:"交通节点",features:["交通枢纽","商业区","居住区"],attractions:[{name:"金家湾商业街",distance:"400m",image:"",description:"巴南区重要商业街，购物休闲好去处",details:"金家湾商业街是巴南区北部重要的商业街区，汇聚了服装、餐饮、娱乐等多种业态。街道两旁商铺林立，商品丰富，价格实惠，是当地居民购物消费的主要场所。商业街环境整洁，设施完善，经常举办各种促销活动和文化表演。这里不仅是购物的好去处，也是体验巴南区商业文化的重要场所。"},{name:"金家湾公园",distance:"600m",image:"",description:"社区公园，居民休闲健身场所",details:"金家湾公园是一座综合性社区公园，占地面积约8公顷，为周边居民提供了良好的休闲健身场所。公园内绿化良好，设有健身步道、儿童游乐区、老年活动区、篮球场等多个功能区域。园内还有小型广场，是居民进行广场舞、太极拳等健身活动的场所。公园环境优美，是附近居民日常休闲的重要场所。"},{name:"巴南区文化馆金家湾分馆",distance:"500m",image:"",description:"文化活动场所，丰富居民精神生活",details:"巴南区文化馆金家湾分馆是当地重要的文化活动场所，设有展览厅、多功能厅、培训室等设施。分馆定期举办各种文化展览、艺术培训、文艺演出等活动，为居民提供了丰富的精神文化生活。这里也是传承和弘扬巴南区地方文化的重要平台，增进了社区文化建设。"}],history:"金家湾因历史上金姓人家聚居而得名，是巴南区的重要发展区域。",culture:"巴南区文化的重要组成部分，体现了地方文化特色。",tips:"适合购物休闲，体验巴南区的商业文化和社区生活。"},{id:"liujiaba",name:"刘家坝",order:25,isTransfer:!1,isFeatured:!1,description:"巴南区传统居住区，历史文化与现代发展并存",detailedDescription:"刘家坝站位于巴南区，是连接金家湾和白居寺的重要站点，这里保持着传统的居住区特色，同时也在现代化发展中不断进步。",highlight:"传统居住",features:["传统社区","历史文化","居住区"],attractions:[{name:"刘家坝老街",distance:"300m",image:"",description:"巴南传统街区，保存完好的历史建筑",details:"刘家坝老街是巴南区保存较为完好的传统街区之一，街道两旁是典型的川东民居建筑，青砖黛瓦，古朴典雅。老街全长约400米，承载着丰富的历史文化内涵。这里曾是重要的商贸通道，见证了巴南区的历史变迁。老街内有传统的茶馆、小吃店、手工艺品店等，是体验巴南传统文化的重要场所。"},{name:"刘家坝社区广场",distance:"200m",image:"",description:"社区活动中心，居民文化生活场所",details:"刘家坝社区广场是当地居民进行文化活动的重要场所，广场面积约2000平方米，设有舞台、健身器材、休息座椅等设施。这里是居民进行广场舞、太极拳、社区活动的主要场所。广场周围绿化良好，环境优美，是社区居民交流互动的重要平台，体现了和谐社区的建设成果。"},{name:"巴南区历史文化展示馆",distance:"800m",image:"",description:"展示巴南历史文化的重要场所",details:"巴南区历史文化展示馆是展示巴南区历史文化的重要场所，馆内通过文物、图片、模型等形式，全面展示了巴南区从古至今的发展历程。展示馆设有古代文明、近现代发展、民俗文化等多个展区，是了解巴南区历史文化的重要窗口。这里也是进行爱国主义教育和历史文化教育的重要基地。"}],history:"刘家坝因刘姓人家在此建坝而得名，是巴南区的传统居住区。",culture:"保持着深厚的巴南地方文化传统，是历史文化的重要载体。",tips:"适合了解巴南历史文化，体验传统社区生活。"}],he=[{name:"较场口",x:425,y:110,isFeatured:!0},{name:"临江门",x:425,y:80,isFeatured:!0},{name:"黄花园",x:395,y:50,isFeatured:!0},{name:"大溪沟",x:365,y:50,isFeatured:!0},{name:"曾家岩",x:335,y:50,isFeatured:!0},{name:"牛角沱",x:235,y:75,isFeatured:!0},{name:"李子坝",x:205,y:105,isFeatured:!0},{name:"佛图关",x:175,y:135,isFeatured:!0},{name:"大坪",x:145,y:165,isFeatured:!1},{name:"袁家岗",x:145,y:200,isFeatured:!1},{name:"谢家湾",x:145,y:240,isFeatured:!1},{name:"杨家坪",x:145,y:265,isFeatured:!1},{name:"动物园",x:115,y:290,isFeatured:!1},{name:"大堰村",x:85,y:290,isFeatured:!1},{name:"马王场",x:55,y:290,isFeatured:!1},{name:"平安",x:35,y:310,isFeatured:!1},{name:"大渡口",x:25,y:330,isFeatured:!1},{name:"新山村",x:25,y:350,isFeatured:!1},{name:"天堂堡",x:25,y:370,isFeatured:!1},{name:"建桥",x:25,y:390,isFeatured:!1},{name:"金家湾",x:25,y:410,isFeatured:!1},{name:"刘家坝",x:25,y:430,isFeatured:!1},{name:"白居寺",x:25,y:450,isFeatured:!1},{name:"大江",x:50,y:550,isFeatured:!1},{name:"鱼洞",x:115,y:555,isFeatured:!1}],ye=S(e.defineComponent({__name:"cq-line2",setup(a){const i=e.ref(!0),l=e.ref(!1),n=e.ref(null),o=e.ref(null),c=e.ref(!1),r=e.ref(0),s=e.ref(""),d=e.ref(!1),m=e.ref([]),u=e.ref(0),v=e.ref(!1),p=e.ref([{number:1,description:"朝天门至大学城",color:"#E60012"},{number:3,description:"鱼洞至江北机场",color:"#FFD320"},{number:4,description:"民安大道至唐家沱",color:"#8B5A96"},{number:5,description:"园博园至跳磴",color:"#00A0E9"},{number:6,description:"茶园至北碚",color:"#E4007F"},{number:9,description:"回兴至春华大道",color:"#00B04F"},{number:10,description:"王家庄至鲤鱼池",color:"#A05EB5"},{number:18,description:"富华路至跳磴南",color:"#F39800"}]),g=e.ref(pe),h=e.ref(ge),y=e.ref(he),_=()=>{l.value=!l.value},E=e=>{n.value=e},w=()=>{n.value=null},N=()=>{v.value=!0},f=()=>{v.value=!1},k=e=>(t("log","at pages/rail/cq-line2.vue:589",e),X(e)),V=e=>{t("error","at pages/rail/cq-line2.vue:595","图片加载失败:",e);const a=e.target||e.currentTarget;a&&(a.src="/static/images/no-image.svg")},x=()=>{if(!o.value)return!1;if(o.value.image&&""!==o.value.image.trim())return!0;if(o.value.images&&o.value.images.length>0){if(o.value.images.filter(e=>e&&""!==e.trim()).length>0)return!0}return!1},C=()=>{o.value=null,c.value=!1,r.value=0,s.value=""},B=()=>{if(o.value){const e=D();if(e.length>0){const t=Math.min(r.value,e.length-1);s.value=e[t]}else s.value=""}},b=()=>{if(!o.value)return;const e=D();e.length>1&&(r.value=(r.value+1)%e.length,B())},I=()=>{if(!o.value)return;const e=D();e.length>1&&(r.value=0===r.value?e.length-1:r.value-1,B())},D=()=>{if(!o.value)return[];const e=[];if(o.value.images&&o.value.images.length>0){const t=o.value.images.filter(e=>e&&""!==e.trim());e.push(...t)}return 0===e.length&&o.value.image&&""!==o.value.image.trim()&&e.push(o.value.image),e},T=async()=>{if(t("log","at pages/rail/cq-line2.vue:746","开始图片预览，当前图片:",s.value),s.value)try{const e=D(),a=k(s.value);t("log","at pages/rail/cq-line2.vue:754","有效图片列表:",e),t("log","at pages/rail/cq-line2.vue:755","当前图片URL:",a),t("log","at pages/rail/cq-line2.vue:756","当前环境是否为App:",!0);const i=await Promise.all(e.map(async e=>{const a=k(e);try{const e=await(i=a,new Promise(e=>{e(i+"?watermark=true")}));return t("log","at pages/rail/cq-line2.vue:764","水印处理:",a,"->",e),e}catch(l){return t("warn","at pages/rail/cq-line2.vue:767","创建水印图片失败，使用原图:",l),a}var i})),l=e.findIndex(e=>k(e)===a);i.some(e=>e.includes("?watermark=true"));S(e,l>=0?l:0)}catch(e){t("error","at pages/rail/cq-line2.vue:783","预览准备失败:",e),uni.showToast({title:"图片预览失败",icon:"none"})}},S=(e,a)=>{t("log","at pages/rail/cq-line2.vue:794","显示自定义预览:",e,a),m.value=e.map(e=>{const a=k(e);return t("log","at pages/rail/cq-line2.vue:799","处理图片路径:",e,"->",a),a}),u.value=a,t("log","at pages/rail/cq-line2.vue:804","预览图片数组:",m.value),t("log","at pages/rail/cq-line2.vue:805","当前索引:",u.value),t("log","at pages/rail/cq-line2.vue:806","轮播图当前显示的URL:",k(s.value)),d.value=!0},M=()=>{d.value=!1,m.value=[],u.value=0},A=()=>{u.value>0?u.value--:u.value=m.value.length-1},P=()=>{u.value<m.value.length-1?u.value++:u.value=0},F=e=>{t("log","at pages/rail/cq-line2.vue:834","预览图片加载成功"),t("log","at pages/rail/cq-line2.vue:835","图片尺寸:",e.detail.width,"x",e.detail.height),t("log","at pages/rail/cq-line2.vue:836","当前显示的图片URL:",m.value[u.value])},L=e=>{t("error","at pages/rail/cq-line2.vue:843","预览图片加载失败:",e),t("log","at pages/rail/cq-line2.vue:844","当前图片URL:",m.value[u.value])};let $=null;const U=e.ref(0),R=e.ref(0),q=()=>{$.strokeStyle="#00A651",$.lineWidth=6,$.lineCap="round",$.lineJoin="round",$.beginPath(),$.moveTo(115,555),$.quadraticCurveTo(82,565,50,550),$.quadraticCurveTo(25,520,25,450),$.lineTo(25,330),$.quadraticCurveTo(30,320,35,310),$.quadraticCurveTo(45,300,55,290),$.lineTo(115,290),$.quadraticCurveTo(130,280,145,265),$.lineTo(145,165),$.lineTo(235,75),$.quadraticCurveTo(260,50,275,50),$.lineTo(335,50),$.lineTo(395,50),$.quadraticCurveTo(430,60,425,80),$.lineTo(425,110),$.stroke()},j=()=>{y.value.forEach(e=>{e.isFeatured?O(e.x,e.y,8,"#FFD700","#ffffff"):($.beginPath(),$.arc(e.x,e.y,6,0,2*Math.PI),$.fillStyle="#00A651",$.fill(),$.strokeStyle="#ffffff",$.lineWidth=2,$.stroke())})},O=(e,t,a,i,l)=>{const n=a,o=.4*a;$.beginPath();for(let c=0;c<10;c++){const a=c*Math.PI/5,i=c%2==0?n:o,l=e+Math.cos(a-Math.PI/2)*i,r=t+Math.sin(a-Math.PI/2)*i;0===c?$.moveTo(l,r):$.lineTo(l,r)}$.closePath(),$.fillStyle=i,$.fill(),$.strokeStyle=l,$.lineWidth=1,$.stroke()},z=()=>{$.fillStyle="#333333",$.font="12px Arial",$.textAlign="left",y.value.forEach(e=>{let t=e.x+10,a=e.y+5;"曾家岩"===e.name&&(t=e.x-30,a=e.y-10),"大溪沟"===e.name&&(t=e.x-20,a=e.y-10),"平安"===e.name&&(t=e.x-30),"马王场"===e.name&&(t=e.x-30,a=e.y-10),"大堰村"===e.name&&(t=e.x-20,a=e.y-10),$.fillText(e.name,t,a)})},G=e=>{uni.createSelectorQuery().select("#metroCanvas").boundingClientRect(t=>{if(t&&e.detail){const a=e.detail.x-t.left,i=e.detail.y-t.top,l=500/t.width,n=600/t.height,o=a*l,c=i*n;y.value.forEach(e=>{const t=Math.sqrt(Math.pow(o-e.x,2)+Math.pow(c-e.y,2));let a=e.x+10,i=e.y+5;"曾家岩"===e.name&&(a=e.x-30,i=e.y-10),"大溪沟"===e.name&&(a=e.x-20,i=e.y-10),"平安"===e.name&&(a=e.x-30),"马王场"===e.name&&(a=e.x-30,i=e.y-10),"大堰村"===e.name&&(a=e.x-20,i=e.y-10);const l=12*e.name.length;(t<=20||o>=a&&o<=a+l&&c>=i-16&&c<=i)&&(e=>{const t=h.value.find(t=>t.name===e);if(t)E(t);else{const t={id:e.toLowerCase(),name:e,order:0,isTransfer:!1,isFeatured:!1,description:`${e}站`,detailedDescription:`${e}是重庆轨道交通2号线的一个站点。`,features:["轨道交通"],attractions:[],history:"暂无详细历史信息。",culture:"暂无详细文化信息。",tips:"欢迎乘坐重庆轨道交通。"};E(t)}})(e.name)})}}).exec()};return e.onMounted(()=>{uni.setNavigationBarTitle({title:"重庆轨道2号线"}),uni.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:"#00a651"}),setTimeout(()=>{uni.createSelectorQuery().select("#metroCanvas").boundingClientRect(e=>{e&&(U.value=e.width,R.value=e.height,$=uni.createCanvasContext("metroCanvas"),$.scale(U.value/500,R.value/600),$.clearRect(0,0,500,600),$.fillStyle="#ffffff",$.fillRect(0,0,500,600),q(),j(),z(),$.draw())}).exec()},500)}),(a,h)=>{var y,S,$,U,R,q;return e.openBlock(),e.createElementBlock("view",{class:"line2-tour-app"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"header-content"},[e.createElementVNode("text",{class:"header-title"},"轨道游重庆-2号线"),e.createElementVNode("view",{class:"line-badge",onClick:N},[e.createElementVNode("text",{class:"line-number"},"2"),e.createElementVNode("text",{class:"line-name"},"号线"),e.createElementVNode("text",{class:"line-arrow"},"▼")])])]),e.createElementVNode("view",{class:"main-content"},[e.createElementVNode("view",{class:"overview-card"},[e.createElementVNode("view",{class:"overview-header"},[e.createElementVNode("view",{class:"line-info"},[e.createElementVNode("view",{class:"line-color-bar"}),e.createElementVNode("view",{class:"line-details"},[e.createElementVNode("text",{class:"line-title"},[e.createTextVNode("重庆轨道交通"),e.createElementVNode("text",{style:{color:"#00a651"}},"2"),e.createTextVNode("号线")]),e.createElementVNode("text",{class:"line-subtitle"},"穿越山城的绿色长龙")])]),e.createElementVNode("view",{class:"line-stats"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(g.value.length),1),e.createElementVNode("text",{class:"stat-label"},"精选站点")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},"25"),e.createElementVNode("text",{class:"stat-label"},"总站数")])])])]),e.createElementVNode("view",{class:"metro-map-section"},[e.createElementVNode("view",{class:"map-header"},[e.createElementVNode("text",{class:"map-title"},"线路图")]),e.createElementVNode("view",{class:e.normalizeClass(["metro-map",{expanded:i.value}])},[e.createElementVNode("view",{class:"canvas-container"},[e.createElementVNode("canvas",{"canvas-id":"metroCanvas",id:"metroCanvas",class:"metro-canvas",onClick:G})]),l.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"featured-stations"},[e.createElementVNode("view",{class:"stations-header"},[e.createElementVNode("view",{class:"header-left"},[e.createElementVNode("text",{class:"stations-title"},"精选站点推荐"),e.createElementVNode("text",{class:"stations-count"},e.toDisplayString(g.value.length)+"个站点",1)]),e.createElementVNode("button",{class:"collapse-btn",onClick:_}," 收起推荐 ")]),e.createElementVNode("scroll-view",{class:"stations-scroll","scroll-y":"true",style:e.normalizeStyle({maxHeight:g.value.length>5?"400rpx":"auto"})},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value,t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"station-item",onClick:e=>E(t)},[e.createElementVNode("view",{class:"station-icon"},[e.createElementVNode("view",{class:"station-star"},"⭐"),t.isTransfer?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-badge"},"换乘")):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"station-content"},[e.createElementVNode("view",{class:"station-header"},[e.createElementVNode("text",{class:"station-name"},e.toDisplayString(t.name),1),t.highlight?(e.openBlock(),e.createElementBlock("text",{key:0,class:"station-highlight"},e.toDisplayString(t.highlight),1)):e.createCommentVNode("",!0)]),e.createElementVNode("text",{class:"station-desc"},e.toDisplayString(t.description),1),t.features&&t.features.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-features"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.features.slice(0,3),t=>(e.openBlock(),e.createElementBlock("text",{key:t,class:"feature-tag"},e.toDisplayString(t),1))),128))])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"station-arrow"},[e.createElementVNode("text",null,">")])],8,["onClick"]))),128))],4)])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"featured-stations-mini"},[e.createElementVNode("view",{class:"mini-header"},[e.createElementVNode("text",{class:"mini-title"},"精选站点"),e.createElementVNode("button",{class:"expand-btn",onClick:_}," 展开推荐 ")]),e.createElementVNode("view",{class:"mini-stations"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value.slice(0,5),t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"mini-station-item",onClick:e=>E(t)},[e.createElementVNode("view",{class:"mini-station-star"},"⭐"),e.createElementVNode("text",{class:"mini-station-name"},e.toDisplayString(t.name),1)],8,["onClick"]))),128)),g.value.length>5?(e.openBlock(),e.createElementBlock("view",{key:0,class:"more-indicator"},[e.createElementVNode("text",null,"+"+e.toDisplayString(g.value.length-5)+"个",1)])):e.createCommentVNode("",!0)])]))],2)]),e.createElementVNode("view",{class:"line-info-section"},[e.createElementVNode("view",{class:"info-card"},[e.createElementVNode("text",{class:"info-title"},"线路信息"),e.createElementVNode("view",{class:"info-grid"},[e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"运营时间"),e.createElementVNode("text",{class:"info-value"},"6:00 - 23:00")]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"全程时间"),e.createElementVNode("text",{class:"info-value"},"约60分钟")]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"票价"),e.createElementVNode("text",{class:"info-value"},"2-7元")]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"车辆类型"),e.createElementVNode("text",{class:"info-value"},"跨座式单轨")])])])])]),n.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-modal",onClick:w},[e.createElementVNode("view",{class:"modal-content",onClick:h[0]||(h[0]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("view",{class:"header-left"},[e.createElementVNode("view",{class:"station-title-area"},[e.createElementVNode("text",{class:"modal-title"},e.toDisplayString(n.value.name),1),e.createElementVNode("view",{class:"station-badges"},[e.createElementVNode("view",{class:e.normalizeClass(["station-type-badge",{transfer:n.value.isTransfer,featured:n.value.isFeatured}])},[n.value.isTransfer?(e.openBlock(),e.createElementBlock("text",{key:0},"换乘站")):n.value.isFeatured?(e.openBlock(),e.createElementBlock("text",{key:1},"精选站点")):(e.openBlock(),e.createElementBlock("text",{key:2},"普通站"))],2),n.value.highlight?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-highlight"},[e.createElementVNode("text",null,e.toDisplayString(n.value.highlight),1)])):e.createCommentVNode("",!0)])])]),e.createElementVNode("button",{class:"close-btn",onClick:w},[e.createElementVNode("text",{class:"modal-close-icon"},"×")])]),e.createElementVNode("view",{class:"modal-body"},[n.value.image?(e.openBlock(),e.createElementBlock("view",{key:0,class:"station-image"},[e.createElementVNode("image",{src:k(n.value.image),alt:n.value.name,mode:"aspectFill",class:"station-main-image",onError:V},null,40,["src","alt"]),e.createElementVNode("view",{class:"image-overlay"},[e.createElementVNode("text",{class:"image-title"},e.toDisplayString(n.value.name)+"站",1)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"station-details"},[e.createElementVNode("view",{class:"station-intro-section"},[e.createElementVNode("text",{class:"section-label"},"站点介绍"),e.createElementVNode("text",{class:"station-intro"},e.toDisplayString(n.value.detailedDescription),1)]),n.value.features&&n.value.features.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-features"},[e.createElementVNode("text",{class:"features-title"},"站点特色"),e.createElementVNode("view",{class:"feature-tags"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.features,t=>(e.openBlock(),e.createElementBlock("text",{key:t,class:"feature-tag"},e.toDisplayString(t),1))),128))])])):e.createCommentVNode("",!0),n.value.attractions&&n.value.attractions.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"modal-attractions"},[e.createElementVNode("text",{class:"attractions-title"},"周边景点/设施"),e.createElementVNode("view",{class:"attraction-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.attractions,t=>(e.openBlock(),e.createElementBlock("view",{key:t.name,class:"attraction-item",onClick:e=>(e=>{o.value=e,c.value=!0,r.value=0,B()})(t)},[e.createElementVNode("view",{class:"attraction-image"},[t.image?(e.openBlock(),e.createElementBlock("image",{key:0,src:k(t.image),alt:t.name,class:"attraction-thumbnail",mode:"aspectFill",onError:V},null,40,["src","alt"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-image-placeholder"},[e.createElementVNode("text",{class:"no-image-icon"},"🖼️"),e.createElementVNode("text",{class:"no-image-text"},"暂无图片")]))]),e.createElementVNode("view",{class:"attraction-info"},[e.createElementVNode("view",{class:"attraction-header"},[e.createElementVNode("text",{class:"attraction-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"attraction-distance"},e.toDisplayString(t.distance),1)]),t.description?(e.openBlock(),e.createElementBlock("text",{key:0,class:"attraction-description"},e.toDisplayString(t.description),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"attraction-arrow"},[e.createElementVNode("text",null,">")])],8,["onClick"]))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"detail-sections"},[n.value.history?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-section"},[e.createElementVNode("text",{class:"section-title"},"历史背景"),e.createElementVNode("text",{class:"section-content"},e.toDisplayString(n.value.history),1)])):e.createCommentVNode("",!0),n.value.culture?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-section"},[e.createElementVNode("text",{class:"section-title"},"文化特色"),e.createElementVNode("text",{class:"section-content"},e.toDisplayString(n.value.culture),1)])):e.createCommentVNode("",!0),n.value.tips?(e.openBlock(),e.createElementBlock("view",{key:2,class:"detail-section"},[e.createElementVNode("text",{class:"section-title"},"游览贴士"),e.createElementVNode("text",{class:"section-content"},e.toDisplayString(n.value.tips),1)])):e.createCommentVNode("",!0)])])])])])):e.createCommentVNode("",!0),c.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"modal-overlay attraction-modal-overlay",onClick:C},[e.createElementVNode("view",{class:"modal-content attraction-modal-content",onClick:h[1]||(h[1]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},e.toDisplayString(null==(y=o.value)?void 0:y.name),1),e.createElementVNode("button",{class:"close-btn",onClick:C},[e.createElementVNode("text",{class:"modal-close-icon"},"×")])]),e.createElementVNode("view",{class:"modal-body attraction-modal-body"},[x()?(e.openBlock(),e.createElementBlock("view",{key:0,class:"attraction-gallery"},[e.createElementVNode("view",{class:"gallery-main"},[e.createElementVNode("image",{src:k(s.value),alt:(null==(S=o.value)?void 0:S.name)||"景点图片",class:"gallery-main-image",mode:"aspectFill",onClick:T,onError:V},null,40,["src","alt"]),e.createElementVNode("view",{class:"copyright-watermark"},[e.createElementVNode("text",{class:"watermark-icon"},"🚫"),e.createElementVNode("text",{class:"watermark-text"},"版权保护，禁止转载")]),D().length>1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"gallery-nav"},[e.createElementVNode("button",{class:"nav-btn prev-btn",onClick:e.withModifiers(I,["stop"])},[e.createElementVNode("text",{class:"nav-icon"},"‹")]),e.createElementVNode("button",{class:"nav-btn next-btn",onClick:e.withModifiers(b,["stop"])},[e.createElementVNode("text",{class:"nav-icon"},"›")])])):e.createCommentVNode("",!0)]),D().length>1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"gallery-indicators"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(D(),(t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["indicator",{active:a===r.value}]),onClick:e=>(e=>{r.value=e,B()})(a)},null,10,["onClick"]))),128))])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"attraction-details"},[e.createElementVNode("view",{class:"attraction-basic-info"},[e.createElementVNode("view",{class:"attraction-distance-badge"}," 距离站点 "+e.toDisplayString(null==($=o.value)?void 0:$.distance),1)]),(null==(U=o.value)?void 0:U.description)?(e.openBlock(),e.createElementBlock("text",{key:0,class:"attraction-intro"},e.toDisplayString(o.value.description),1)):e.createCommentVNode("",!0),(null==(R=o.value)?void 0:R.details)?(e.openBlock(),e.createElementBlock("view",{key:1,class:"attraction-detail-content"},[e.createElementVNode("text",{class:"detail-title"},"详细介绍"),e.createElementVNode("text",{class:"detail-text"},e.toDisplayString(o.value.details),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"attraction-practical-info"},[e.createElementVNode("text",{class:"practical-title"},"实用信息"),e.createElementVNode("view",{class:"info-grid"},[e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"步行时间"),e.createElementVNode("text",{class:"info-value"},"约"+e.toDisplayString(Math.ceil(parseInt((null==(q=o.value)?void 0:q.distance)||"0")/80))+"分钟",1)]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"建议游览"),e.createElementVNode("text",{class:"info-value"},"30-60分钟")])])])])])])])):e.createCommentVNode("",!0),d.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"custom-preview-modal",onClick:M},[e.createElementVNode("view",{class:"custom-preview-container",onClick:h[2]||(h[2]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"custom-preview-image-wrapper"},[e.createElementVNode("image",{src:m.value[u.value],class:"custom-preview-image",mode:"aspectFit",style:{width:"100%",height:"100%",minHeight:"200px"},onError:L,onLoad:F},null,40,["src"]),e.createElementVNode("view",{class:"custom-preview-watermark"},[e.createElementVNode("text",{class:"watermark-text"},"版权保护，禁止转载")])]),m.value.length>1?(e.openBlock(),e.createElementBlock("view",{key:0,class:"custom-preview-nav"},[e.createElementVNode("button",{class:"custom-nav-btn prev",onClick:A},[e.createElementVNode("text",{class:"nav-icon"},"‹")]),e.createElementVNode("button",{class:"custom-nav-btn next",onClick:P},[e.createElementVNode("text",{class:"nav-icon"},"›")])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"custom-preview-close",onClick:M},[e.createElementVNode("text",{class:"close-icon"},"×")]),m.value.length>1?(e.openBlock(),e.createElementBlock("view",{key:1,class:"custom-preview-indicators"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.value,(t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["custom-indicator",{active:a===u.value}]),onClick:e=>u.value=a},null,10,["onClick"]))),128))])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),v.value?(e.openBlock(),e.createElementBlock("view",{key:3,class:"line-selector-modal",onClick:f},[e.createElementVNode("view",{class:"line-selector-container",onClick:h[3]||(h[3]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"line-selector-header"},[e.createElementVNode("text",{class:"selector-title"},"选择线路"),e.createElementVNode("view",{class:"selector-close",onClick:f},[e.createElementVNode("text",{class:"selector-close-icon"},"×")])]),e.createElementVNode("view",{class:"line-list"},[e.createElementVNode("view",{class:"line-item current"},[e.createElementVNode("view",{class:"line-info"},[e.createElementVNode("view",{class:"line-badge-small line-2"},[e.createElementVNode("text",{class:"line-number-small"},"2")]),e.createElementVNode("view",{class:"line-details-small"},[e.createElementVNode("text",{class:"line-name-small"},"2号线"),e.createElementVNode("text",{class:"line-desc"},"穿越山城的绿色长龙")])]),e.createElementVNode("view",{class:"line-status current-status"},[e.createElementVNode("text",{class:"status-text"},"当前")])]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p.value,a=>(e.openBlock(),e.createElementBlock("view",{key:a.number,class:"line-item",onClick:e=>(e=>{t("log","at pages/rail/cq-line2.vue:578","选择线路:",e),uni.showToast({title:`${e.number}号线建设中...`,icon:"none",duration:2e3}),f()})(a)},[e.createElementVNode("view",{class:"line-info"},[e.createElementVNode("view",{class:e.normalizeClass(["line-badge-small",`line-${a.number}`])},[e.createElementVNode("text",{class:"line-number-small"},e.toDisplayString(a.number),1)],2),e.createElementVNode("view",{class:"line-details-small"},[e.createElementVNode("text",{class:"line-name-small"},e.toDisplayString(a.number)+"号线",1),e.createElementVNode("text",{class:"line-desc"},e.toDisplayString(a.description),1)])]),e.createElementVNode("view",{class:"line-status"},[e.createElementVNode("text",{class:"status-text"},"建设中...")])],8,["onClick"]))),128))])])])):e.createCommentVNode("",!0)])}}}),[["__scopeId","data-v-ed74583e"]]);function _e(e,t={}){const a=function(e){const t=[];for(const[a,i]of Object.entries(e))if(null!=i&&""!==i){const e=encodeURIComponent(String(i));t.push(`${a}=${e}`)}return t.join("&")}(t);if(!a)return e;const i=e.includes("?")?"&":"?";return`${e}${i}${a}`}const Ee=y(),we=e=>({[F.GUEST]:"游客",[F.DISTRICT_ADMIN]:"区县管理员",[F.CITY_ADMIN]:"市级管理员",[F.PROVINCE_ADMIN]:"省级管理员",[F.SUPER_ADMIN]:"超级管理员"}[e]||e),Ne=e=>({0:"未知",1:"男",2:"女"}[e]||"未知"),fe=async e=>{var a;try{const t=uni.getStorageSync("access_token"),i=_e(`${Ee}/users/search/guests`,e),l=await uni.request({url:i,method:"GET",header:{Authorization:`Bearer ${t}`}});if(200===l.statusCode)return l.data;throw new Error((null==(a=l.data)?void 0:a.detail)||"搜索游客用户失败")}catch(i){throw t("error","at api/users.ts:230","搜索游客用户失败:",i),i}},ke=S(e.defineComponent({__name:"users",setup(a){e.ref(),e.ref();const i=e.ref(!1),l=e.ref([]),n=e.ref(0),o=e.ref(1),c=e.computed(()=>l.value.length<n.value),r=e.ref(null),s=e.ref(!1),d=e.ref(!1),m=e.ref(!1),u=e.reactive({page:1,page_size:20,search:"",role:void 0,province_id:void 0,city_id:void 0,district_id:void 0}),v=e.ref(0),p=[{label:"全部角色",value:void 0},{label:"游客",value:F.GUEST},{label:"区县管理员",value:F.DISTRICT_ADMIN},{label:"市级管理员",value:F.CITY_ADMIN},{label:"省级管理员",value:F.PROVINCE_ADMIN},{label:"超级管理员",value:F.SUPER_ADMIN}],g=e.reactive({userId:0,roleIndex:0,provinceIndex:-1,cityIndex:-1,districtIndex:-1}),h=e.computed(()=>{const e=O.currentUser;if(!e)return[];const t=[{label:"游客",value:F.GUEST},{label:"区县管理员",value:F.DISTRICT_ADMIN},{label:"市级管理员",value:F.CITY_ADMIN},{label:"省级管理员",value:F.PROVINCE_ADMIN},{label:"超级管理员",value:F.SUPER_ADMIN}];return e.role===F.SUPER_ADMIN?t:e.role===F.PROVINCE_ADMIN?t.filter(e=>e.value!==F.SUPER_ADMIN&&e.value!==F.PROVINCE_ADMIN):e.role===F.CITY_ADMIN?t.filter(e=>![F.SUPER_ADMIN,F.PROVINCE_ADMIN,F.CITY_ADMIN].includes(e.value)):e.role===F.DISTRICT_ADMIN?t.filter(e=>[F.GUEST,F.DISTRICT_ADMIN].includes(e.value)):[{label:"游客",value:F.GUEST}]}),y=e.computed(()=>{if(g.roleIndex<0||g.roleIndex>=h.value.length)return!1;const e=h.value[g.roleIndex];return e&&[F.DISTRICT_ADMIN,F.CITY_ADMIN,F.PROVINCE_ADMIN].includes(e.value)}),_=e.computed(()=>{if(g.roleIndex<0||g.roleIndex>=h.value.length)return!1;const e=h.value[g.roleIndex];return e&&[F.CITY_ADMIN,F.DISTRICT_ADMIN].includes(e.value)&&V.value.length>0}),f=e.computed(()=>{if(g.roleIndex<0||g.roleIndex>=h.value.length)return!1;const e=h.value[g.roleIndex];return e&&e.value===F.DISTRICT_ADMIN&&x.value.length>0}),k=e.ref([]),V=e.ref([]),x=e.ref([]),C=e.reactive({phone:"",page:1,page_size:20}),B=e.ref([]),b=e.ref(0),I=e.ref(1),D=e.ref(!1),T=e.ref(!1),S=e.computed(()=>B.value.length<b.value),M=e.ref([]),A=e.reactive({ancient_books:!1,paintings:!1,archives:!1,videos:!1}),P=e.computed(()=>O.currentUser);let L;const $=()=>{clearTimeout(L),L=setTimeout(()=>{q()},500)};e.onMounted(async()=>{await U(),await ae(),await ie()});const U=async(e=!1)=>{try{i.value=!0,e||(o.value=1,u.page=1);const a=await(async(e={})=>{var a;try{const t=uni.getStorageSync("access_token"),i=_e(`${Ee}/users/`,e),l=await uni.request({url:i,method:"GET",header:{Authorization:`Bearer ${t}`}});if(200===l.statusCode)return l.data;throw new Error((null==(a=l.data)?void 0:a.detail)||"获取用户列表失败")}catch(i){throw t("error","at api/users.ts:89","获取用户列表失败:",i),i}})(u);e?l.value.push(...a.users):l.value=a.users,n.value=a.total}catch(a){t("error","at pages/admin/users.vue:589","加载用户列表失败:",a),uni.showToast({title:"加载失败",icon:"none"})}finally{i.value=!1}},R=async()=>{!i.value&&c.value&&(o.value++,u.page=o.value,await U(!0))},q=async()=>{await U()},j=e=>{v.value=parseInt(e.detail.value),u.role=p[v.value].value,q()},z=()=>{uni.showToast({title:"功能开发中",icon:"none"})},G=e=>e.nickname&&e.nickname.length>0?e.nickname.charAt(0).toUpperCase():"用",H=e=>{const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},Y=e=>{const t=new Date(e);return`${H(e)} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},W=()=>{d.value=!1,r.value=null},X=async e=>{g.userId=e.id;const t=h.value.findIndex(t=>t.value===e.role);g.roleIndex=t>=0?t:0,g.provinceIndex=-1,g.cityIndex=-1,g.districtIndex=-1,V.value=[],x.value=[];const a=O.currentUser;if(a){if(a.role===F.PROVINCE_ADMIN&&a.province_id){const e=k.value.findIndex(e=>e.province_id===a.province_id);e>=0&&(g.provinceIndex=e,V.value=await w(a.province_id))}else if(a.role===F.CITY_ADMIN&&a.province_id&&a.city_id){const e=k.value.findIndex(e=>e.province_id===a.province_id);if(e>=0){g.provinceIndex=e,V.value=await w(a.province_id);const t=V.value.findIndex(e=>e.city_id===a.city_id);t>=0&&(g.cityIndex=t,x.value=await N(a.province_id,a.city_id))}}else if(a.role===F.DISTRICT_ADMIN&&a.province_id&&a.city_id&&a.district_id){const e=k.value.findIndex(e=>e.province_id===a.province_id);if(e>=0){g.provinceIndex=e,V.value=await w(a.province_id);const t=V.value.findIndex(e=>e.city_id===a.city_id);if(t>=0){g.cityIndex=t,x.value=await N(a.province_id,a.city_id);const e=x.value.findIndex(e=>e.district_id===a.district_id);e>=0&&(g.districtIndex=e)}}}else if(e.province_id){const t=k.value.findIndex(t=>t.province_id===e.province_id);t>=0&&(g.provinceIndex=t,ne(e))}r.value=e,a.role===F.SUPER_ADMIN&&e.role!==F.GUEST&&await le(e.id),s.value=!0}},J=()=>{s.value=!1},K=async e=>{g.roleIndex=parseInt(e.detail.value);const t=O.currentUser;if(t)if(t.role===F.PROVINCE_ADMIN&&t.province_id){const e=k.value.findIndex(e=>e.province_id===t.province_id);e>=0&&(g.provinceIndex=e,0===V.value.length&&(V.value=await w(t.province_id))),g.cityIndex=-1,g.districtIndex=-1,x.value=[]}else if(t.role===F.CITY_ADMIN&&t.province_id&&t.city_id){const e=k.value.findIndex(e=>e.province_id===t.province_id);if(e>=0){g.provinceIndex=e,0===V.value.length&&(V.value=await w(t.province_id));const a=V.value.findIndex(e=>e.city_id===t.city_id);a>=0&&(g.cityIndex=a,0===x.value.length&&(x.value=await N(t.province_id,t.city_id)))}g.districtIndex=-1}else if(t.role===F.DISTRICT_ADMIN&&t.province_id&&t.city_id&&t.district_id){const e=k.value.findIndex(e=>e.province_id===t.province_id);if(e>=0){g.provinceIndex=e,0===V.value.length&&(V.value=await w(t.province_id));const a=V.value.findIndex(e=>e.city_id===t.city_id);if(a>=0){g.cityIndex=a,0===x.value.length&&(x.value=await N(t.province_id,t.city_id));const e=x.value.findIndex(e=>e.district_id===t.district_id);e>=0&&(g.districtIndex=e)}}}else g.provinceIndex=-1,g.cityIndex=-1,g.districtIndex=-1,V.value=[],x.value=[]},Q=async e=>{if(g.provinceIndex=parseInt(e.detail.value),g.cityIndex=-1,g.districtIndex=-1,x.value=[],g.provinceIndex>=0){const e=k.value[g.provinceIndex];V.value=await w(e.province_id)}else V.value=[]},Z=async e=>{if(g.cityIndex=parseInt(e.detail.value),g.districtIndex=-1,g.cityIndex>=0&&g.provinceIndex>=0){const e=k.value[g.provinceIndex],t=V.value[g.cityIndex];x.value=await N(e.province_id,t.city_id)}else x.value=[]},ee=e=>{g.districtIndex=parseInt(e.detail.value)},te=async()=>{try{if(g.roleIndex<0||g.roleIndex>=h.value.length)return void uni.showToast({title:"请选择角色",icon:"none"});const a=h.value[g.roleIndex];if(!a)return void uni.showToast({title:"请选择角色",icon:"none"});const i={role:a.value,province_id:void 0,city_id:void 0,district_id:void 0};if(a.value===F.PROVINCE_ADMIN){if(g.provinceIndex<0)return void uni.showToast({title:"请选择省份",icon:"none"});i.province_id=k.value[g.provinceIndex].province_id,i.city_id=void 0,i.district_id=void 0}else if(a.value===F.CITY_ADMIN){if(g.provinceIndex<0)return void uni.showToast({title:"请选择省份",icon:"none"});if(g.cityIndex<0)return void uni.showToast({title:"请选择城市",icon:"none"});i.province_id=k.value[g.provinceIndex].province_id,i.city_id=V.value[g.cityIndex].city_id,i.district_id=void 0}else if(a.value===F.DISTRICT_ADMIN){if(g.provinceIndex<0)return void uni.showToast({title:"请选择省份",icon:"none"});if(g.cityIndex<0)return void uni.showToast({title:"请选择城市",icon:"none"});if(g.districtIndex<0)return void uni.showToast({title:"请选择区县",icon:"none"});i.province_id=k.value[g.provinceIndex].province_id,i.city_id=V.value[g.cityIndex].city_id,i.district_id=x.value[g.districtIndex].district_id}uni.showLoading({title:"保存中..."}),t("log","at pages/admin/users.vue:1072",i);const n=await(async(e,a)=>{var i;try{const t=uni.getStorageSync("access_token"),l=await uni.request({url:`${Ee}/users/${e}/role`,method:"PUT",data:a,header:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}});if(200===l.statusCode)return l.data;throw new Error((null==(i=l.data)?void 0:i.detail)||"更新用户角色失败")}catch(l){throw t("error","at api/users.ts:137","更新用户角色失败:",l),l}})(g.userId,i),o=O.currentUser;if((null==o?void 0:o.role)===F.SUPER_ADMIN&&a.value!==F.GUEST)try{await(async(e,a)=>{var i;try{const t=uni.getStorageSync("access_token"),l=await uni.request({url:`${Ee}/users/${e}/module-permissions`,method:"PUT",data:a,header:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}});if(200===l.statusCode)return l.data;throw new Error((null==(i=l.data)?void 0:i.detail)||"更新用户模块权限失败")}catch(l){throw t("error","at api/users.ts:301","更新用户模块权限失败:",l),l}})(g.userId,{module_permissions:{...A}})}catch(e){t("error","at pages/admin/users.vue:1086","更新模块权限失败:",e),uni.showToast({title:"角色更新成功，但模块权限更新失败",icon:"none",duration:3e3})}const c=l.value.findIndex(e=>e.id===g.userId);-1!==c&&(l.value[c]=n);const r=B.value.findIndex(e=>e.id===g.userId);-1!==r&&(B.value[r]=n),s.value=!1,uni.showToast({title:"角色更新成功",icon:"success"})}catch(e){t("error","at pages/admin/users.vue:1120","更新用户角色失败:",e),uni.showToast({title:"更新失败",icon:"none"})}finally{uni.hideLoading()}},ae=async()=>{try{k.value=await E()}catch(e){t("error","at pages/admin/users.vue:1135","加载省份列表失败:",e)}},ie=async()=>{try{const e=await(async()=>{var e;try{const t=uni.getStorageSync("access_token"),a=await uni.request({url:`${Ee}/users/module-permissions/available`,method:"GET",header:{Authorization:`Bearer ${t}`}});if(200===a.statusCode)return a.data;throw new Error((null==(e=a.data)?void 0:e.detail)||"获取可用模块列表失败")}catch(a){throw t("error","at api/users.ts:253","获取可用模块列表失败:",a),a}})();M.value=e.modules}catch(e){t("error","at pages/admin/users.vue:1145","加载可用模块列表失败:",e)}},le=async e=>{try{const a=await(async e=>{var a;try{const t=uni.getStorageSync("access_token"),i=await uni.request({url:`${Ee}/users/${e}/module-permissions`,method:"GET",header:{Authorization:`Bearer ${t}`}});if(200===i.statusCode)return i.data;throw new Error((null==(a=i.data)?void 0:a.detail)||"获取用户模块权限失败")}catch(i){throw t("error","at api/users.ts:276","获取用户模块权限失败:",i),i}})(e);Object.assign(A,a.module_permissions)}catch(a){t("error","at pages/admin/users.vue:1156","加载用户模块权限失败:",a),Object.assign(A,{ancient_books:!1,paintings:!1,archives:!1,videos:!1})}},ne=async e=>{try{if(e.province_id&&(V.value=await w(e.province_id),e.city_id)){const t=V.value.findIndex(t=>t.city_id===e.city_id);if(t>=0&&(g.cityIndex=t,e.district_id)){x.value=await N(e.province_id,e.city_id);const t=x.value.findIndex(t=>t.district_id===e.district_id);t>=0&&(g.districtIndex=t)}}}catch(a){t("error","at pages/admin/users.vue:1199","加载区域数据失败:",a)}},oe=()=>{m.value=!0,C.phone="",C.page=1,B.value=[],b.value=0,I.value=1,T.value=!1},ce=()=>{m.value=!1},re=async()=>{if(C.phone)try{D.value=!0,I.value=1,C.page=1;const e=await fe(C);B.value=e.users,b.value=e.total,T.value=!0,0===e.users.length&&uni.showToast({title:"未找到符合条件的用户",icon:"none"})}catch(e){t("error","at pages/admin/users.vue:1247","搜索游客用户失败:",e),uni.showToast({title:"搜索失败",icon:"none"})}finally{D.value=!1}else uni.showToast({title:"请输入手机号",icon:"none"})},se=async()=>{if(!D.value&&S.value)try{D.value=!0,I.value++,C.page=I.value;const e=await fe(C);B.value.push(...e.users)}catch(e){t("error","at pages/admin/users.vue:1269","加载更多游客搜索结果失败:",e),uni.showToast({title:"加载失败",icon:"none"})}finally{D.value=!1}},de=e.computed(()=>{const e=O.currentUser;return!!e&&[F.PROVINCE_ADMIN,F.CITY_ADMIN,F.DISTRICT_ADMIN].includes(e.role)}),me=e.computed(()=>{const e=O.currentUser;return!!e&&[F.CITY_ADMIN,F.DISTRICT_ADMIN].includes(e.role)}),ue=e.computed(()=>{const e=O.currentUser;return!!e&&e.role===F.DISTRICT_ADMIN}),ve=e=>{if(e.role===F.GUEST||e.role===F.SUPER_ADMIN)return"";let t="";return e.province_name?(t=e.province_name,e.city_name&&(t+=` ${e.city_name}`),e.district_name&&(t+=` ${e.district_name}`),t):e.province_id?"未知地区":"未设置地区"},pe=e=>!(!e.module_permissions||e.role===F.GUEST)&&(e.module_permissions.ancient_books||e.module_permissions.paintings||e.module_permissions.archives||e.module_permissions.videos);return(t,a)=>{var n,o,E,w,N,I;return e.openBlock(),e.createElementBlock("view",{class:"users-management"},[e.createElementVNode("view",{class:"search-section"},[e.createElementVNode("view",{class:"search-bar"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>u.search=e),class:"search-input",placeholder:"搜索昵称或手机号",onInput:$},null,544),[[e.vModelText,u.search]]),e.createElementVNode("button",{onClick:q,class:"search-btn"},"搜索"),e.createElementVNode("button",{onClick:oe,class:"guest-search-btn"},"搜索游客")]),e.createElementVNode("view",{class:"filter-bar"},[e.createElementVNode("picker",{value:v.value,range:p,"range-key":"label",onChange:j},[e.createElementVNode("view",{class:"filter-item"}," 角色："+e.toDisplayString((null==(n=p[v.value])?void 0:n.label)||"全部"),1)],40,["value"]),e.createElementVNode("view",{class:"filter-item",onClick:z}," 区域："+e.toDisplayString("全部区域"),1)])]),e.createElementVNode("view",{class:"user-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"user-item",onClick:e=>(e=>{r.value=e,d.value=!0})(t)},[e.createElementVNode("view",{class:"user-avatar"},[t.avatar_url?(e.openBlock(),e.createElementBlock("image",{key:0,src:t.avatar_url,class:"avatar-img"},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"avatar-placeholder"},[e.createElementVNode("text",{class:"avatar-text"},e.toDisplayString(G(t)),1)]))]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"user-name"},e.toDisplayString(t.nickname||"未设置昵称"),1),e.createElementVNode("view",{class:"user-role"},e.toDisplayString(e.unref(we)(t.role)),1),ve(t)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"user-region"},e.toDisplayString(ve(t)),1)):e.createCommentVNode("",!0),t.phone?(e.openBlock(),e.createElementBlock("view",{key:1,class:"user-phone"},e.toDisplayString(t.phone),1)):e.createCommentVNode("",!0),t.module_permissions&&"guest"!==t.role?(e.openBlock(),e.createElementBlock("view",{key:2,class:"user-permissions"},[e.createElementVNode("text",{class:"permission-label"},"模块权限："),e.createElementVNode("view",{class:"permission-tags"},[t.module_permissions.ancient_books?(e.openBlock(),e.createElementBlock("text",{key:0,class:"permission-tag active"},"古籍")):e.createCommentVNode("",!0),t.module_permissions.paintings?(e.openBlock(),e.createElementBlock("text",{key:1,class:"permission-tag active"},"书画")):e.createCommentVNode("",!0),t.module_permissions.archives?(e.openBlock(),e.createElementBlock("text",{key:2,class:"permission-tag active"},"档案")):e.createCommentVNode("",!0),t.module_permissions.videos?(e.openBlock(),e.createElementBlock("text",{key:3,class:"permission-tag active"},"影像")):e.createCommentVNode("",!0),pe(t)?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("text",{key:4,class:"permission-tag inactive"},"无权限"))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"user-meta"}," ID: "+e.toDisplayString(t.id)+" | "+e.toDisplayString(t.is_active?"正常":"已禁用")+" | "+e.toDisplayString(H(t.created_at)),1)]),e.createElementVNode("view",{class:"user-actions"},[e.createElementVNode("button",{onClick:e.withModifiers(e=>X(t),["stop"]),class:"auth-btn"}," 授权管理 ",8,["onClick"])])],8,["onClick"]))),128)),c.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"load-more",onClick:R},[e.createElementVNode("text",null,e.toDisplayString(i.value?"加载中...":"加载更多"),1)])):e.createCommentVNode("",!0),i.value||0!==l.value.length?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-state"},[e.createElementVNode("text",null,"暂无用户数据")]))]),d.value&&r.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"popup-overlay",onClick:W},[e.createElementVNode("view",{class:"user-detail-popup",onClick:a[1]||(a[1]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-header"},[e.createElementVNode("text",{class:"popup-title"},"用户详情"),e.createElementVNode("text",{onClick:W,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"detail-content"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"用户ID:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(r.value.id),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"昵称:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(r.value.nickname||"未设置"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"手机号:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(r.value.phone||"未绑定"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"性别:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(e.unref(Ne)(r.value.gender)),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"角色:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(e.unref(we)(r.value.role)),1)]),ve(r.value)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"管辖地区:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(ve(r.value)),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"状态:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(r.value.is_active?"正常":"已禁用"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"注册时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(Y(r.value.created_at)),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"最后登录:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(r.value.last_login_at?Y(r.value.last_login_at):"未登录"),1)])])])])):e.createCommentVNode("",!0),s.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"popup-overlay role-edit-overlay",onClick:J},[e.createElementVNode("view",{class:"role-edit-popup",onClick:a[2]||(a[2]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-title"},"编辑用户角色"),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"角色"),e.createElementVNode("picker",{value:g.roleIndex,range:h.value,"range-key":"label",onChange:K},[e.createElementVNode("view",{class:"form-input picker-input"},e.toDisplayString(g.roleIndex>=0&&g.roleIndex<h.value.length?null==(o=h.value[g.roleIndex])?void 0:o.label:"请选择"),1)],40,["value","range"])]),y.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"region-selection"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"省份"),e.createElementVNode("picker",{value:g.provinceIndex,range:k.value,"range-key":"name",onChange:Q,disabled:de.value},[e.createElementVNode("view",{class:e.normalizeClass(["form-input","picker-input",{disabled:de.value}])},e.toDisplayString(g.provinceIndex>=0&&g.provinceIndex<k.value.length?null==(E=k.value[g.provinceIndex])?void 0:E.name:"请选择省份"),3)],40,["value","range","disabled"])]),_.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"城市"),e.createElementVNode("picker",{value:g.cityIndex,range:V.value,"range-key":"name",onChange:Z,disabled:me.value},[e.createElementVNode("view",{class:e.normalizeClass(["form-input","picker-input",{disabled:me.value}])},e.toDisplayString(g.cityIndex>=0&&g.cityIndex<V.value.length?null==(w=V.value[g.cityIndex])?void 0:w.name:"请选择城市"),3)],40,["value","range","disabled"])])):e.createCommentVNode("",!0),f.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"区县"),e.createElementVNode("picker",{value:g.districtIndex,range:x.value,"range-key":"name",onChange:ee,disabled:ue.value},[e.createElementVNode("view",{class:e.normalizeClass(["form-input","picker-input",{disabled:ue.value}])},e.toDisplayString(g.districtIndex>=0&&g.districtIndex<x.value.length?null==(N=x.value[g.districtIndex])?void 0:N.name:"请选择区县"),3)],40,["value","range","disabled"])])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),"super_admin"===(null==(I=P.value)?void 0:I.role)&&r.value&&"guest"!==r.value.role?(e.openBlock(),e.createElementBlock("view",{key:1,class:"module-permissions-section"},[e.createElementVNode("view",{class:"section-title"},"模块权限设置"),e.createElementVNode("view",{class:"permission-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(M.value,t=>(e.openBlock(),e.createElementBlock("view",{key:t.key,class:"permission-item"},[e.createElementVNode("view",{class:"permission-info"},[e.createElementVNode("text",{class:"permission-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"permission-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("switch",{checked:A[t.key],onChange:e=>((e,t)=>{const a=t.detail.value;A[e]=a})(t.key,e),class:"permission-switch"},null,40,["checked","onChange"])]))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"popup-actions"},[e.createElementVNode("button",{onClick:J,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:te,class:"confirm-btn"},"保存")])])])):e.createCommentVNode("",!0),m.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"popup-overlay",onClick:ce},[e.createElementVNode("view",{class:"guest-search-popup",onClick:a[4]||(a[4]=e.withModifiers(()=>{},["stop"]))},[e.createElementVNode("view",{class:"popup-header"},[e.createElementVNode("text",{class:"popup-title"},"搜索游客用户"),e.createElementVNode("text",{onClick:ce,class:"close-btn"},"×")]),e.createElementVNode("view",{class:"search-form"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"手机号（完整准确）"),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>C.phone=e),class:"form-input",placeholder:"请输入完整的手机号",type:"tel",maxlength:"11"},null,512),[[e.vModelText,C.phone]])]),e.createElementVNode("view",{class:"search-actions"},[e.createElementVNode("button",{onClick:ce,class:"cancel-btn"},"取消"),e.createElementVNode("button",{onClick:re,class:"confirm-btn"},"搜索")])]),B.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"search-results"},[e.createElementVNode("view",{class:"results-header"},[e.createElementVNode("text",null,"搜索结果 ("+e.toDisplayString(b.value)+"条)",1)]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(B.value,t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"guest-item"},[e.createElementVNode("view",{class:"guest-avatar"},[t.avatar_url?(e.openBlock(),e.createElementBlock("image",{key:0,src:t.avatar_url,class:"avatar-img"},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"avatar-placeholder"},[e.createElementVNode("text",{class:"avatar-text"},e.toDisplayString(G(t)),1)]))]),e.createElementVNode("view",{class:"guest-info"},[e.createElementVNode("view",{class:"guest-name"},e.toDisplayString(t.nickname||"未设置昵称"),1),t.phone?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guest-phone"},e.toDisplayString(t.phone),1)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"guest-meta"}," ID: "+e.toDisplayString(t.id)+" | "+e.toDisplayString(H(t.created_at)),1)]),e.createElementVNode("view",{class:"guest-actions"},[e.createElementVNode("button",{onClick:e=>(async e=>{ce(),await X(e)})(t),class:"auth-btn"}," 授权 ",8,["onClick"])])]))),128)),S.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"load-more",onClick:se},[e.createElementVNode("text",null,e.toDisplayString(D.value?"加载中...":"加载更多"),1)])):e.createCommentVNode("",!0)])):T.value&&!D.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-search"},[e.createElementVNode("text",null,"未找到符合条件的游客用户")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0)])}}}),[["__scopeId","data-v-751612a8"]]);__definePage("pages/index/index",M),__definePage("pages/zhiyin/index",A),__definePage("pages/user/index",z),__definePage("pages/location/select",G),__definePage("pages/webview/index",H),__definePage("pages/culture/history",J),__definePage("pages/culture/chongqing",K),__definePage("pages/culture/heritage",te),__definePage("pages/culture/place-edit",ie),__definePage("pages/culture/timeline-edit",oe),__definePage("pages/culture/heritage-edit",ce),__definePage("pages/culture/heritage-detail",re),__definePage("pages/culture/heritage-content",se),__definePage("pages/culture/memory-edit",de),__definePage("pages/culture/memory-detail",me),__definePage("pages/culture/memory-content",ue),__definePage("pages/culture/index",ve),__definePage("pages/rail/cq-line2",ye),__definePage("pages/admin/users",ke);const Ve=e.defineComponent({__name:"App",setup:e=>(n(e=>{t("log","at App.vue:7","App Launch",e)}),i(e=>{t("log","at App.vue:16","App Show",e)}),l(()=>{t("log","at App.vue:21","App Hide")}),o(e=>{t("error","at App.vue:26","App Error:",e)}),c(e=>{t("error","at App.vue:31","Page Not Found:",e),uni.switchTab({url:"/pages/index/index"})}),()=>{})});const{app:xe,Vuex:Ce,Pinia:Be}={app:e.createVueApp(Ve),globalData:{}};uni.Vuex=Ce,uni.Pinia=Be,xe.provide("__globalStyles",__uniConfig.styles),xe._component.mpType="app",xe._component.render=()=>{},xe.mount("#app")}(Vue);
