# 千载·今知 (Millennia Now)

> 文化传承小程序 - 从APK反编译重构的完整源代码

## 项目简介

千载·今知是一个文化传承主题的uni-app应用，通过现代化的交互方式展示传统文化内容。本项目通过APK反编译技术，完整还原了应用的源代码结构。

## 功能特色

### 🚇 重庆轨道2号线文化之旅
- **8个特色站点**: 较场口、临江门、黄花园、大溪沟、曾家岩、牛角沱、李子坝、佛图关
- **网红景点**: 轻轨穿楼（李子坝）、开往春天的地铁（佛图关）
- **文化展示**: 每个站点的历史文化背景和周边景点介绍
- **交互体验**: 站点详情弹窗、图片预览、线路选择器

### 🏛️ 文化遗产
- 历史文化内容管理
- 文化遗产详情展示
- 文化记忆编辑功能
- 时间线编辑器

### 🎵 知音阁
- 文化音频内容
- 传统文化知识分享

### 👤 用户中心
- 个人信息管理
- 用户偏好设置

## 技术栈

- **框架**: uni-app (Vue 3 + TypeScript)
- **构建工具**: Vite
- **样式**: SCSS
- **状态管理**: Pinia
- **开发语言**: TypeScript

## 项目结构

```
data/new_source/
├── pages/                  # 页面文件
│   ├── index/             # 首页
│   ├── rail/              # 轨道交通
│   │   └── cq-line2.vue   # 重庆2号线
│   ├── culture/           # 文化模块
│   ├── zhiyin/            # 知音阁
│   ├── user/              # 用户中心
│   ├── location/          # 位置选择
│   ├── webview/           # 网页视图
│   └── admin/             # 管理功能
├── static/                # 静态资源
│   ├── icons/             # 图标文件
│   └── images/            # 图片资源
├── components/            # 组件库
├── utils/                 # 工具函数
├── api/                   # API接口
├── store/                 # 状态管理
├── types/                 # 类型定义
├── App.vue               # 应用入口
├── main.ts               # 主入口文件
├── pages.json            # 页面配置
├── manifest.json         # 应用配置
└── uni.scss              # 全局样式
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# H5开发
npm run dev:h5

# APP开发
npm run dev:app

# 微信小程序开发
npm run dev:mp-weixin
```

### 构建打包
```bash
# H5构建
npm run build:h5

# APP构建
npm run build:app

# 微信小程序构建
npm run build:mp-weixin
```

## 重要说明

本项目通过APK反编译技术重构，包含以下特点：

1. **完整还原**: 保持了原应用的所有功能和页面结构
2. **现代化重构**: 使用TypeScript和Vue 3重写了所有组件
3. **标准化结构**: 遵循uni-app标准项目结构
4. **可维护性**: 添加了完整的类型定义和文档

## 许可证

本项目仅用于学习和研究目的。

## 更新日志

### v1.0.0 (2025-01-30)
- 🎉 完成APK反编译和源代码重构
- ✨ 重庆2号线功能完整还原
- 🔧 添加TypeScript支持
- 📦 配置完整的开发环境
