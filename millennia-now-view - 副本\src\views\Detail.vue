<template>
  <div class="detail-container">
    <div class="detail-header">
      <!-- 使用小程序返回按钮组件 -->
      <MiniappBackButton />
      
      <!-- 普通返回按钮（当不是从小程序进入时显示） -->
      <div class="back-button" @click="goBack" v-if="!isFromMiniapp">
        <span class="icon">←</span>
        <span class="text">返回</span>
      </div>
      
      <h1 class="title">{{ exhibit?.title || '展品详情' }}</h1>
    </div>
    
    <div class="loading-indicator" v-if="loading">
      <div class="spinner"></div>
      <div class="text">加载中...</div>
    </div>
    
    <div class="detail-content" v-else>
      <div class="main-section">
        <div class="exhibit-image">
          <img :src="exhibit?.image" alt="展品图片" v-if="exhibit?.image">
          <div class="no-image" v-else>暂无图片</div>
        </div>
        
        <div class="exhibit-info">
          <h2 class="subtitle">{{ exhibit?.title }}</h2>
          
          <div class="metadata">
            <div class="metadata-item">
              <span class="label">年代:</span>
              <span class="value">{{ exhibit?.year || '未知' }}</span>
            </div>
            <div class="metadata-item">
              <span class="label">类型:</span>
              <span class="value">{{ exhibit?.type || '未知' }}</span>
            </div>
            <div class="metadata-item">
              <span class="label">来源:</span>
              <span class="value">{{ exhibit?.source || '未知' }}</span>
            </div>
          </div>
          
          <div class="description">
            <h3>描述</h3>
            <p>{{ exhibit?.description || '暂无描述' }}</p>
          </div>
          
          <div class="additional-info" v-if="exhibit?.additionalInfo">
            <h3>更多信息</h3>
            <p>{{ exhibit.additionalInfo }}</p>
          </div>
        </div>
      </div>
      
      <div class="related-section" v-if="relatedExhibits.length > 0">
        <h3 class="section-title">相关展品</h3>
        <div class="related-items">
          <div 
            v-for="item in relatedExhibits" 
            :key="item.id" 
            class="related-item"
            @click="viewRelatedItem(item.id)"
          >
            <div class="item-image">
              <img :src="item.image" alt="相关展品图片">
            </div>
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
      
      <div class="actions">
        <button class="action-btn primary" @click="goToGallery">
          返回展厅
        </button>
        <button class="action-btn secondary" @click="goToHome">
          返回首页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getHeritageItemDetail } from '@/api/heritage';
import MiniappBackButton from '../components/MiniappBackButton.vue';

const route = useRoute();
const router = useRouter();

// 小程序环境标记
const isFromMiniapp = ref(false);

// 展品数据
const exhibit = ref<any>(null);
const relatedExhibits = ref<any[]>([]);
const loading = ref(true);

// 检查是否从小程序进入
const checkFromMiniapp = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const fromParam = urlParams.get('from') === 'miniapp';
  const fromStorage = localStorage.getItem('fromMiniapp') === 'true';
  
  isFromMiniapp.value = fromParam || fromStorage;
};

// 获取展品详情
const fetchExhibitDetail = async () => {
  const id = route.params.id;
  if (!id) {
    router.push('/gallery');
    return;
  }
  
    loading.value = true;
  try {
    const data = await getHeritageItemDetail(parseInt(id as string, 10));
    exhibit.value = data;
    
    // 获取相关展品（这里简单模拟一些相关展品）
    relatedExhibits.value = Array(3).fill(null).map((_, index) => ({
      id: parseInt(id as string, 10) + index + 1,
      title: `相关展品 ${index + 1}`,
      image: `https://picsum.photos/seed/${parseInt(id as string, 10) + 300 + index}/400/300`
    }));
  } catch (error) {
    console.error('获取展品详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 查看相关展品
const viewRelatedItem = (id: number | string) => {
  router.push(`/detail/${id}`);
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 返回展厅
const goToGallery = () => {
  router.push('/gallery');
};

// 返回首页
const goToHome = () => {
  router.push('/');
};

// 组件挂载时
onMounted(() => {
  checkFromMiniapp();
  fetchExhibitDetail();
});
</script>

<style scoped lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 50px;
}

.detail-header {
  background-color: var(--primary-color);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  position: relative;
  
  .back-button {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 20px;
    
    .icon {
      font-size: 1.5rem;
      margin-right: 5px;
    }
    
    .text {
      font-size: 1rem;
    }
  }
  
  .title {
    font-size: 1.8rem;
    margin: 0;
    flex-grow: 1;
    text-align: center;
    font-weight: bold;
  }
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(200, 22, 30, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  .text {
    font-size: 1.2rem;
    color: var(--primary-color);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-bottom: 40px;
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  
  @media (min-width: 768px) {
    flex-direction: row;
  }
}

.exhibit-image {
  flex: 1;
  
  img {
    width: 100%;
    max-height: 500px;
    object-fit: contain;
    border-radius: 5px;
  }
  
  .no-image {
    width: 100%;
    height: 300px;
    background-color: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #999;
    border-radius: 5px;
  }
}

.exhibit-info {
  flex: 1;
  
  .subtitle {
    font-size: 2rem;
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--secondary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
  }
  
  .metadata {
    margin-bottom: 30px;
    
    .metadata-item {
      margin-bottom: 10px;
      display: flex;
      
      .label {
        font-weight: bold;
        width: 80px;
      }
      
      .value {
        flex: 1;
      }
    }
  }
  
  .description, .additional-info {
    margin-bottom: 20px;
    
    h3 {
      font-size: 1.3rem;
      margin-bottom: 10px;
      color: var(--secondary-color);
    }
    
    p {
      line-height: 1.6;
      white-space: pre-line;
    }
  }
}

.related-section {
  margin-bottom: 40px;
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--secondary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
  }
  
  .related-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .related-item {
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }
    
    .item-image {
      height: 150px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .item-title {
      padding: 10px;
      font-size: 1rem;
      text-align: center;
    }
  }
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  
  .action-btn {
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.primary {
      background-color: var(--primary-color);
      color: white;
      
      &:hover {
        background-color: darken(#C8161E, 10%);
      }
    }
    
    &.secondary {
      background-color: var(--secondary-color);
      color: white;
      
      &:hover {
        background-color: lighten(#333, 10%);
      }
    }
  }
}

@media (max-width: 768px) {
  .detail-header {
    padding: 15px;
    
    .title {
      font-size: 1.5rem;
    }
  }
  
  .main-section {
    padding: 20px;
  }
  
  .exhibit-info {
    .subtitle {
      font-size: 1.5rem;
    }
  }
  
  .actions {
    flex-direction: column;
    
    .action-btn {
      width: 100%;
    }
  }
}
</style> 