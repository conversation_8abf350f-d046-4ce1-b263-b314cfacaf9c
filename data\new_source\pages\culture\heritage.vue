<template>
  <view class="heritage-page">
    <!-- 页面内容将从app-service.js中提取 -->
    <view class="page-container">
      <text>页面: pages/culture/heritage</text>
      <text>分类: culture</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 页面逻辑将从app-service.js中提取
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({})

// 页面方法
onMounted(() => {
  console.log('页面加载: pages/culture/heritage')
})
</script>

<style scoped>
.container{background-color:#f8f8f8;position:relative;min-height:100vh}.region-selector{top:2.75rem;left:0;right:0;height:2.25rem;background-color:#f8f9fa;border-bottom:1px solid #eee;z-index:998;display:flex;align-items:center;justify-content:space-between;padding:0 .9375rem}.region-info{flex:1}.region-actions{flex-shrink:0}.current-region{font-size:.8125rem;color:#333}.change-region,.region-btn{font-size:.75rem;color:#007aff}.no-data-tip{position:fixed;top:5rem;left:0;right:0;bottom:0;background-color:#f8f9fa;display:flex;justify-content:center;align-items:center;z-index:1}.tip-content{text-align:center;padding:1.875rem 1.25rem}.tip-icon{font-size:3.75rem;display:block;margin-bottom:.9375rem}.tip-title{font-size:1.125rem;font-weight:700;color:#333;display:block;margin-bottom:.625rem}.tip-desc{font-size:.875rem;color:#666;display:block;margin-bottom:1.25rem}.tip-actions{text-align:center}.create-btn{background-color:#007aff;color:#fff;padding:.625rem 1.25rem;border-radius:1.25rem;font-size:.875rem;display:inline-block}.empty-tip{text-align:center;padding:1.875rem .625rem;color:#999}.empty-text{font-size:.875rem;line-height:1.6}.manage-overlay{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.3);display:flex;flex-direction:column;justify-content:center;align-items:center;color:#fff}.edit-icon{font-size:1.5rem;margin-bottom:.3125rem}.edit-text{font-size:.875rem}.header-section{position:relative;height:12.5rem;overflow:hidden;animation:fadeIn 1.5s ease-out}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.header-bg{width:100%;height:100%;transform:scale(1.1);animation:zoomOut 2s ease-out forwards}@keyframes zoomOut{0%{transform:scale(1.1);filter:brightness(.8) saturate(1.2)}to{transform:scale(1);filter:brightness(1) saturate(1)}}.header-content{position:absolute;bottom:1.25rem;left:1.25rem;z-index:2;opacity:0;transform:translateY(20px);animation:slideUp 1.5s ease-out forwards;animation-delay:.5s}@keyframes slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.place-name{font-size:1.875rem;color:#fff;font-weight:700;text-shadow:0 .0625rem .125rem rgba(0,0,0,.5);display:block;margin-bottom:.3125rem}.place-desc{font-size:1rem;color:#fff;text-shadow:0 .0625rem .125rem rgba(0,0,0,.5)}.intro-section{background-color:#fff;padding:1.25rem .9375rem;margin:.625rem;border-radius:.375rem;opacity:0;transform:translateY(20px);animation:fadeInUp 1.5s ease-out forwards;animation-delay:.8s;position:relative;overflow:hidden}.intro-section:before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at top right,rgba(0,0,0,.03),rgba(255,255,255,0) 70%);opacity:0;animation:inkFade 2s ease-out forwards;animation-delay:1.2s}@keyframes inkFade{0%{opacity:0}50%{opacity:1}to{opacity:.5}}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.intro-text{font-size:.9375rem;color:#333;line-height:1.8;text-align:justify;position:relative;z-index:1}.timeline-section{padding:.9375rem .625rem;opacity:0;animation:fadeIn 1.5s ease-out forwards;animation-delay:1s}.timeline-title,.section-title{font-size:1.125rem;font-weight:700;color:#333;text-align:center;margin-bottom:1.25rem;position:relative}.timeline-title:after,.section-title:after{content:"";position:absolute;bottom:-.3125rem;left:50%;width:0;height:.1875rem;background-color:#c8161e;border-radius:.09375rem;transform:translate(-50%);animation:lineExpand 1.5s ease-out forwards;animation-delay:1.5s}@keyframes lineExpand{0%{width:0}to{width:2.5rem}}.timeline-title:before{content:"";position:absolute;bottom:-.625rem;left:50%;width:0;height:0;background:radial-gradient(circle,rgba(200,22,30,.7),rgba(200,22,30,0) 70%);border-radius:50%;transform:translate(-50%);opacity:0;z-index:0;animation:inkDrop 2s ease-out forwards;animation-delay:1.2s}@keyframes inkDrop{0%{width:0;height:0;opacity:0}50%{opacity:.7}to{width:3.125rem;height:3.125rem;opacity:0}}.timeline-title,.section-title{display:flex;justify-content:center;align-items:center;position:relative}.add-btn{position:absolute;right:0;top:50%;transform:translateY(-50%);display:flex;align-items:center;background-color:#007aff;color:#fff;padding:.3125rem .625rem;border-radius:.625rem;font-size:.75rem}.add-icon{font-size:.875rem;margin-right:.25rem}.add-text{font-size:.75rem}.icon-add-btn{position:absolute;right:0;top:50%;transform:translateY(-50%);display:flex;align-items:center;justify-content:center;width:1.875rem;height:1.875rem;background-color:#007aff;border-radius:50%;box-shadow:0 .125rem .375rem rgba(0,122,255,.3)}.add-btn-icon{width:1rem;height:1rem;filter:brightness(0) invert(1)}.manage-actions{display:flex;gap:.3125rem}.manage-actions.vertical{flex-direction:column;position:absolute;right:.625rem;top:50%;transform:translateY(-50%)}.manage-actions.card-actions{flex-direction:column;position:absolute;right:.625rem;top:.625rem;gap:.25rem}.manage-actions.timeline-actions{flex-direction:row;position:absolute;right:0;top:0;gap:.25rem}.manage-actions.overlay{position:absolute;top:.3125rem;right:.3125rem;background-color:rgba(0,0,0,.6);border-radius:.25rem;padding:.25rem;gap:.25rem}.action-btn{padding:.25rem .5rem;border-radius:.1875rem;font-size:.75rem;text-align:center;min-width:1.875rem}.action-btn.edit{background-color:#007aff;color:#fff}.action-btn.delete{background-color:#ff3b30;color:#fff}.icon-btn{display:flex;align-items:center;justify-content:center;width:1.5rem;height:1.5rem;border-radius:50%;box-shadow:0 .0625rem .25rem rgba(0,0,0,.15)}.icon-btn.edit-btn{background-color:#007aff}.icon-btn.delete-btn{background-color:#ff3b30}.btn-icon{width:.75rem;height:.75rem;filter:brightness(0) invert(1)}.timeline{padding:.625rem 0 .625rem .625rem}.timeline-item{display:flex;margin-bottom:.9375rem;opacity:0;transform:translate(-50px);animation:slideIn .8s forwards;animation-fill-mode:both}@keyframes slideIn{0%{opacity:0;transform:translate(-50px)}to{opacity:1;transform:translate(0)}}.time-marker{display:flex;flex-direction:column;align-items:center;margin-right:.9375rem;width:.9375rem}.time-dot{width:.9375rem;height:.9375rem;border-radius:50%;background-color:#c8161e;margin-top:.9375rem;z-index:2;position:relative;box-shadow:0 0 rgba(200,22,30,.4);animation:pulse 2s infinite}@keyframes pulse{0%{box-shadow:0 0 rgba(200,22,30,.4)}70%{box-shadow:0 0 0 10px rgba(200,22,30,0)}to{box-shadow:0 0 rgba(200,22,30,0)}}.time-line{width:.125rem;background-color:#e0e0e0;flex:1;margin-top:.3125rem;position:relative;overflow:hidden}.time-line:after{content:"";position:absolute;top:0;left:0;width:100%;height:0%;background-color:#c8161e;animation:lineGrow 2s ease-out forwards}@keyframes lineGrow{0%{height:0%}to{height:100%}}.time-card{flex:1;background-color:#fff;border-radius:.375rem;padding:.46875rem;box-shadow:0 .125rem .375rem rgba(0,0,0,.1);position:relative;overflow:hidden}.time-card:before{content:"";position:absolute;top:50%;left:50%;width:0;height:0;background:radial-gradient(circle,rgba(0,0,0,.05),rgba(255,255,255,0) 70%);border-radius:50%;transform:translate(-50%,-50%);opacity:0;z-index:0;animation:inkSpread 1.5s ease-out forwards}@keyframes inkSpread{0%{width:0;height:0;opacity:0}50%{opacity:.8}to{width:300%;height:300%;opacity:0}}.time-period{display:flex;justify-content:space-between;align-items:center;margin-bottom:.625rem;position:relative;z-index:1}.period-name{font-size:1.125rem;font-weight:700;color:#333}.period-year{font-size:.875rem;color:#999}.card-content{display:flex;margin-bottom:.625rem}.card-image{width:6.25rem;height:4.6875rem;border-radius:.25rem;margin-right:.625rem}.card-text{flex:1}.card-title{font-size:1rem;font-weight:700;color:#333;margin-bottom:.3125rem;display:block}.card-desc{font-size:.875rem;color:#666;line-height:1.6}.heritage-tags{display:flex;flex-wrap:wrap;margin-top:.625rem}.tag{background-color:#f0f5ff;color:#3370ff;font-size:.75rem;padding:.1875rem .5rem;border-radius:.9375rem;margin-right:.5rem;margin-bottom:.5rem}.expand-section{display:flex;justify-content:center;align-items:center;margin-top:.625rem;padding:.3125rem 0;border-top:1px dashed #efefef;cursor:pointer;position:relative;z-index:10;background-color:transparent}.expand-section:hover{background-color:rgba(51,112,255,.05)}.expand-section:active{background-color:rgba(51,112,255,.1)}.expand-text{font-size:.8125rem;color:#3370ff;margin-right:.3125rem}.expand-icon{font-size:.625rem;color:#3370ff}.detail-content{margin-top:.625rem;padding-top:.625rem;border-top:1px dashed #efefef}.detail-content uni-rich-text{font-size:.875rem;color:#666;line-height:1.8}.detail-images{display:flex;flex-wrap:wrap;margin-top:.625rem}.detail-image{width:6.25rem;height:4.6875rem;border-radius:.25rem;margin-right:.625rem;margin-bottom:.625rem}.modern-section,.memory-section{padding:1.25rem .625rem}.heritage-list{padding:0 .625rem}.heritage-item{display:flex;background-color:#fff;border-radius:.375rem;padding:.9375rem;margin-bottom:.9375rem;box-shadow:0 .125rem .375rem rgba(0,0,0,.05);position:relative}.heritage-image{width:5.625rem;height:5.625rem;border-radius:.25rem;margin-right:.9375rem}.heritage-info{flex:1}.heritage-title{font-size:1rem;font-weight:700;color:#333;margin-bottom:.3125rem;display:block}.heritage-type{font-size:.75rem;color:#c8161e;background-color:#fff0f0;padding:.125rem .375rem;border-radius:.125rem;display:inline-block;margin-bottom:.5rem}.heritage-brief{font-size:.875rem;color:#666;line-height:1.6}.add-heritage-card{display:flex;align-items:center;justify-content:center;background-color:#f8f9fa;border:.0625rem dashed #007aff;border-radius:.375rem;padding:1.25rem .625rem;margin-bottom:.9375rem;min-height:6.25rem}.add-content{display:flex;flex-direction:column;align-items:center;justify-content:center;color:#007aff}.add-icon-svg{width:1.5rem;height:1.5rem;margin-bottom:.5rem;filter:brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1028%) hue-rotate(204deg) brightness(95%) contrast(101%)}.add-card-text{font-size:.875rem;color:#007aff;font-weight:500}.memory-scroll{white-space:nowrap;padding:.625rem}.memory-item{display:inline-block;width:8.75rem;margin-right:.9375rem;background-color:#fff;border-radius:.375rem;overflow:hidden;box-shadow:0 .125rem .375rem rgba(0,0,0,.05);position:relative}.memory-image{width:8.75rem;height:6.25rem}.memory-title{font-size:.875rem;font-weight:700;color:#333;padding:.5rem .625rem .1875rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.memory-year{font-size:.75rem;color:#999;padding:0 .625rem .5rem}.footer{text-align:center;padding:1.875rem 0}.footer-text{font-size:.875rem;color:#999}.popup-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);display:flex;justify-content:center;align-items:center;z-index:1000}.region-select-popup{background-color:#fff;border-radius:.625rem;width:80%;max-width:18.75rem;max-height:80vh;overflow:hidden}.popup-header{display:flex;justify-content:space-between;align-items:center;padding:.9375rem;border-bottom:1px solid #eee}.popup-title{font-size:1rem;font-weight:700;color:#333}.close-btn{font-size:1.25rem;color:#999;line-height:1;width:1.875rem;height:1.875rem;display:flex;justify-content:center;align-items:center;border-radius:50%}.close-btn:active{background-color:#f5f5f5}.region-form{padding:.9375rem;max-height:50vh;overflow-y:auto}.form-item{margin-bottom:.9375rem}.form-label{display:block;font-size:.875rem;color:#333;margin-bottom:.46875rem}.form-input{width:100%;padding:.625rem;border:1px solid #ddd;border-radius:.25rem;font-size:.875rem;color:#333;background-color:#fff}.picker-input{background-color:#f8f9fa;color:#333;display:flex;justify-content:space-between;align-items:center}.picker-arrow{color:#999;font-size:.75rem}.return-btn{width:100%;padding:.625rem;background-color:#6c757d;color:#fff;border:none;border-radius:.25rem;font-size:.875rem}.popup-actions{display:flex;border-top:1px solid #eee}.cancel-btn,.confirm-btn{flex:1;padding:.9375rem;border:none;font-size:.875rem;text-align:center}.cancel-btn{background-color:#f8f9fa;color:#6c757d}.confirm-btn{background-color:#007aff;color:#fff}.current-selection{margin-top:.625rem;padding:.46875rem;background-color:#f0f9ff;border-radius:.25rem;border:1px solid #d0e6ff}.selection-text{font-size:.8125rem;color:#007aff}.loading-container{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.7);display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:999}.loading-spinner{width:1.875rem;height:1.875rem;border:.1875rem solid #f3f3f3;border-top:.1875rem solid #c8161e;border-radius:50%;animation:spin 1s linear infinite;margin-bottom:.625rem}.loading-text{font-size:.875rem;color:#666}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.picker-wrapper{position:relative;width:100%}.picker-backup-btn{display:none}

</style>
